"use client";


import { Phone } from "lucide-react";
import { motion } from "framer-motion";

interface LinkPhoneSectionProps {
  currentEmail?: string | null;
  currentPhone?: string | null;
  registrationType: 'google' | 'email' | 'phone';
}

export default function LinkPhoneSection({
  currentPhone,
}: LinkPhoneSectionProps) {

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.2 }}
      className="space-y-6"
    >
      {/* Section Header */}
      <div className="pb-6 border-b border-neutral-200/60 dark:border-neutral-700/60">
        <div className="flex items-center gap-3 mb-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-green-500/10 to-green-500/5 border border-green-500/20">
            <Phone className="w-4 h-4 text-green-500" />
          </div>
          <h2 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
            Phone Number
          </h2>
        </div>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed">
          {currentPhone
            ? "Your current phone number linked to this account."
            : "No phone number is currently linked to your account."
          }
        </p>
      </div>

      {/* Section Content */}
      <div className="space-y-6">
          {currentPhone ? (
            // Show current phone number (read-only)
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  Current Phone Number
                </label>
                <div className="mt-1 p-3 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md">
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">{currentPhone}</span>
                </div>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                  Phone number changes are not currently supported. Contact support if you need to update your number.
                </p>
              </div>
            </div>
          ) : (
            // No phone number linked
            <div className="text-center p-6 rounded-lg bg-neutral-50 dark:bg-neutral-900/50 border border-neutral-200 dark:border-neutral-700">
              <div className="p-3 rounded-full bg-neutral-200 dark:bg-neutral-700 text-neutral-500 dark:text-neutral-400 mx-auto mb-3 w-fit">
                <Phone className="w-6 h-6" />
              </div>
              <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-2">
                No Phone Number
              </h3>
              <p className="text-sm text-neutral-600 dark:text-neutral-400 max-w-sm mx-auto">
                No phone number is currently linked to your account. Phone number linking is not available at this time.
              </p>
            </div>
          )}
      </div>
    </motion.div>
  );
}
