/**
 * Unified Social Service for Next.js
 * Handles subscriptions, likes, and reviews data fetching and management
 * Supports both customer and business contexts with identical backend functionality to React Native
 */

import { createClient } from '@/utils/supabase/server';
import { Database } from '@/types/supabase';
import { TABLES, COLUMNS } from '@/lib/supabase/constants';
import { getSecureCustomerProfilesByIds } from '@/lib/actions/secureCustomerProfiles';

export interface ActivityMetrics {
  likesCount: number;
  reviewCount: number;
  subscriptionCount: number;
  lastUpdated: string;
}

// Types for subscriptions
export interface SubscriptionWithProfile {
  id: string;
  business_profiles: Database['public']['Tables']['business_profiles']['Row'] | null;
}

export interface SubscriptionsResult {
  items: SubscriptionWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

// Types for likes
export interface LikeWithProfile {
  id: string;
  business_profiles: Database['public']['Tables']['business_profiles']['Row'] | null;
}

export interface LikesResult {
  items: LikeWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

// Types for reviews
export interface ReviewBusinessProfile {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

export interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: ReviewBusinessProfile | null;
}

export interface ReviewsResult {
  items: ReviewData[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

// Business-specific types for received interactions
export interface BusinessLikeReceived {
  id: string;
  user_id: string;
  customer_profiles?: {
    id: string;
    name: string | null;
    email: string | null;
    avatar_url: string | null;
  } | null;
  business_profiles?: {
    id: string;
    business_name: string | null;
    business_slug: string | null;
    logo_url: string | null;
    city: string | null;
    state: string | null;
    pincode: string | null;
    address_line: string | null;
    locality: string | null;
  } | null;
  profile_type: 'customer' | 'business';
}

export interface BusinessLikesReceivedResult {
  items: BusinessLikeReceived[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

// Follower types for business
export interface FollowerProfileData {
  id: string;
  name: string | null;
  slug: string | null;
  logo_url?: string | null;
  avatar_url?: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
  type: "business" | "customer";
}

export interface FollowerWithProfile {
  id: string;
  profile: FollowerProfileData | null;
}

export interface FollowersResult {
  items: FollowerWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Subscriptions Service
 */
export const subscriptionsService = {
  /**
   * Fetch user subscriptions with pagination and search
   */
  async fetchSubscriptions(
    userId: string,
    page: number = 1,
    limit: number = 20,
    searchTerm: string = ""
  ): Promise<SubscriptionsResult> {
    try {
      const supabase = await createClient();

      // Get total count first with separate query to avoid count issues with joins
      let countQuery = supabase
        .from(TABLES.SUBSCRIPTIONS)
        .select(
          `
          ${COLUMNS.ID},
          ${TABLES.BUSINESS_PROFILES}!inner (
            ${COLUMNS.ID},
            ${COLUMNS.BUSINESS_NAME}
          )
        `,
          { count: "exact", head: true }
        )
        .eq(COLUMNS.USER_ID, userId);

      // Apply search filter to count query if provided
      if (searchTerm && searchTerm.trim()) {
        countQuery = countQuery.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      const { count: totalCount, error: countError } = await countQuery;

      if (countError) {
        throw new Error(`Failed to get subscriptions count: ${countError.message}`);
      }

      // If no subscriptions, return empty result
      if (!totalCount || totalCount === 0) {
        return {
          items: [],
          totalCount: 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Build the main query for fetching data
      let query = supabase
        .from(TABLES.SUBSCRIPTIONS)
        .select(
          `
          ${COLUMNS.ID},
          ${COLUMNS.BUSINESS_PROFILE_ID},
          ${TABLES.BUSINESS_PROFILES}!inner (*)
        `
        )
        .eq(COLUMNS.USER_ID, userId);

      // Apply search filter if provided
      if (searchTerm && searchTerm.trim()) {
        query = query.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      // Apply pagination
      const offset = (page - 1) * limit;
      
      query = query.range(offset, offset + limit - 1);

      const { data: subscriptionsWithProfiles, error } = await query;
      

      if (error) {
        throw new Error(`Failed to fetch subscriptions: ${error.message}`);
      }

      // Transform the data
      const transformedSubscriptions: SubscriptionWithProfile[] = (
        subscriptionsWithProfiles || []
      ).map(
        (sub: {
          id: string;
          business_profiles: Database['public']['Tables']['business_profiles']['Row'] | Database['public']['Tables']['business_profiles']['Row'][];
        }) => ({
          id: sub.id,
          business_profiles: Array.isArray(sub.business_profiles)
            ? sub.business_profiles[0]
            : sub.business_profiles,
        })
      );

      const hasMore = totalCount > offset + limit;

      return {
        items: transformedSubscriptions,
        totalCount,
        hasMore,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error in fetchSubscriptions:", error);
      throw error;
    }
  },

  /**
   * Unsubscribe from a business
   */
  async unsubscribe(subscriptionId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const { error } = await supabase
        .from(TABLES.SUBSCRIPTIONS)
        .delete()
        .eq(COLUMNS.ID, subscriptionId);

      if (error) {
        throw new Error(`Failed to unsubscribe: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in unsubscribe:", error);
      throw error;
    }
  },

  /**
   * Fetch followers to a business (both customers and other businesses)
   */
  async fetchBusinessFollowers(
    businessId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<FollowersResult> {
    try {
      const supabase = await createClient();
      
      // Calculate offset for pagination
      const offset = (page - 1) * limit;

      // Get total count first
      const { count: totalCount, error: countError } = await supabase
        .from(TABLES.SUBSCRIPTIONS)
        .select("*", { count: "exact", head: true })
        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);

      if (countError) {
        throw new Error("Failed to fetch subscription count");
      }

      if (!totalCount || totalCount === 0) {
        return {
          items: [],
          totalCount: 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Get paginated subscriptions (database-level pagination)
      const { data: subscriptions, error: subsError } = await supabase
        .from(TABLES.SUBSCRIPTIONS)
        .select(`${COLUMNS.ID}, ${COLUMNS.USER_ID}, ${COLUMNS.CREATED_AT}`)
        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)
        .order(COLUMNS.CREATED_AT, { ascending: false })
        .range(offset, offset + limit - 1);

      if (subsError) {
        throw new Error("Failed to fetch subscriptions");
      }

      if (!subscriptions || subscriptions.length === 0) {
        return {
          items: [],
          totalCount: totalCount || 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Get user IDs from the paginated subscriptions only
      const userIds = subscriptions.map((sub) => sub.user_id);

      // Fetch profiles for only the paginated user IDs (not all users)
      const [customerProfilesResult, businessProfiles] = await Promise.all([
        getSecureCustomerProfilesByIds(userIds),
        supabase
          .from(TABLES.BUSINESS_PROFILES)
          .select(
            `${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}`
          )
          .in(COLUMNS.ID, userIds),
      ]);

      if (customerProfilesResult.error) {
        throw new Error("Failed to fetch customer profiles");
      }

      if (businessProfiles.error) {
        throw new Error("Failed to fetch business profiles");
      }

      // Create profile maps for efficient lookup
      const customerMap = new Map(
        customerProfilesResult.data?.map((p) => [p.id, p]) || []
      );
      const businessMap = new Map(
        businessProfiles.data?.map((p) => [p.id, p]) || []
      );

      // Transform subscriptions to include profile data
      const allFollowers: FollowerWithProfile[] = subscriptions
        .map((sub) => {
          const customerProfile = customerMap.get(sub.user_id);
          const businessProfile = businessMap.get(sub.user_id);

          if (customerProfile) {
            return {
              id: sub.id,
              profile: {
                id: customerProfile.id,
                name: customerProfile.name,
                slug: null,
                avatar_url: customerProfile.avatar_url,
                city: null,
                state: null,
                pincode: null,
                address_line: null,
                type: "customer" as const,
              },
            };
          } else if (businessProfile) {
            return {
              id: sub.id,
              profile: {
                id: businessProfile.id,
                name: businessProfile.business_name,
                slug: businessProfile.business_slug,
                logo_url: businessProfile.logo_url,
                city: businessProfile.city,
                state: businessProfile.state,
                pincode: businessProfile.pincode,
                address_line: businessProfile.address_line,
                type: "business" as const,
              },
            };
          }
          return null;
        })
        .filter((sub) => sub !== null) as FollowerWithProfile[];

      // Calculate hasMore based on database-level pagination
      const hasMore = totalCount > offset + limit;

      return {
        items: allFollowers,
        totalCount: totalCount || 0,
        hasMore,
        currentPage: page,
      };
    } catch (error) {
      throw error;
    }
  },
};

/**
 * Likes Service
 */
export const likesService = {
  /**
   * Fetch user likes with pagination and search
   */
  async fetchLikes(
    userId: string,
    page: number = 1,
    limit: number = 20,
    searchTerm: string = ""
  ): Promise<LikesResult> {
    try {
      const supabase = await createClient();

      // Build query with proper joins and filtering
      let query = supabase
        .from(TABLES.LIKES)
        .select(
          `
          ${COLUMNS.ID},
          ${TABLES.BUSINESS_PROFILES}!inner (*)
        `
        )
        .eq(COLUMNS.USER_ID, userId);

      // Apply search filter if provided
      if (searchTerm && searchTerm.trim()) {
        query = query.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      // Get total count for pagination
      let countQuery = supabase
        .from(TABLES.LIKES)
        .select(
          `
          ${COLUMNS.ID},
          ${TABLES.BUSINESS_PROFILES}!inner (
            ${COLUMNS.ID},
            ${COLUMNS.BUSINESS_NAME}
          )
        `,
          { count: "exact", head: true }
        )
        .eq(COLUMNS.USER_ID, userId);

      if (searchTerm && searchTerm.trim()) {
        countQuery = countQuery.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      const { count: totalCount, error: countError } = await countQuery;

      if (countError) {
        throw new Error(`Failed to get likes count: ${countError.message}`);
      }

      // If no likes, return empty result
      if (!totalCount || totalCount === 0) {
        return {
          items: [],
          totalCount: 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Apply pagination to the main query
      const offset = (page - 1) * limit;
      
      query = query.range(offset, offset + limit - 1);

      const { data: likesWithProfiles, error } = await query;
      

      if (error) {
        throw new Error(`Failed to fetch likes: ${error.message}`);
      }

      // Transform the data
      const transformedLikes: LikeWithProfile[] = (likesWithProfiles || []).map(
        (like: {
          id: string;
          business_profiles: Database['public']['Tables']['business_profiles']['Row'] | Database['public']['Tables']['business_profiles']['Row'][];
        }) => ({
          id: like.id,
          business_profiles: Array.isArray(like.business_profiles)
            ? like.business_profiles[0]
            : like.business_profiles,
        })
      );

      const hasMore = totalCount > offset + limit;

      return {
        items: transformedLikes,
        totalCount,
        hasMore,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error in fetchLikes:", error);
      throw error;
    }
  },

  /**
   * Unlike a business
   */
  async unlike(likeId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const { error } = await supabase.from(TABLES.LIKES).delete().eq(COLUMNS.ID, likeId);

      if (error) {
        throw new Error(`Failed to unlike: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in unlike:", error);
      throw error;
    }
  },

  /**
   * Fetch likes received by a business (customers/businesses who liked this business)
   */
  async fetchBusinessLikesReceived(
    businessId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<BusinessLikesReceivedResult> {
    try {
      const supabase = await createClient();

      // Get total count first
      const { count: totalCount, error: countError } = await supabase
        .from(TABLES.LIKES)
        .select(COLUMNS.ID, { count: "exact", head: true })
        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);

      if (countError) {
        throw new Error("Failed to get total count");
      }

      if (!totalCount || totalCount === 0) {
        return {
          items: [],
          totalCount: 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Get likes with pagination (database-level pagination)
      const from = (page - 1) * limit;
      
      const { data: likes, error: likesError } = await supabase
        .from(TABLES.LIKES)
        .select(`${COLUMNS.ID}, ${COLUMNS.USER_ID}`)
        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)
        .order(COLUMNS.CREATED_AT, { ascending: false })
        .range(from, from + limit - 1);
      

      if (likesError) {
        throw new Error("Failed to fetch likes");
      }

      if (!likes || likes.length === 0) {
        return {
          items: [],
          totalCount,
          hasMore: false,
          currentPage: page,
        };
      }

      // Get user IDs for the paginated results only
      const userIds = likes.map((like) => like.user_id);

      // Fetch customer and business profiles for paginated results only
      
      const [customerProfilesResult, businessProfiles] = await Promise.all([
        getSecureCustomerProfilesByIds(userIds),
        supabase
          .from(TABLES.BUSINESS_PROFILES)
          .select(
            `${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}, ${COLUMNS.LOCALITY}`
          )
          .in(COLUMNS.ID, userIds),
      ]);

      
      

      // Create maps for easy lookup
      const customerProfilesMap = new Map(
        customerProfilesResult.data?.map((profile) => [profile.id, profile]) || []
      );
      const businessProfilesMap = new Map(
        businessProfiles.data?.map((profile) => [profile.id, profile]) || []
      );

      // Combine likes with their corresponding profiles
      
      const processedLikes = likes
        .map((like) => {
          const customerProfile = customerProfilesMap.get(like.user_id);
          const businessProfile = businessProfilesMap.get(like.user_id);

          if (customerProfile) {
            return {
              id: like.id,
              user_id: like.user_id,
              customer_profiles: customerProfile,
              profile_type: "customer" as const,
            };
          } else if (businessProfile) {
            return {
              id: like.id,
              user_id: like.user_id,
              business_profiles: businessProfile,
              profile_type: "business" as const,
            };
          }

          return null;
        })
        .filter((item) => item !== null) as BusinessLikeReceived[];

      

      const hasMore = totalCount > from + limit;

      return {
        items: processedLikes,
        totalCount,
        hasMore,
        currentPage: page,
      };
    } catch (error) {
      throw error;
    }
  },
};

/**
 * Reviews Service
 */
export const reviewsService = {
  /**
   * Fetch user reviews with pagination and sorting
   */
  async fetchReviews(
    userId: string,
    page: number = 1,
    limit: number = 20,
    sortBy: "newest" | "oldest" | "rating_high" | "rating_low" = "newest",
    searchTerm: string = ""
  ): Promise<ReviewsResult> {
    try {
      const supabase = await createClient();

      // Get total count first with separate query to avoid count issues with joins
      let countResult;

      if (searchTerm && searchTerm.trim()) {
        // For search, we need to join with business_profiles
        countResult = await supabase
          .from(TABLES.RATINGS_REVIEWS)
          .select(
            `
            ${COLUMNS.ID},
            ${TABLES.BUSINESS_PROFILES}!inner (
              ${COLUMNS.ID},
              ${COLUMNS.BUSINESS_NAME}
            )
          `,
            { count: "exact", head: true }
          )
          .eq(COLUMNS.USER_ID, userId)
          .ilike(
            `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
            `%${searchTerm.trim()}%`
          );
      } else {
        // For simple count without search, no need to join
        countResult = await supabase
          .from(TABLES.RATINGS_REVIEWS)
          .select(COLUMNS.ID, { count: "exact", head: true })
          .eq(COLUMNS.USER_ID, userId);
      }

      const { count: totalCount, error: countError } = countResult;

      if (countError) {
        throw new Error(`Failed to get reviews count: ${countError.message}`);
      }

      // If no reviews, return empty result
      if (!totalCount || totalCount === 0) {
        return {
          items: [],
          totalCount: 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Build the main query for fetching data
      let query = supabase
        .from(TABLES.RATINGS_REVIEWS)
        .select(
          `
          ${COLUMNS.ID},
          ${COLUMNS.RATING},
          ${COLUMNS.REVIEW_TEXT},
          ${COLUMNS.CREATED_AT},
          ${COLUMNS.UPDATED_AT},
          ${COLUMNS.BUSINESS_PROFILE_ID},
          ${COLUMNS.USER_ID},
          ${TABLES.BUSINESS_PROFILES}!inner (
            ${COLUMNS.ID},
            ${COLUMNS.BUSINESS_NAME},
            ${COLUMNS.BUSINESS_SLUG},
            ${COLUMNS.LOGO_URL}
          )
        `
        )
        .eq(COLUMNS.USER_ID, userId);

      // Apply search filter if provided
      if (searchTerm && searchTerm.trim()) {
        query = query.ilike(
          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
          `%${searchTerm.trim()}%`
        );
      }

      // Apply sorting
      switch (sortBy) {
        case "oldest":
          query = query.order(COLUMNS.CREATED_AT, { ascending: true });
          break;
        case "rating_high":
          query = query.order(COLUMNS.RATING, { ascending: false });
          break;
        case "rating_low":
          query = query.order(COLUMNS.RATING, { ascending: true });
          break;
        case "newest":
        default:
          query = query.order(COLUMNS.CREATED_AT, { ascending: false });
          break;
      }

      // Apply pagination
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      const { data: reviews, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch reviews: ${error.message}`);
      }

      // Transform the data (business profiles are already joined)
      const transformedReviews: ReviewData[] = (reviews || []).map((review: {
        id: string;
        rating: number;
        review_text: string | null;
        created_at: string;
        updated_at: string;
        business_profile_id: string;
        user_id: string;
        business_profiles: ReviewBusinessProfile | ReviewBusinessProfile[];
      }) => ({
        id: review.id,
        rating: review.rating,
        review_text: review.review_text,
        created_at: review.created_at,
        updated_at: review.updated_at,
        business_profile_id: review.business_profile_id,
        user_id: review.user_id,
        business_profiles: Array.isArray(review.business_profiles)
          ? review.business_profiles[0]
          : review.business_profiles,
      }));

      const hasMore = totalCount > offset + limit;

      return {
        items: transformedReviews,
        totalCount,
        hasMore,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error in fetchReviews:", error);
      throw error;
    }
  },

  /**
   * Delete a review
   */
  async deleteReview(reviewId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const { error } = await supabase
        .from(TABLES.RATINGS_REVIEWS)
        .delete()
        .eq(COLUMNS.ID, reviewId);

      if (error) {
        throw new Error(`Failed to delete review: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in deleteReview:", error);
      throw error;
    }
  },

  /**
   * Update a review
   */
  async updateReview(
    reviewId: string,
    rating: number,
    reviewText: string
  ): Promise<void> {
    try {
      const supabase = await createClient();
      const { error } = await supabase
        .from(TABLES.RATINGS_REVIEWS)
        .update({
          [COLUMNS.RATING]: rating,
          [COLUMNS.REVIEW_TEXT]: reviewText || null,
          [COLUMNS.UPDATED_AT]: new Date().toISOString(),
        })
        .eq(COLUMNS.ID, reviewId);

      if (error) {
        throw new Error(`Failed to update review: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in updateReview:", error);
      throw error;
    }
  },

  /**
   * Fetch reviews received by a business (customers/businesses who reviewed this business)
   */
  async fetchBusinessReviewsReceived(
    businessId: string,
    page: number = 1,
    limit: number = 10,
    sortBy: "newest" | "oldest" | "rating_high" | "rating_low" = "newest"
  ): Promise<{
    items: {
      id: string;
      rating: number;
      review_text: string | null;
      created_at: string;
      updated_at: string;
      business_profile_id: string;
      user_id: string;
      customer_profiles?: {
        id: string;
        name: string | null;
        email: string | null;
        avatar_url: string | null;
      } | null;
      business_profiles?: {
        id: string;
        business_name: string | null;
        business_slug: string | null;
        logo_url: string | null;
        city: string | null;
        state: string | null;
        pincode: string | null;
        address_line: string | null;
      } | null;
      profile_type: "customer" | "business";
    }[];
    totalCount: number;
    hasMore: boolean;
    currentPage: number;
  }> {
    try {
      const supabase = await createClient();

      // Get total count first
      const { count: totalCount, error: countError } = await supabase
        .from(TABLES.RATINGS_REVIEWS)
        .select(COLUMNS.ID, { count: "exact", head: true })
        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);

      if (countError) {
        throw new Error("Failed to get total count");
      }

      if (!totalCount || totalCount === 0) {
        return {
          items: [],
          totalCount: 0,
          hasMore: false,
          currentPage: page,
        };
      }

      // Get reviews with pagination and sorting
      const from = (page - 1) * limit;
      let query = supabase
        .from(TABLES.RATINGS_REVIEWS)
        .select(`${COLUMNS.ID}, ${COLUMNS.RATING}, ${COLUMNS.REVIEW_TEXT}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.BUSINESS_PROFILE_ID}, ${COLUMNS.USER_ID}`)
        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);

      // Apply sorting
      switch (sortBy) {
        case "oldest":
          query = query.order(COLUMNS.CREATED_AT, { ascending: true });
          break;
        case "rating_high":
          query = query.order(COLUMNS.RATING, { ascending: false });
          break;
        case "rating_low":
          query = query.order(COLUMNS.RATING, { ascending: true });
          break;
        case "newest":
        default:
          query = query.order(COLUMNS.CREATED_AT, { ascending: false });
          break;
      }

      query = query.range(from, from + limit - 1);

      const { data: reviews, error: reviewsError } = await query;

      if (reviewsError) {
        throw new Error("Failed to fetch reviews");
      }

      if (!reviews || reviews.length === 0) {
        return {
          items: [],
          totalCount,
          hasMore: false,
          currentPage: page,
        };
      }

      // Get user IDs for the paginated results only
      const userIds = reviews.map((review) => review.user_id);

      // Fetch customer and business profiles for paginated results only
      const [customerProfilesResult, businessProfiles] = await Promise.all([
        getSecureCustomerProfilesByIds(userIds),
        supabase
          .from(TABLES.BUSINESS_PROFILES)
          .select(
            `${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}`
          )
          .in(COLUMNS.ID, userIds),
      ]);

      // Create maps for easy lookup
      const customerProfilesMap = new Map(
        customerProfilesResult.data?.map((profile) => [profile.id, profile]) || []
      );
      const businessProfilesMap = new Map(
        businessProfiles.data?.map((profile) => [profile.id, profile]) || []
      );

      // Combine reviews with their corresponding profiles
      const processedReviews = reviews
        .map((review) => {
          const customerProfile = customerProfilesMap.get(review.user_id);
          const businessProfile = businessProfilesMap.get(review.user_id);

          if (customerProfile) {
            return {
              id: review.id,
              rating: review.rating,
              review_text: review.review_text,
              created_at: review.created_at,
              updated_at: review.updated_at,
              business_profile_id: review.business_profile_id,
              user_id: review.user_id,
              customer_profiles: customerProfile,
              profile_type: "customer" as const,
            };
          } else if (businessProfile) {
            return {
              id: review.id,
              rating: review.rating,
              review_text: review.review_text,
              created_at: review.created_at,
              updated_at: review.updated_at,
              business_profile_id: review.business_profile_id,
              user_id: review.user_id,
              business_profiles: businessProfile,
              profile_type: "business" as const,
            };
          }
          return null;
        })
        .filter((item) => item !== null);

      const hasMore = totalCount > from + limit;

      return {
        items: processedReviews,
        totalCount,
        hasMore,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error in fetchBusinessReviewsReceived:", error);
      throw error;
    }
  },
};

/**
 * Get customer activity metrics
 */
export async function getActivityMetrics(
  userId: string
): Promise<ActivityMetrics | null> {
  try {
    const supabase = await createClient();

    // Fetch fresh metrics from database
    const [likesResult, reviewsResult, subscriptionsResult] = await Promise.all(
      [
        supabase
          .from(TABLES.LIKES)
          .select(COLUMNS.ID, { count: "exact" })
          .eq(COLUMNS.USER_ID, userId),
        supabase
          .from(TABLES.RATINGS_REVIEWS)
          .select(COLUMNS.ID, { count: "exact" })
          .eq(COLUMNS.USER_ID, userId),
        supabase
          .from(TABLES.SUBSCRIPTIONS)
          .select(COLUMNS.ID, { count: "exact" })
          .eq(COLUMNS.USER_ID, userId),
      ]
    );

    const metrics: ActivityMetrics = {
      likesCount: likesResult.count || 0,
      reviewCount: reviewsResult.count || 0,
      subscriptionCount: subscriptionsResult.count || 0,
      lastUpdated: new Date().toISOString(),
    };

    return metrics;
  } catch (error) {
    console.error("Error fetching activity metrics:", error);
    return null;
  }
}

// Export a service object for compatibility with existing imports
export const socialService = {
  likesService,
  reviewsService,
  subscriptionsService,
  fetchActivityMetrics: getActivityMetrics,
};
