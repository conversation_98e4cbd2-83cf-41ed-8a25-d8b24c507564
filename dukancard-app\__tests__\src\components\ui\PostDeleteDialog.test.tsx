/**
 * Unit tests for src/components/ui/PostDeleteDialog.tsx
 * Tests the PostDeleteDialog component functionality
 */

import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import { PostDeleteDialog } from "@/src/components/ui/PostDeleteDialog";

// Mock the AlertDialog component
jest.mock("@/src/components/ui/AlertDialog", () => ({
  AlertDialog: ({ visible, title, message, buttons, onClose }: any) => {
    const { Text, View, TouchableOpacity } = require("react-native");

    if (!visible) return null;

    return (
      <View testID="alert-dialog">
        <Text>{title}</Text>
        <Text>{message}</Text>
        {buttons?.map((button: any, index: number) => (
          <TouchableOpacity
            key={index}
            testID={`alert-button-${button.text
              .toLowerCase()
              .replace(/\s+/g, "-")}`}
            onPress={button.onPress}
            disabled={button.loading}
          >
            <Text>{button.text}</Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  },
}));

describe("src/components/ui/PostDeleteDialog.tsx", () => {
  const defaultProps = {
    visible: true,
    onClose: jest.fn(),
    onConfirm: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("PostDeleteDialog component", () => {
    it("should render with default props", () => {
      const { getByTestId } = render(<PostDeleteDialog {...defaultProps} />);

      expect(getByTestId("alert-dialog")).toBeTruthy();
      expect(getByTestId("alert-button-cancel")).toBeTruthy();
      expect(getByTestId("alert-button-delete")).toBeTruthy();
    });

    it("should render with custom title and message", () => {
      const customProps = {
        ...defaultProps,
        title: "Custom Delete Title",
        message: "Custom delete message",
      };

      const { getByTestId } = render(<PostDeleteDialog {...customProps} />);

      expect(getByTestId("alert-dialog")).toBeTruthy();
      expect(getByTestId("alert-button-cancel")).toBeTruthy();
      expect(getByTestId("alert-button-delete")).toBeTruthy();
    });

    it("should not render when visible is false", () => {
      const { queryByTestId } = render(
        <PostDeleteDialog {...defaultProps} visible={false} />
      );

      expect(queryByTestId("alert-dialog")).toBeNull();
    });

    it("should call onClose when cancel button is pressed", () => {
      const { getByTestId } = render(<PostDeleteDialog {...defaultProps} />);

      const cancelButton = getByTestId("alert-button-cancel");
      fireEvent.press(cancelButton);

      expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    });

    it("should call onConfirm when delete button is pressed", () => {
      const { getByTestId } = render(<PostDeleteDialog {...defaultProps} />);

      const deleteButton = getByTestId("alert-button-delete");
      fireEvent.press(deleteButton);

      expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1);
    });

    it("should show loading state when isDeleting is true", () => {
      const { getByTestId } = render(
        <PostDeleteDialog {...defaultProps} isDeleting={true} />
      );

      expect(getByTestId("alert-button-deleting...")).toBeTruthy();
    });

    it("should disable delete button when isDeleting is true", () => {
      const { getByTestId } = render(
        <PostDeleteDialog {...defaultProps} isDeleting={true} />
      );

      const deleteButton = getByTestId("alert-button-deleting...");
      fireEvent.press(deleteButton);

      // onConfirm should still be called, but the button should be in loading state
      expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1);
    });

    it("should pass correct button configuration to AlertDialog", () => {
      const { getByTestId } = render(<PostDeleteDialog {...defaultProps} />);

      // Verify both buttons are present
      expect(getByTestId("alert-button-cancel")).toBeTruthy();
      expect(getByTestId("alert-button-delete")).toBeTruthy();
    });

    it("should pass correct button configuration when deleting", () => {
      const { getByTestId } = render(
        <PostDeleteDialog {...defaultProps} isDeleting={true} />
      );

      // Verify cancel button is still present
      expect(getByTestId("alert-button-cancel")).toBeTruthy();
      // Verify delete button shows loading text
      expect(getByTestId("alert-button-deleting...")).toBeTruthy();
    });

    it("should handle multiple rapid button presses", () => {
      const { getByTestId } = render(<PostDeleteDialog {...defaultProps} />);

      const deleteButton = getByTestId("alert-button-delete");

      // Press multiple times rapidly
      fireEvent.press(deleteButton);
      fireEvent.press(deleteButton);
      fireEvent.press(deleteButton);

      // onConfirm should be called for each press
      expect(defaultProps.onConfirm).toHaveBeenCalledTimes(3);
    });

    it("should maintain button state consistency", () => {
      const { rerender, getByTestId } = render(
        <PostDeleteDialog {...defaultProps} isDeleting={false} />
      );

      expect(getByTestId("alert-button-delete")).toBeTruthy();

      rerender(<PostDeleteDialog {...defaultProps} isDeleting={true} />);

      expect(getByTestId("alert-button-deleting...")).toBeTruthy();
    });

    it("should pass through all AlertDialog props correctly", () => {
      const { getByTestId } = render(<PostDeleteDialog {...defaultProps} />);

      const alertDialog = getByTestId("alert-dialog");
      expect(alertDialog).toBeTruthy();
    });
  });
});
