import { motion } from "framer-motion";
import { Image as ImageIcon, Upload } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface EmptyGalleryProps {
  canAddMore: boolean;
  isDragging: boolean;
  userPlan: string;
  galleryLimit: number;
  onUploadClick: () => void;
}

export default function EmptyGallery({
  canAddMore,
  isDragging,
  userPlan: _userPlan,
  galleryLimit,
  onUploadClick,
}: EmptyGalleryProps) {
  return (
    <motion.div
      className={cn(
        "text-center py-20 border-2 border-dashed rounded-2xl bg-neutral-50/50 dark:bg-neutral-900/50 transition-all duration-300",
        canAddMore && "hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50 cursor-pointer border-neutral-300 dark:border-neutral-700",
        !canAddMore && "border-neutral-200 dark:border-neutral-800",
        isDragging && canAddMore && "border-primary bg-primary/10 dark:bg-primary/5"
      )}
      initial={{ scale: 0.95, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ delay: 0.3 }}
      onClick={canAddMore ? onUploadClick : undefined}
    >
      <div className={cn(
        "w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 flex items-center justify-center mb-6 transition-all duration-300",
        isDragging && canAddMore && "bg-gradient-to-br from-primary/20 to-primary/10 scale-110 border-primary/40"
      )}>
        <ImageIcon className={cn(
          "h-8 w-8 text-primary transition-all duration-300",
          isDragging && canAddMore && "scale-110"
        )} />
      </div>
      <div className="space-y-3">
        <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
          {canAddMore ? "Start Your Gallery" : "Gallery Limit Reached"}
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400 max-w-md mx-auto leading-relaxed">
          {canAddMore ? (
            <>
              Showcase your business with beautiful photos. Upload images to create an engaging visual experience for your customers.
              <br />
              <span className="text-primary font-medium mt-2 block">
                {isDragging ? "Drop your images here" : "Click to upload or drag and drop"}
              </span>
            </>
          ) : (
            <>
              You&apos;ve reached your plan&apos;s gallery limit of {galleryLimit} photos.
              <br />
              <span className="text-primary font-medium mt-2 block">
              Upgrade your plan to add more photos.
            </span>
          </>
        )}
        </p>
        {canAddMore && (
          <Button
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              onUploadClick();
            }}
            disabled={!canAddMore}
            className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium"
            size="lg"
          >
            <Upload className="mr-2 h-4 w-4" />
            Upload Your First Photo
          </Button>
        )}
      </div>
    </motion.div>
  );
}
