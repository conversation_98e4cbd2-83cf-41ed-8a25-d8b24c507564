"use client";

import { motion } from "framer-motion";
import { Clock } from "lucide-react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { useIsMobile } from "@/hooks/use-mobile";
import { formatIndianNumberShort } from "@/lib/utils";
import PremiumFeatureLock from "./PremiumFeatureLock";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

interface HourlyVisitTrendChartProps {
  data: { hour: number; visits: number }[];
  userPlan?: string | null;
}

const chartConfig = {
  visits: {
    label: "Visits",
    color: "var(--chart-2)",
  },
} satisfies ChartConfig;

export default function HourlyVisitTrendChart({
  data,
  userPlan,
}: HourlyVisitTrendChartProps) {
  const isMobile = useIsMobile();

  // Format hour number to a readable time format (e.g., 14 -> "2 PM")
  const formatHour = (hour: number) => {
    if (hour === 0) return "12 AM";
    if (hour === 12) return "12 PM";
    return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
  };

  // Format hour for X-axis ticks - shorter on mobile
  const formatXAxisTick = (hourStr: string) => {
    if (isMobile) {
      // Extract just the hour number for mobile
      const match = hourStr.match(/(\d+)/);
      if (match) {
        return match[1];
      }
      return hourStr;
    }
    return hourStr; // Full formatted hour on desktop
  };

  // Generate all 24 hours
  const generateAllHours = () => {
    const hours = [];
    for (let i = 0; i < 24; i++) {
      hours.push(i);
    }
    return hours;
  };

  // Get all hours
  const allHours = generateAllHours();

  // Create a map of existing data
  const dataMap = new Map();
  data.forEach(item => {
    dataMap.set(item.hour, item.visits);
  });

  // Format data for the chart with all hours, using 0 for missing data
  const chartData = allHours.map(hour => ({
    hour,
    visits: dataMap.has(hour) ? dataMap.get(hour) : 0,
    formattedHour: formatHour(hour),
  }));

  // Calculate the maximum value for better Y-axis scaling
  const maxVisits = Math.max(...chartData.map(item => item.visits));
  // Calculate a nice rounded upper bound for the Y-axis
  const calculateYAxisMax = (maxValue: number) => {
    if (maxValue <= 0) return 5; // Default if no data
    if (maxValue <= 5) return Math.ceil(maxValue * 1.2); // Add 20% padding for small values
    if (maxValue <= 10) return Math.ceil(maxValue * 1.1); // Add 10% padding for medium values
    return Math.ceil(maxValue * 1.05); // Add 5% padding for large values
  };
  const yAxisMax = calculateYAxisMax(maxVisits);





  // Custom formatter for Y-axis ticks using Indian number format
  const formatYAxisTick = (value: number) => {
    return formatIndianNumberShort(Math.floor(value));
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Check if user has access to this premium feature
  const isPremiumUser = userPlan === "growth" || userPlan === "pro" || userPlan === "enterprise";

  // If user doesn't have a premium plan, show the premium feature lock component
  if (!isPremiumUser) {
    return (
      <PremiumFeatureLock
        title="Hourly Visit Trend"
        description="Upgrade to Growth plan or higher to see detailed hourly visit trends for your business."
      />
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6"
    >
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40" />

      <div className="relative z-10">
        <div className="flex items-start justify-between mb-6">
          <div className="space-y-1">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
                <Clock className="w-5 h-5 text-primary" />
              </div>
              <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
              <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
                Hourly Breakdown
              </div>
            </div>
            <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
              Hourly Visit Trend
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Today&apos;s visitor activity by hour
            </p>
          </div>
        </div>

        {/* Chart */}
        <div className="h-[280px] w-full">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <AreaChart
            data={chartData}
            margin={isMobile
              ? { top: 5, right: 5, left: 0, bottom: 5 }
              : { top: 10, right: 10, left: 0, bottom: 5 }
            }
          >
            <defs>
              <linearGradient id="fillVisits" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-visits)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-visits)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="formattedHour"
              axisLine={false}
              tickLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={formatXAxisTick}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              domain={[0, yAxisMax]}
              allowDecimals={false}
              tickFormatter={formatYAxisTick}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelFormatter={(label) => `Time: ${label}`}
                  formatter={(value) => [
                    formatIndianNumberShort(Number(value)),
                    " Visits"
                  ]}
                />
              }
            />
            <Area
              dataKey="visits"
              type="natural"
              fill="url(#fillVisits)"
              stroke="var(--color-visits)"
              strokeWidth={isMobile ? 1.5 : 2}
            />
          </AreaChart>
        </ChartContainer>
        </div>
      </div>
    </motion.div>
  );
}
