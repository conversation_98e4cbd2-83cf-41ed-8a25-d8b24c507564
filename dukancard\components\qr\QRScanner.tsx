"use client";

import React, { useEffect, useRef, useState } from 'react';
import { Html5QrcodeScanner, Html5QrcodeScanType } from 'html5-qrcode';
import { validateQRCodeForUser } from '@/lib/utils/qrCodeUtils';
import { requestCameraAccess } from '@/lib/utils/cameraUtils';
import type { CameraCapabilities } from '@/lib/utils/cameraUtils';
import './qr-scanner.css';

interface QRScannerProps {
  onScanSuccess: (_businessSlug: string) => void;
  onScanError?: (_error: string) => void;
  onClose?: () => void;
  className?: string;
}

const QRScanner: React.FC<QRScannerProps> = ({
  onScanSuccess,
  onScanError,
  onClose,
  className = ""
}) => {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const elementRef = useRef<HTMLDivElement>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [capabilities, setCapabilities] = useState<CameraCapabilities | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const qrCodeRegionId = "qr-scanner-region";

  // Request camera access and check capabilities on mount
  useEffect(() => {
    const initializeCamera = async () => {
      try {
        const { stream, capabilities: newCaps } = await requestCameraAccess();
        setCapabilities(newCaps);
        // Stop the stream immediately after getting capabilities, as html5-qrcode will create its own
        stream.getTracks().forEach(track => track.stop());

        if (newCaps.error) {
          setError(newCaps.error);
          onScanError?.(newCaps.error);
        }
      } catch (err: any) {
        const errorMsg = err.error || (err instanceof Error ? err.message : 'Failed to get camera access');
        const caps = err.capabilities || { hasCamera: false, hasPermission: false, isSecureContext: false, supportedConstraints: null, error: errorMsg };
        setCapabilities(caps);
        setError(errorMsg);
        onScanError?.(errorMsg);
      }
    };

    initializeCamera();
  }, [onScanError]);

  // Initialize scanner when capabilities are ready
  useEffect(() => {
    if (!capabilities || capabilities.error || isScanning) {
      return;
    }

    const initializeScanner = async () => {
      try {
        setIsScanning(true);
        setError(null);

        // Create scanner configuration
        const config = {
          fps: 10,
          qrbox: {
            width: 250,
            height: 250,
          },
          aspectRatio: 1.0,
          disableFlip: false,
          supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
          showTorchButtonIfSupported: true,
          showZoomSliderIfSupported: false,
          defaultZoomValueIfSupported: 1,
          // Custom styling to match theme
          colorScheme: 'dark',
        };

        // Success callback
        const onScanSuccessCallback = async (decodedText: string) => {
          if (isProcessing) return; // Prevent multiple scans
          
          setIsProcessing(true);
          
          try {
            // Validate the QR code
            const validation = validateQRCodeForUser(decodedText);
            
            if (!validation.isValid) {
              const errorMessage = validation.error || 'Invalid QR code';
              setError(errorMessage);
              onScanError?.(errorMessage);
              
              // Reset processing after a delay
              setTimeout(() => {
                setIsProcessing(false);
              }, 2000);
              return;
            }

            // Extract business slug and call success callback
            const businessSlug = validation.businessSlug!;
            onScanSuccess(businessSlug);
            
          } catch (err: unknown) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to process QR code';
            setError(errorMessage);
            onScanError?.(errorMessage);
            setIsProcessing(false);
          }
        };

        // Error callback
        const onScanErrorCallback = (errorMessage: string) => {
          // Only log errors, don't show them to user (too noisy)
          console.debug('QR scan error:', errorMessage);
        };

        // Create and start scanner
        const scanner = new Html5QrcodeScanner(
          qrCodeRegionId,
          config,
          false // verbose
        );

        scannerRef.current = scanner;
        scanner.render(onScanSuccessCallback, onScanErrorCallback);

      } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize QR scanner';
        setError(errorMessage);
        onScanError?.(errorMessage);
        setIsScanning(false);
      }
    };

    initializeScanner();

    // Cleanup function
    return () => {
      if (scannerRef.current) {
        scannerRef.current.clear().catch((err) => {
          console.error('Error clearing QR scanner:', err);
        });
        scannerRef.current = null;
      }
      setIsScanning(false);
      setIsProcessing(false);
    };
  }, [capabilities, onScanSuccess, onScanError, isProcessing, isScanning]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (scannerRef.current) {
        scannerRef.current.clear().catch((err) => {
          console.error('Error clearing QR scanner on unmount:', err);
        });
      }
    };
  }, []);

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>
        <div className="text-red-500 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-foreground mb-2">Camera Access Required</h3>
        <p className="text-muted-foreground mb-4">{error}</p>
        {onClose && (
          <button
            onClick={onClose}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Close
          </button>
        )}
      </div>
    );
  }

  if (!capabilities) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Checking camera availability...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {isProcessing && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10 rounded-lg">
          <div className="bg-white rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-foreground">Processing QR code...</p>
          </div>
        </div>
      )}
      
      <div 
        id={qrCodeRegionId} 
        ref={elementRef}
        className="w-full"
      />
      
      <div className="mt-4 text-center">
        <p className="text-sm text-muted-foreground">
          Point your camera at a Dukancard QR code
        </p>
      </div>
    </div>
  );
};

export default QRScanner;
