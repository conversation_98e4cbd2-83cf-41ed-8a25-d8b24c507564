'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Compass, Bell } from 'lucide-react';
import SubscriptionCard, { SubscriptionData } from './SubscriptionCard';

interface SubscriptionListProps {
  initialSubscriptions: SubscriptionData[];
  onUnsubscribeSuccess?: (_subscriptionId: string) => void;
  showUnsubscribe?: boolean;
  variant?: 'default' | 'compact';
  emptyMessage?: string;
  emptyDescription?: string;
  showDiscoverButton?: boolean;
}

export default function SubscriptionList({
  initialSubscriptions,
  onUnsubscribeSuccess,
  showUnsubscribe = true,
  variant = 'default',
  emptyMessage = "No subscriptions found.",
  emptyDescription = "Subscribe to profiles to see them here.",
  showDiscoverButton = false
}: SubscriptionListProps) {
  const [subscriptions, setSubscriptions] = useState(initialSubscriptions);

  // Update subscriptions when initialSubscriptions changes
  useEffect(() => {
    setSubscriptions(initialSubscriptions);
  }, [initialSubscriptions]);

  const handleUnsubscribeSuccess = (subscriptionIdToRemove: string) => {
    setSubscriptions(currentSubscriptions =>
      currentSubscriptions.filter(sub => sub.id !== subscriptionIdToRemove)
    );

    // Call parent callback if provided
    if (onUnsubscribeSuccess) {
      onUnsubscribeSuccess(subscriptionIdToRemove);
    }
  };

  if (subscriptions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-20 text-center">
        <div className="relative mb-8">
          <div className="absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"></div>
          <div className="relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg">
            <Bell className="w-10 h-10 text-primary" />
          </div>
        </div>
        <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3">
          {emptyMessage}
        </h3>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2">
          {emptyDescription}
        </p>
        {showDiscoverButton && (
          <div className="mt-6">
            <Button asChild variant="outline" className="gap-2 px-6 py-2.5 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200">
              <Link href="/discover" target="_blank" rel="noopener noreferrer">
                <Compass className="w-4 h-4" />
                Discover Businesses
              </Link>
            </Button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {subscriptions.map((sub, _index) => {
        const profile = sub.profile;

        if (!profile) {
          return null; // Skip items with missing profiles
        }

        return (
          <div key={sub.id} className="transform transition-all duration-200 hover:scale-[1.02]">
            <SubscriptionCard
              subscriptionId={sub.id}
              profile={profile}
              onUnsubscribeSuccess={showUnsubscribe ? handleUnsubscribeSuccess : undefined}
              showUnsubscribe={showUnsubscribe}
              variant={variant}
            />
          </div>
        );
      })}
    </div>
  );
}
