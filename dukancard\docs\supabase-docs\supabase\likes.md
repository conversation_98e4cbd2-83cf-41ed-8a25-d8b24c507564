# Likes Table Documentation

## Table Overview

The `likes` table in the Dukancard application tracks user likes for business profiles. It records when a user likes a business, enabling social engagement features and popularity metrics for businesses.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the like record |
| user_id | uuid | NO | | Foreign key to users.id, the user who liked the business |
| business_profile_id | uuid | NO | | Foreign key to business_profiles.id, the business that was liked |
| created_at | timestamptz | NO | timezone('utc'::text, now()) | Timestamp when the like was created |

## Constraints

### Primary Key
- `likes_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `likes_user_id_fkey` - Foreign key constraint linking `user_id` to `users.id`
- `likes_business_profile_id_fkey` - Foreign key constraint linking `business_profile_id` to `business_profiles.id`

### Unique Constraints
- `likes_user_business_profile_unique` - Ensures a user can only like a specific business once
  - Columns: `user_id`, `business_profile_id`

### Check Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| likes_pkey | UNIQUE | id | Primary key index |
| likes_user_business_profile_unique | UNIQUE | user_id, business_profile_id | Ensures unique combination of user and business |

## Triggers

### trigger_add_like_activity
- **Event**: INSERT
- **Function**: add_like_activity()
- **Description**: Creates a record in the business_activities table when a new like is added

### trigger_delete_like_activity
- **Event**: DELETE
- **Function**: delete_like_activity()
- **Description**: Removes the corresponding record from the business_activities table when a like is deleted

### handle_new_like
- **Event**: INSERT
- **Function**: update_total_likes()
- **Description**: Updates the total_likes count in the business_profiles table when a new like is added

### handle_deleted_like
- **Event**: DELETE
- **Function**: update_total_likes()
- **Description**: Updates the total_likes count in the business_profiles table when a like is removed

### Trigger Function Definitions

#### add_like_activity()

```sql
CREATE OR REPLACE FUNCTION public.add_like_activity()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
  -- Insert a new activity record
  INSERT INTO business_activities (
    business_profile_id,
    user_id,
    activity_type,
    created_at
  ) VALUES (
    NEW.business_profile_id,
    NEW.user_id,
    'like',
    NEW.created_at
  );

  RETURN NEW;
END;
$function$
```

#### delete_like_activity()

```sql
CREATE OR REPLACE FUNCTION public.delete_like_activity()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
  -- Delete the activity record
  DELETE FROM business_activities
  WHERE business_profile_id = OLD.business_profile_id
  AND user_id = OLD.user_id
  AND activity_type = 'like';

  RETURN OLD;
END;
$function$
```

#### update_total_likes()

```sql
CREATE OR REPLACE FUNCTION public.update_total_likes()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  total_count integer;
  profile_id uuid;
BEGIN
  -- Determine the business_profile_id based on the operation type
  IF (TG_OP = 'DELETE') THEN
    profile_id := OLD.business_profile_id;
  ELSE
    profile_id := NEW.business_profile_id;
  END IF;

  -- Count the total likes for the specific business profile
  SELECT COUNT(*) INTO total_count
  FROM public.likes
  WHERE business_profile_id = profile_id;

  -- Update the business_profiles table
  UPDATE public.business_profiles
  SET total_likes = total_count
  WHERE id = profile_id;

  RETURN NULL; -- Result is ignored since this is an AFTER trigger
END;
$function$
```

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Allow public read access for likes | SELECT | true | |
| Allow authenticated users to insert their own like | INSERT | | |
| Allow users to delete their own like | DELETE | (auth.role() = 'authenticated' AND user_id = auth.uid()) | |

These policies ensure that:
1. Anyone can view likes (public read access)
2. Authenticated users can add likes
3. Users can only delete their own likes

## Related Tables

### users
The `users` table is referenced by the `user_id` foreign key and contains information about the user who liked the business.

### business_profiles
The `business_profiles` table is referenced by the `business_profile_id` foreign key and contains information about the business that was liked.

### business_activities
The `business_activities` table receives new records via the `add_like_activity` trigger when a like is created, tracking this as a business activity.

## Usage Notes

1. **Like Functionality**:
   - The table implements a simple "like" feature for businesses
   - Each record represents one user liking one business
   - The unique constraint ensures a user can only like a business once

2. **Business Metrics**:
   - The `update_total_likes` trigger maintains an accurate count of likes in the `business_profiles.total_likes` field
   - This enables efficient display of like counts without needing to query the likes table

3. **Activity Tracking**:
   - The `add_like_activity` trigger creates a record in the `business_activities` table
   - This allows business owners to see notifications about new likes

4. **Security**:
   - RLS policies ensure that while anyone can view likes, only authenticated users can create them
   - Users can only delete their own likes, not likes created by others

5. **Performance Considerations**:
   - The composite index on `user_id` and `business_profile_id` supports efficient lookups
   - The triggers maintain denormalized counts for performance optimization

6. **User Experience Flow**:
   - When a user likes a business, a new record is created
   - The business owner receives a notification via the business_activities table
   - The business profile's like count is automatically updated
   - If a user tries to like the same business again, the unique constraint prevents duplicate likes

7. **Data Relationships**:
   - Each like is associated with exactly one user and one business profile
   - The relationship between users and business profiles through likes is many-to-many
