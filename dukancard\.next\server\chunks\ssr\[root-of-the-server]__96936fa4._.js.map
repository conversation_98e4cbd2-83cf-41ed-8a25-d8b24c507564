{"version": 3, "sources": [], "sections": [{"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr';\r\nimport { SupabaseClient } from '@supabase/supabase-js';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport async function createClient(): Promise<SupabaseClient<Database>> {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseAnonKey) {\r\n    throw new Error('Supabase environment variables are not set.');\r\n  }\r\n\r\n  // Check if we're in a test environment\r\n  let headersList: Headers | null = null;\r\n  let cookieStore: any = null;\r\n\r\n  try {\r\n    // Dynamically import next/headers to avoid issues in edge runtime\r\n    const { headers, cookies } = await import('next/headers');\r\n    headersList = await headers();\r\n    cookieStore = await cookies();\r\n  } catch (error) {\r\n    // If next/headers is not available (e.g., in edge runtime), continue without it\r\n    console.warn('next/headers not available in this context, using fallback');\r\n  }\r\n\r\n  const isTestEnvironment =\r\n    process.env.NODE_ENV === 'test' ||\r\n    process.env.PLAYWRIGHT_TESTING === 'true' ||\r\n    (headersList && headersList.get('x-playwright-testing') === 'true');\r\n\r\n  if (isTestEnvironment && headersList) {\r\n    // Return a mocked Supabase client for testing\r\n    return createMockSupabaseClient(headersList) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  // If cookies are not available, create a basic server client\r\n  if (!cookieStore) {\r\n    return createServerClient(\r\n      supabaseUrl,\r\n      supabaseAnonKey,\r\n      {\r\n        cookies: {\r\n          getAll() {\r\n            return [];\r\n          },\r\n          setAll() {\r\n            // No-op when cookies are not available\r\n          },\r\n        },\r\n      }\r\n    ) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  return createServerClient(\r\n    supabaseUrl,\r\n    supabaseAnonKey,\r\n    {\r\n      cookies: {\r\n        async getAll() {\r\n          return await cookieStore.getAll();\r\n        },\r\n        async setAll(cookiesToSet: any[]) {\r\n          try {\r\n            for (const { name, value, options } of cookiesToSet) {\r\n              await cookieStore.set(name, value, options);\r\n            }\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  ) as unknown as SupabaseClient<Database>;\r\n}\r\n\r\ntype MockQueryBuilder = {\r\n  select: (columns?: string) => MockQueryBuilder;\r\n  eq: (column: string, value: any) => MockQueryBuilder;\r\n  neq: (column: string, value: any) => MockQueryBuilder;\r\n  gt: (column: string, value: any) => MockQueryBuilder;\r\n  gte: (column: string, value: any) => MockQueryBuilder;\r\n  lt: (column: string, value: any) => MockQueryBuilder;\r\n  lte: (column: string, value: any) => MockQueryBuilder;\r\n  like: (column: string, pattern: string) => MockQueryBuilder;\r\n  ilike: (column: string, pattern: string) => MockQueryBuilder;\r\n  is: (column: string, value: any) => MockQueryBuilder;\r\n  in: (column: string, values: any[]) => MockQueryBuilder;\r\n  contains: (column: string, value: any) => MockQueryBuilder;\r\n  containedBy: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeAdjacent: (column: string, value: any) => MockQueryBuilder;\r\n  overlaps: (column: string, value: any) => MockQueryBuilder;\r\n  textSearch: (column: string, query: string) => MockQueryBuilder;\r\n  match: (query: Record<string, any>) => MockQueryBuilder;\r\n  not: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  or: (filters: string) => MockQueryBuilder;\r\n  filter: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  order: (column: string, options?: { ascending?: boolean; nullsFirst?: boolean }) => MockQueryBuilder;\r\n  limit: (count: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  range: (from: number, to: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  abortSignal: (signal: AbortSignal) => MockQueryBuilder;\r\n  single: () => Promise<any>;\r\n  maybeSingle: () => Promise<any>;\r\n  then: (callback?: any) => Promise<any>;\r\n  data: any;\r\n  error: any;\r\n  count: number;\r\n  status: number;\r\n  statusText: string;\r\n};\r\n\r\nfunction createMockSupabaseClient(headersList: Headers) {\r\n  const testAuthState = headersList.get('x-test-auth-state');\r\n  const testUserType = headersList.get('x-test-user-type');\r\n  const testHasProfile = testUserType === 'customer' || testUserType === 'business';\r\n  const testBusinessSlug = headersList.get('x-test-business-slug');\r\n  const testPlanId = headersList.get('x-test-plan-id') || 'free';\r\n\r\n  return {\r\n    auth: {\r\n      getUser: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { user: { id: 'test-user-id', email: '<EMAIL>' } }, error: null };\r\n        }\r\n        return { data: { user: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      getSession: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } }, error: null };\r\n        }\r\n        return { data: { session: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      signInWithOtp: async () => ({ data: { user: null, session: null }, error: null }),\r\n      signOut: async () => ({ error: null }),\r\n    },\r\n    from: (table: string) => createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId),\r\n  };\r\n}\r\n\r\nfunction createMockQueryBuilder(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n): any {\r\n  const getMockData = () => getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);\r\n\r\n  const createChainableMock = (data?: any): MockQueryBuilder => ({\r\n    select: (_columns?: string) => createChainableMock(data),\r\n    eq: (_column: string, _value: any) => createChainableMock(data),\r\n    neq: (_column: string, _value: any) => createChainableMock(data),\r\n    gt: (_column: string, _value: any) => createChainableMock(data),\r\n    gte: (_column: string, _value: any) => createChainableMock(data),\r\n    lt: (_column: string, _value: any) => createChainableMock(data),\r\n    lte: (_column: string, _value: any) => createChainableMock(data),\r\n    like: (_column: string, _pattern: string) => createChainableMock(data),\r\n    ilike: (_column: string, _pattern: string) => createChainableMock(data),\r\n    is: (_column: string, _value: any) => createChainableMock(data),\r\n    in: (_column: string, _values: any[]) => createChainableMock(data),\r\n    contains: (_column: string, _value: any) => createChainableMock(data),\r\n    containedBy: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeAdjacent: (_column: string, _value: any) => createChainableMock(data),\r\n    overlaps: (_column: string, _value: any) => createChainableMock(data),\r\n    textSearch: (_column: string, _query: string) => createChainableMock(data),\r\n    match: (_query: Record<string, any>) => createChainableMock(data),\r\n    not: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    or: (_filters: string) => createChainableMock(data),\r\n    filter: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    order: (_column: string, _options?: { ascending?: boolean; nullsFirst?: boolean }) => createChainableMock(data),\r\n    limit: (_count: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    range: (_from: number, _to: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    abortSignal: (_signal: AbortSignal) => createChainableMock(data),\r\n    single: async () => getMockData(),\r\n    maybeSingle: async () => getMockData(),\r\n    then: async (callback?: any) => {\r\n      const result = getMockData();\r\n      return callback ? callback(result) : result;\r\n    },\r\n    data: data || [],\r\n    error: null,\r\n    count: data ? data.length : 0,\r\n    status: 200,\r\n    statusText: 'OK',\r\n  });\r\n\r\n  return {\r\n    select: (_columns?: string) => createChainableMock(),\r\n    insert: (data: any | any[]) => ({\r\n      select: (_columns?: string) => ({\r\n        single: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        maybeSingle: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        then: async (_callback?: any) => {\r\n          const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n          return _callback ? _callback(result) : result;\r\n        },\r\n      }),\r\n      then: async (_callback?: any) => {\r\n        const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n        return _callback ? _callback(result) : result;\r\n      },\r\n    }),\r\n    update: (data: any) => createChainableMock(data),\r\n    upsert: (data: any | any[]) => createChainableMock(data),\r\n    delete: () => createChainableMock(),\r\n    rpc: (_functionName: string, _params?: any) => createChainableMock(),\r\n  };\r\n}\r\n\r\n/**\r\n * Helper function to get mock table data based on test state\r\n */\r\nfunction getMockTableData(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n) {\r\n  if (table === 'customer_profiles') {\r\n    const hasCustomerProfile = testHasProfile && testUserType === 'customer';\r\n    return {\r\n      data: hasCustomerProfile ? {\r\n        id: 'test-user-id',\r\n        name: 'Test Customer',\r\n        avatar_url: null,\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        address: 'Test Address',\r\n        city: 'Test City',\r\n        state: 'Test State',\r\n        pincode: '123456'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'business_profiles') {\r\n    const hasBusinessProfile = testHasProfile && testUserType === 'business';\r\n    return {\r\n      data: hasBusinessProfile ? {\r\n        id: 'test-user-id',\r\n        business_slug: testBusinessSlug || null,\r\n        trial_end_date: null,\r\n        has_active_subscription: true,\r\n        business_name: 'Test Business',\r\n        city_slug: 'test-city',\r\n        state_slug: 'test-state',\r\n        locality_slug: 'test-locality',\r\n        pincode: '123456',\r\n        business_description: 'Test business description',\r\n        business_category: 'retail',\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        website: 'https://testbusiness.com'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'payment_subscriptions') {\r\n    return {\r\n      data: testUserType === 'business' ? {\r\n        id: 'test-subscription-id',\r\n        plan_id: testPlanId,\r\n        business_profile_id: 'test-user-id',\r\n        status: 'active',\r\n        created_at: '2024-01-01T00:00:00Z'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'products') {\r\n    return {\r\n      data: testUserType === 'business' ? [\r\n        {\r\n          id: 'test-product-1',\r\n          name: 'Test Product 1',\r\n          price: 100,\r\n          business_profile_id: 'test-user-id',\r\n          available: true\r\n        },\r\n        {\r\n          id: 'test-product-2',\r\n          name: 'Test Product 2',\r\n          price: 200,\r\n          business_profile_id: 'test-user-id',\r\n          available: false\r\n        }\r\n      ] : [],\r\n      error: null\r\n    };\r\n  }\r\n\r\n  // Default return for unknown tables\r\n  return { data: null, error: null };\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAIO,eAAe;IACpB,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAEtC;IAEA,uCAAuC;IACvC,IAAI,cAA8B;IAClC,IAAI,cAAmB;IAEvB,IAAI;QACF,kEAAkE;QAClE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAC7B,cAAc,MAAM;QACpB,cAAc,MAAM;IACtB,EAAE,OAAO,OAAO;QACd,gFAAgF;QAChF,QAAQ,IAAI,CAAC;IACf;IAEA,MAAM,oBACJ,oDAAyB,UACzB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UAClC,eAAe,YAAY,GAAG,CAAC,4BAA4B;IAE9D,IAAI,qBAAqB,aAAa;QACpC,8CAA8C;QAC9C,OAAO,yBAAyB;IAClC;IAEA,6DAA6D;IAC7D,IAAI,CAAC,aAAa;QAChB,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;YACE,SAAS;gBACP;oBACE,OAAO,EAAE;gBACX;gBACA;gBACE,uCAAuC;gBACzC;YACF;QACF;IAEJ;IAEA,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP,MAAM;gBACJ,OAAO,MAAM,YAAY,MAAM;YACjC;YACA,MAAM,QAAO,YAAmB;gBAC9B,IAAI;oBACF,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,aAAc;wBACnD,MAAM,YAAY,GAAG,CAAC,MAAM,OAAO;oBACrC;gBACF,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAyCA,SAAS,yBAAyB,WAAoB;IACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC;IACtC,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,MAAM,iBAAiB,iBAAiB,cAAc,iBAAiB;IACvE,MAAM,mBAAmB,YAAY,GAAG,CAAC;IACzC,MAAM,aAAa,YAAY,GAAG,CAAC,qBAAqB;IAExD,OAAO;QACL,MAAM;YACJ,SAAS;gBACP,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,MAAM;gCAAE,IAAI;gCAAgB,OAAO;4BAAmB;wBAAE;wBAAG,OAAO;oBAAK;gBAC1F;gBACA,OAAO;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YACvG;YACA,YAAY;gBACV,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,SAAS;gCAAE,MAAM;oCAAE,IAAI;oCAAgB,OAAO;gCAAmB;4BAAE;wBAAE;wBAAG,OAAO;oBAAK;gBACvG;gBACA,OAAO;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YAC1G;YACA,eAAe,UAAY,CAAC;oBAAE,MAAM;wBAAE,MAAM;wBAAM,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YAChF,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;QACvC;QACA,MAAM,CAAC,QAAkB,uBAAuB,OAAO,cAAc,gBAAgB,kBAAkB;IACzG;AACF;AAEA,SAAS,uBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,MAAM,cAAc,IAAM,iBAAiB,OAAO,cAAc,gBAAgB,kBAAkB;IAElG,MAAM,sBAAsB,CAAC,OAAiC,CAAC;YAC7D,QAAQ,CAAC,WAAsB,oBAAoB;YACnD,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,MAAM,CAAC,SAAiB,WAAqB,oBAAoB;YACjE,OAAO,CAAC,SAAiB,WAAqB,oBAAoB;YAClE,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,IAAI,CAAC,SAAiB,UAAmB,oBAAoB;YAC7D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,aAAa,CAAC,SAAiB,SAAgB,oBAAoB;YACnE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,eAAe,CAAC,SAAiB,SAAgB,oBAAoB;YACrE,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,YAAY,CAAC,SAAiB,SAAmB,oBAAoB;YACrE,OAAO,CAAC,SAAgC,oBAAoB;YAC5D,KAAK,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YAC9E,IAAI,CAAC,WAAqB,oBAAoB;YAC9C,QAAQ,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YACjF,OAAO,CAAC,SAAiB,WAA6D,oBAAoB;YAC1G,OAAO,CAAC,QAAgB,WAAyC,oBAAoB;YACrF,OAAO,CAAC,OAAe,KAAa,WAAyC,oBAAoB;YACjG,aAAa,CAAC,UAAyB,oBAAoB;YAC3D,QAAQ,UAAY;YACpB,aAAa,UAAY;YACzB,MAAM,OAAO;gBACX,MAAM,SAAS;gBACf,OAAO,WAAW,SAAS,UAAU;YACvC;YACA,MAAM,QAAQ,EAAE;YAChB,OAAO;YACP,OAAO,OAAO,KAAK,MAAM,GAAG;YAC5B,QAAQ;YACR,YAAY;QACd,CAAC;IAED,OAAO;QACL,QAAQ,CAAC,WAAsB;QAC/B,QAAQ,CAAC,OAAsB,CAAC;gBAC9B,QAAQ,CAAC,WAAsB,CAAC;wBAC9B,QAAQ,UAAY,CAAC;gCACnB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,aAAa,UAAY,CAAC;gCACxB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,MAAM,OAAO;4BACX,MAAM,SAAS;gCAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;oCAAC;iCAAK;gCAAE,OAAO;4BAAK;4BACxE,OAAO,YAAY,UAAU,UAAU;wBACzC;oBACF,CAAC;gBACD,MAAM,OAAO;oBACX,MAAM,SAAS;wBAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;4BAAC;yBAAK;wBAAE,OAAO;oBAAK;oBACxE,OAAO,YAAY,UAAU,UAAU;gBACzC;YACF,CAAC;QACD,QAAQ,CAAC,OAAc,oBAAoB;QAC3C,QAAQ,CAAC,OAAsB,oBAAoB;QACnD,QAAQ,IAAM;QACd,KAAK,CAAC,eAAuB,UAAkB;IACjD;AACF;AAEA;;CAEC,GACD,SAAS,iBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,eAAe,oBAAoB;gBACnC,gBAAgB;gBAChB,yBAAyB;gBACzB,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;gBACT,sBAAsB;gBACtB,mBAAmB;gBACnB,OAAO;gBACP,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,yBAAyB;QACrC,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC,IAAI;gBACJ,SAAS;gBACT,qBAAqB;gBACrB,QAAQ;gBACR,YAAY;YACd,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,YAAY;QACxB,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;aACD,GAAG,EAAE;YACN,OAAO;QACT;IACF;IAEA,oCAAoC;IACpC,OAAO;QAAE,MAAM;QAAM,OAAO;IAAK;AACnC", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\nexport const IndianMobileSchema = z\r\n  .string()\r\n  .trim()\r\n  .min(10, { message: \"Mobile number must be 10 digits\" })\r\n  .max(10, { message: \"Mobile number must be 10 digits\" })\r\n  .regex(/^[6-9]\\d{9}$/, { message: \"Please enter a valid Indian mobile number\" });\r\n\r\nexport const EmailOTPSchema = z.object({\r\n  email: z\r\n    .string()\r\n    .trim()\r\n    .min(1, { message: \"Email is required\" })\r\n    .email({ message: \"Please enter a valid email address\" }),\r\n});\r\n\r\nexport const VerifyOTPSchema = z.object({\r\n  email: z\r\n    .string()\r\n    .trim()\r\n    .min(1, { message: \"Email is required\" })\r\n    .email({ message: \"Please enter a valid email address\" }),\r\n  otp: z\r\n    .string()\r\n    .trim()\r\n    .min(6, { message: \"OTP must be 6 digits\" })\r\n    .max(6, { message: \"OTP must be 6 digits\" })\r\n    .regex(/^\\d{6}$/, { message: \"OTP must be 6 digits\" }),\r\n});\r\n\r\nexport const PasswordComplexitySchema = z\r\n  .string()\r\n  .min(6, \"Password must be at least 6 characters long\")\r\n  .regex(/[A-Z]/, \"Password must contain at least one uppercase letter\")\r\n  .regex(/[a-z]/, \"Password must contain at least one lowercase letter\")\r\n  .regex(/\\d/, \"Password must contain at least one number\")\r\n  .regex(/[^a-zA-Z0-9]/, \"Password must contain at least one special character\");\r\n\r\nexport const MobilePasswordLoginSchema = z.object({\r\n  mobile: IndianMobileSchema,\r\n  password: z.string().trim().min(1, { message: \"Password is required\" }),\r\n});"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAChC,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,GAAG,CAAC,IAAI;IAAE,SAAS;AAAkC,GACrD,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAEzE,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;AAC3D;AAEO,MAAM,kBAAkB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,KAAK,CAAC;QAAE,SAAS;IAAqC;IACzD,KAAK,oIAAA,CAAA,IAAC,CACH,MAAM,GACN,IAAI,GACJ,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAuB;AACxD;AAEO,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,MAAM,6CACZ,KAAK,CAAC,gBAAgB;AAElB,MAAM,4BAA4B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChD,QAAQ;IACR,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB;AACvE", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/supabaseErrorHandler.ts"], "sourcesContent": ["import { AuthError } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * User-friendly error messages for Supabase auth error codes\r\n */\r\nconst AUTH_ERROR_MESSAGES: Record<string, string> = {\r\n  // Rate limiting errors\r\n  over_email_send_rate_limit: \"Email rate limit exceeded. Please wait before requesting another OTP.\",\r\n  over_request_rate_limit: \"Too many requests. Please wait a few minutes before trying again.\",\r\n  over_sms_send_rate_limit: \"Too many SMS messages sent. Please wait before requesting another OTP.\",\r\n\r\n  // OTP related errors\r\n  otp_expired: \"OTP has expired. Please request a new one.\",\r\n  otp_disabled: \"OTP authentication is currently disabled.\",\r\n\r\n  // Token/JWT errors\r\n  bad_jwt: \"Invalid authentication token. Please sign in again.\",\r\n  session_expired: \"Your session has expired. Please sign in again.\",\r\n  session_not_found: \"Session not found. Please sign in again.\",\r\n  refresh_token_not_found: \"Authentication expired. Please sign in again.\",\r\n  refresh_token_already_used: \"Authentication expired. Please sign in again.\",\r\n\r\n  // User/account errors\r\n  user_not_found: \"User account not found.\",\r\n  user_banned: \"Your account has been temporarily suspended.\",\r\n  email_not_confirmed: \"Please verify your email address before signing in.\",\r\n  phone_not_confirmed: \"Please verify your phone number before signing in.\",\r\n  invalid_credentials: \"Invalid email or password.\",\r\n\r\n  // Signup/registration errors\r\n  signup_disabled: \"New account registration is currently disabled.\",\r\n  email_exists: \"An account with this email already exists.\",\r\n  phone_exists: \"An account with this phone number already exists.\",\r\n  weak_password: \"Password does not meet security requirements.\",\r\n  email_address_invalid: \"Please enter a valid email address.\",\r\n  email_address_not_authorized: \"This email address is not authorized for registration.\",\r\n\r\n  // Provider/OAuth errors\r\n  provider_disabled: \"This sign-in method is currently disabled.\",\r\n  oauth_provider_not_supported: \"This sign-in provider is not supported.\",\r\n  provider_email_needs_verification: \"Please verify your email address to complete sign-in.\",\r\n\r\n  // Validation errors\r\n  validation_failed: \"Please check your input and try again.\",\r\n  bad_json: \"Invalid request format. Please try again.\",\r\n\r\n  // MFA errors\r\n  mfa_challenge_expired: \"MFA challenge expired. Please try again.\",\r\n  mfa_verification_failed: \"Invalid MFA code. Please try again.\",\r\n  insufficient_aal: \"Additional authentication required.\",\r\n\r\n  // CAPTCHA errors\r\n  captcha_failed: \"CAPTCHA verification failed. Please try again.\",\r\n\r\n  // General errors\r\n  conflict: \"A conflict occurred. Please try again.\",\r\n  request_timeout: \"Request timed out. Please try again.\",\r\n  unexpected_failure: \"An unexpected error occurred. Please try again.\",\r\n  same_password: \"New password must be different from your current password.\",\r\n\r\n  // Flow state errors\r\n  flow_state_expired: \"Authentication session expired. Please start over.\",\r\n  flow_state_not_found: \"Authentication session not found. Please start over.\",\r\n\r\n  // Reauthentication errors\r\n  reauthentication_needed: \"Please verify your identity to continue.\",\r\n  reauthentication_not_valid: \"Identity verification failed. Please try again.\",\r\n};\r\n\r\n/**\r\n * Default error message for unknown error codes\r\n */\r\nconst DEFAULT_ERROR_MESSAGE = \"An error occurred. Please try again.\";\r\n\r\n/**\r\n * Handles Supabase auth errors and returns user-friendly messages\r\n * @param error - The error object from Supabase\r\n * @returns User-friendly error message\r\n */\r\nexport function handleSupabaseAuthError(error: AuthError | Error | null): string {\r\n  if (!error) {\r\n    return DEFAULT_ERROR_MESSAGE;\r\n  }\r\n\r\n  // Check if it's an AuthError with a code property\r\n  if ('code' in error && error.code) {\r\n    const userMessage = AUTH_ERROR_MESSAGES[error.code];\r\n    if (userMessage) {\r\n      return userMessage;\r\n    }\r\n  }\r\n\r\n  // Check if it's an AuthError with a message we can parse for specific cases\r\n  if (error.message) {\r\n      const message = error.message.toLowerCase();\r\n    \r\n    // Handle common network-related messages first\r\n    if (message.includes('failed to fetch') || message.includes('network request failed') || message.includes('network error')) {\r\n      return \"Network error. Please check your internet connection and try again.\";\r\n    }\r\n\r\n    // Handle other common message patterns that might not have specific codes\r\n    if (message.includes('token has expired') || message.includes('expired')) {\r\n      return \"Your session has expired. Please sign in again.\";\r\n    }\r\n    \r\n    if (message.includes('invalid token') || message.includes('invalid otp')) {\r\n      return \"Invalid code. Please check and try again.\";\r\n    }\r\n    \r\n    if (message.includes('rate limit') || message.includes('too many')) {\r\n      return \"Too many attempts. Please wait before trying again.\";\r\n    }\r\n    \r\n    if (message.includes('email already exists') || message.includes('user already exists')) {\r\n      return \"An account with this email already exists.\";\r\n    }\r\n    \r\n    if (message.includes('invalid email')) {\r\n      return \"Please enter a valid email address.\";\r\n    }\r\n    \r\n    if (message.includes('weak password')) {\r\n      return \"Password does not meet security requirements.\";\r\n    }\r\n  }\r\n\r\n  // Return default message for unknown errors\r\n  return DEFAULT_ERROR_MESSAGE;\r\n}\r\n\r\n/**\r\n * Checks if an error is a rate limit error\r\n * @param error - The error object from Supabase\r\n * @returns True if it's a rate limit error\r\n */\r\nexport function isRateLimitError(error: AuthError | Error | null): boolean {\r\n  if (!error) return false;\r\n\r\n  if ('code' in error && error.code) {\r\n    return [\r\n      'over_email_send_rate_limit',\r\n      'over_request_rate_limit',\r\n      'over_sms_send_rate_limit'\r\n    ].includes(error.code);\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\n/**\r\n * Checks if an error is specifically an email rate limit error\r\n * This indicates Supabase is trying to send verification emails instead of OTP\r\n * @param error - The error object from Supabase\r\n * @returns True if it's an email rate limit error\r\n */\r\nexport function isEmailRateLimitError(error: AuthError | Error | null): boolean {\r\n  if (!error) return false;\r\n\r\n  if ('code' in error && error.code) {\r\n    return error.code === 'over_email_send_rate_limit';\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\n/**\r\n * Checks if an error is a temporary/retryable error\r\n * @param error - The error object from Supabase\r\n * @returns True if the error is temporary and user should retry\r\n */\r\nexport function isTemporaryError(error: AuthError | Error | null): boolean {\r\n  if (!error) return false;\r\n  \r\n  if ('code' in error && error.code) {\r\n    return [\r\n      'over_email_send_rate_limit',\r\n      'over_request_rate_limit',\r\n      'over_sms_send_rate_limit',\r\n      'request_timeout',\r\n      'conflict',\r\n      'unexpected_failure'\r\n    ].includes(error.code);\r\n  }\r\n  \r\n  return false;\r\n}\r\n\r\n/**\r\n * Gets the retry delay in seconds for rate limit errors\r\n * @param error - The error object from Supabase\r\n * @returns Suggested retry delay in seconds, or null if not applicable\r\n */\r\nexport function getRetryDelay(error: AuthError | Error | null): number | null {\r\n  if (!error || !isRateLimitError(error)) return null;\r\n  \r\n  if ('code' in error && error.code) {\r\n    switch (error.code) {\r\n      case 'over_email_send_rate_limit':\r\n        return 60; // 1 minute for email rate limits\r\n      case 'over_sms_send_rate_limit':\r\n        return 60; // 1 minute for SMS rate limits\r\n      case 'over_request_rate_limit':\r\n        return 300; // 5 minutes for general rate limits\r\n      default:\r\n        return 60;\r\n    }\r\n  }\r\n  \r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;;CAEC,GACD,MAAM,sBAA8C;IAClD,uBAAuB;IACvB,4BAA4B;IAC5B,yBAAyB;IACzB,0BAA0B;IAE1B,qBAAqB;IACrB,aAAa;IACb,cAAc;IAEd,mBAAmB;IACnB,SAAS;IACT,iBAAiB;IACjB,mBAAmB;IACnB,yBAAyB;IACzB,4BAA4B;IAE5B,sBAAsB;IACtB,gBAAgB;IAChB,aAAa;IACb,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IAErB,6BAA6B;IAC7B,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,eAAe;IACf,uBAAuB;IACvB,8BAA8B;IAE9B,wBAAwB;IACxB,mBAAmB;IACnB,8BAA8B;IAC9B,mCAAmC;IAEnC,oBAAoB;IACpB,mBAAmB;IACnB,UAAU;IAEV,aAAa;IACb,uBAAuB;IACvB,yBAAyB;IACzB,kBAAkB;IAElB,iBAAiB;IACjB,gBAAgB;IAEhB,iBAAiB;IACjB,UAAU;IACV,iBAAiB;IACjB,oBAAoB;IACpB,eAAe;IAEf,oBAAoB;IACpB,oBAAoB;IACpB,sBAAsB;IAEtB,0BAA0B;IAC1B,yBAAyB;IACzB,4BAA4B;AAC9B;AAEA;;CAEC,GACD,MAAM,wBAAwB;AAOvB,SAAS,wBAAwB,KAA+B;IACrE,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,kDAAkD;IAClD,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,MAAM,cAAc,mBAAmB,CAAC,MAAM,IAAI,CAAC;QACnD,IAAI,aAAa;YACf,OAAO;QACT;IACF;IAEA,4EAA4E;IAC5E,IAAI,MAAM,OAAO,EAAE;QACf,MAAM,UAAU,MAAM,OAAO,CAAC,WAAW;QAE3C,+CAA+C;QAC/C,IAAI,QAAQ,QAAQ,CAAC,sBAAsB,QAAQ,QAAQ,CAAC,6BAA6B,QAAQ,QAAQ,CAAC,kBAAkB;YAC1H,OAAO;QACT;QAEA,0EAA0E;QAC1E,IAAI,QAAQ,QAAQ,CAAC,wBAAwB,QAAQ,QAAQ,CAAC,YAAY;YACxE,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,oBAAoB,QAAQ,QAAQ,CAAC,gBAAgB;YACxE,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,iBAAiB,QAAQ,QAAQ,CAAC,aAAa;YAClE,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,2BAA2B,QAAQ,QAAQ,CAAC,wBAAwB;YACvF,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,kBAAkB;YACrC,OAAO;QACT;QAEA,IAAI,QAAQ,QAAQ,CAAC,kBAAkB;YACrC,OAAO;QACT;IACF;IAEA,4CAA4C;IAC5C,OAAO;AACT;AAOO,SAAS,iBAAiB,KAA+B;IAC9D,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,OAAO;YACL;YACA;YACA;SACD,CAAC,QAAQ,CAAC,MAAM,IAAI;IACvB;IAEA,OAAO;AACT;AAQO,SAAS,sBAAsB,KAA+B;IACnE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,OAAO,MAAM,IAAI,KAAK;IACxB;IAEA,OAAO;AACT;AAOO,SAAS,iBAAiB,KAA+B;IAC9D,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;SACD,CAAC,QAAQ,CAAC,MAAM,IAAI;IACvB;IAEA,OAAO;AACT;AAOO,SAAS,cAAc,KAA+B;IAC3D,IAAI,CAAC,SAAS,CAAC,iBAAiB,QAAQ,OAAO;IAE/C,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO,IAAI,iCAAiC;YAC9C,KAAK;gBACH,OAAO,IAAI,+BAA+B;YAC5C,KAAK;gBACH,OAAO,KAAK,oCAAoC;YAClD;gBACE,OAAO;QACX;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { z } from \"zod\";\r\nimport { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from \"@/lib/schemas/authSchemas\";\r\nimport { handleSupabaseAuthError, isEmailRateLimitError } from \"@/lib/utils/supabaseErrorHandler\";\r\n\r\n// Validate email format\r\nfunction validateEmail(email: string): { isValid: boolean; message?: string } {\r\n  if (!email) {\r\n    return { isValid: false, message: 'Email is required' };\r\n  }\r\n  \r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  if (!emailRegex.test(email)) {\r\n    return { isValid: false, message: 'Please enter a valid email address' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Validate OTP format\r\nfunction validateOTP(otp: string): { isValid: boolean; message?: string } {\r\n  if (!otp) {\r\n    return { isValid: false, message: 'OTP is required' };\r\n  }\r\n  \r\n  if (otp.length !== 6) {\r\n    return { isValid: false, message: 'OTP must be 6 digits' };\r\n  }\r\n  \r\n  if (!/^\\d{6}$/.test(otp)) {\r\n    return { isValid: false, message: 'OTP must contain only numbers' };\r\n  }\r\n  \r\n  return { isValid: true };\r\n}\r\n\r\n// Send OTP to email\r\nexport async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {\r\n  const { email } = values;\r\n  const emailValidation = validateEmail(email);\r\n  if (!emailValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: emailValidation.message,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const supabase = await createClient();\r\n    const { error } = await supabase.auth.signInWithOtp({\r\n      email: email,\r\n      options: {\r\n        // Set shouldCreateUser to true to allow new user registration\r\n        shouldCreateUser: true,\r\n        data: {\r\n          auth_type: \"email\",\r\n        },\r\n      },\r\n    });\r\n\r\n    if (error) {\r\n      if (isEmailRateLimitError(error)) {\r\n        return {\r\n          success: false,\r\n          error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n          isConfigurationError: true,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"OTP sent to your email address. Please check your inbox.\",\r\n    };\r\n  } catch (error) {\r\n    if (isEmailRateLimitError(error as Error)) {\r\n      return {\r\n        success: false,\r\n        error: \"Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.\",\r\n        isConfigurationError: true,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP and sign in\r\nexport async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {\r\n  const { email, otp } = values;\r\n  const otpValidation = validateOTP(otp);\r\n  if (!otpValidation.isValid) {\r\n    return {\r\n      success: false,\r\n      error: otpValidation.message,\r\n    };\r\n  }\r\n  \r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { data, error } = await supabase.auth.verifyOtp({\r\n      email: email,\r\n      token: otp,\r\n      type: 'email',\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n\r\n// Mobile + password login\r\nexport async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {\r\n  const validatedFields = MobilePasswordLoginSchema.safeParse(values);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid mobile number or password format\",\r\n    };\r\n  }\r\n\r\n  const { mobile, password } = validatedFields.data;\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Format mobile number with +91 prefix\r\n    const phoneNumber = `+91${mobile}`;\r\n\r\n    const { data, error } = await supabase.auth.signInWithPassword({\r\n      phone: phoneNumber,\r\n      password: password,\r\n    });\r\n\r\n    if (error) {\r\n      return {\r\n        success: false,\r\n        error: handleSupabaseAuthError(error),\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data,\r\n      message: \"Successfully signed in!\",\r\n    };\r\n  } catch (error) {\r\n    return {\r\n      success: false,\r\n      error: handleSupabaseAuthError(error as Error),\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;AACA;;;;;;;AAEA,wBAAwB;AACxB,SAAS,cAAc,KAAa;IAClC,IAAI,CAAC,OAAO;QACV,OAAO;YAAE,SAAS;YAAO,SAAS;QAAoB;IACxD;IAEA,MAAM,aAAa;IACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;QAC3B,OAAO;YAAE,SAAS;YAAO,SAAS;QAAqC;IACzE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,sBAAsB;AACtB,SAAS,YAAY,GAAW;IAC9B,IAAI,CAAC,KAAK;QACR,OAAO;YAAE,SAAS;YAAO,SAAS;QAAkB;IACtD;IAEA,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,OAAO;YAAE,SAAS;YAAO,SAAS;QAAuB;IAC3D;IAEA,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM;QACxB,OAAO;YAAE,SAAS;YAAO,SAAS;QAAgC;IACpE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,QAAQ,MAAsC;IAClE,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,kBAAkB,cAAc;IACtC,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,OAAO,gBAAgB,OAAO;QAChC;IACF;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC;YAClD,OAAO;YACP,SAAS;gBACP,8DAA8D;gBAC9D,kBAAkB;gBAClB,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QAEA,IAAI,OAAO;YACT,IAAI,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;gBAChC,OAAO;oBACL,SAAS;oBACT,OAAO;oBACP,sBAAsB;gBACxB;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;YACjC;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,IAAI,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EAAE,QAAiB;YACzC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,sBAAsB;YACxB;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;QACjC;IACF;AACF;AAGO,eAAe,UAAU,MAAuC;IACrE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IACvB,MAAM,gBAAgB,YAAY;IAClC,IAAI,CAAC,cAAc,OAAO,EAAE;QAC1B,OAAO;YACL,SAAS;YACT,OAAO,cAAc,OAAO;QAC9B;IACF;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC;YACpD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAEA,IAAI,OAAO;YACT,OAAO;gBACL,SAAS;gBACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;YACjC;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;QACjC;IACF;AACF;AAGO,eAAe,wBAAwB,MAAiD;IAC7F,MAAM,kBAAkB,6HAAA,CAAA,4BAAyB,CAAC,SAAS,CAAC;IAE5D,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;IACjD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,uCAAuC;QACvC,MAAM,cAAc,CAAC,GAAG,EAAE,QAAQ;QAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D,OAAO;YACP,UAAU;QACZ;QAEA,IAAI,OAAO;YACT,OAAO;gBACL,SAAS;gBACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;YACjC;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,CAAA,GAAA,oIAAA,CAAA,0BAAuB,AAAD,EAAE;QACjC;IACF;AACF;;;IA3IsB;IA2DA;IAwCA;;AAnGA,+OAAA;AA2DA,+OAAA;AAwCA,+OAAA", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28main%29/login/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {sendOTP as '40c4645d03ce93888084da699909d1cb8ed4c08028'} from 'ACTIONS_MODULE0'\nexport {verifyOTP as '40c382d01f32ba231e3b87edecb6a907b27fe5705e'} from 'ACTIONS_MODULE0'\nexport {loginWithMobilePassword as '40476f0ade05992429e5520119c8940bc9ac50a28d'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/LoginForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoginForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/login/LoginForm.tsx <module evaluation>\",\n    \"LoginForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gEACA", "debugId": null}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/LoginForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoginForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/login/LoginForm.tsx\",\n    \"LoginForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4CACA", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 898, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/auth/AuthPageBackground.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/components/auth/AuthPageBackground.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/components/auth/AuthPageBackground.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqT,GAClV,mFACA", "debugId": null}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/components/auth/AuthPageBackground.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/components/auth/AuthPageBackground.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/components/auth/AuthPageBackground.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/login/page.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\r\nimport { LoginForm } from \"./LoginForm\";\r\nimport { Suspense } from \"react\";\r\nimport AuthPageBackground from \"../components/auth/AuthPageBackground\";\r\n\r\nexport async function generateMetadata(): Promise<Metadata> {\r\n  const title = \"Sign In\";\r\n  const description =\r\n    \"Sign in to your Dukancard account or create a new account with just your email address.\";\r\n\r\n  return {\r\n    title, // Uses template: \"Sign In - Dukancard\"\r\n    description,\r\n    robots: \"noindex, follow\", // Prevent indexing\r\n    // Keywords are generally not needed for noindex pages\r\n  };\r\n}\r\n\r\nexport default function LoginPage() {\r\n  return (\r\n    // Use semantic background and add top padding\r\n    <div className=\"w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50 dark:bg-neutral-950 p-2 sm:p-4 pt-6 pb-20 md:pb-6 relative overflow-hidden\">\r\n      {/* Add animated background */}\r\n      <AuthPageBackground />\r\n\r\n      <Suspense\r\n        fallback={\r\n          <div className=\"flex flex-col justify-center items-center min-h-screen gap-2 relative z-10\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[var(--brand-gold)]\"></div>\r\n            <p className=\"text-muted-foreground\">Loading sign in form...</p>\r\n          </div>\r\n        }\r\n      >\r\n        <LoginForm />\r\n      </Suspense>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAEO,eAAe;IACpB,MAAM,QAAQ;IACd,MAAM,cACJ;IAEF,OAAO;QACL;QACA;QACA,QAAQ;IAEV;AACF;AAEe,SAAS;IACtB,OACE,8CAA8C;kBAC9C,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,4JAAA,CAAA,UAAkB;;;;;0BAEnB,8OAAC,qMAAA,CAAA,WAAQ;gBACP,wBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;0BAIzC,cAAA,8OAAC,sIAAA,CAAA,YAAS;;;;;;;;;;;;;;;;AAIlB", "debugId": null}}]}