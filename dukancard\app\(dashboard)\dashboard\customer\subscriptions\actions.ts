"use server";

import { subscriptionsService, SubscriptionWithProfile, SubscriptionsResult } from '@/lib/services/socialService';

// Re-export types for compatibility
export type { SubscriptionWithProfile };

/**
 * Fetch subscriptions with pagination and search
 */
export async function fetchSubscriptions(
  userId: string,
  page: number = 1,
  limit: number = 12, // Optimized for 3-column grid
  searchTerm: string = ""
): Promise<SubscriptionsResult> {
  try {
    return await subscriptionsService.fetchSubscriptions(userId, page, limit, searchTerm);
  } catch (error) {
    console.error('Error in fetchSubscriptions:', error);
    throw error;
  }
}
