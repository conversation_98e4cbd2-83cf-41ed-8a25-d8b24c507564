# System Alerts Table Documentation

## Table Overview

The `system_alerts` table stores critical alerts and monitoring data for the Dukancard application. It serves as a centralized logging system for system health issues, subscription inconsistencies, webhook failures, and other critical events that require administrator attention.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key, unique alert identifier |
| alert_type | text | NO | | Type of alert (e.g., 'SUBSCRIPTION_INCONSISTENCY', 'HIGH_FAILURE_RATE') |
| severity | text | NO | | Alert severity level ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') |
| message | text | NO | | Human-readable alert message |
| entity_id | text | YES | | ID of the related entity (business_profile_id, user_id, etc.) |
| subscription_id | text | YES | | Related subscription ID if applicable |
| metadata | jsonb | YES | | Additional alert data and context |
| resolved | boolean | YES | false | Whether the alert has been resolved |
| resolved_at | timestamptz | YES | | Timestamp when the alert was resolved |
| created_at | timestamptz | YES | now() | Timestamp when the alert was created |
| updated_at | timestamptz | YES | now() | Timestamp when the alert was last updated |

## Constraints

### Primary Key
- `system_alerts_pkey` - Primary key constraint on the `id` column

### Check Constraints
- `system_alerts_severity_check` - Ensures severity is one of: 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'

### Not Null Constraints
- `alert_type` must not be null
- `severity` must not be null
- `message` must not be null

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| system_alerts_pkey | UNIQUE | id | Primary key index |
| idx_system_alerts_created_at | BTREE | created_at | Time-based queries and cleanup |
| idx_system_alerts_severity | BTREE | severity | Filtering by severity level |
| idx_system_alerts_resolved | BTREE | resolved | Filtering resolved/unresolved alerts |
| idx_system_alerts_alert_type | BTREE | alert_type | Filtering by alert type |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Service role can manage system alerts | ALL | auth.role() = 'service_role' | |

This policy ensures that only the service role (backend application) can access and modify system alerts. Regular users cannot view or interact with this table.

## Alert Types

### SUBSCRIPTION_INCONSISTENCY
- **Description**: Inconsistencies between `payment_subscriptions` and `business_profiles` tables
- **Severity**: Usually HIGH
- **Metadata**: Contains subscription details, inconsistency type, and affected tables

### HIGH_FAILURE_RATE
- **Description**: Webhook processing failure rate exceeds acceptable thresholds
- **Severity**: HIGH to CRITICAL depending on failure rate
- **Metadata**: Contains failure statistics, time period, and affected services

### PAYMENT_FAILURE
- **Description**: Payment processing failures or issues
- **Severity**: MEDIUM to HIGH
- **Metadata**: Contains payment details, error codes, and retry information

### TERMINAL_STATE_VIOLATION
- **Description**: Violations of subscription terminal state rules
- **Severity**: HIGH to CRITICAL
- **Metadata**: Contains subscription state information and violation details

## Severity Levels

### LOW
- Minor issues that don't affect functionality
- Informational alerts for monitoring
- Performance degradation within acceptable limits

### MEDIUM
- Issues that may affect some users
- Non-critical functionality problems
- Performance issues requiring attention

### HIGH
- Issues that affect many users or core functionality
- Data consistency problems
- Service degradation

### CRITICAL
- Issues that affect all users or could cause data loss
- System-wide failures
- Security breaches or data corruption

## Usage Examples

### Creating Alerts

```typescript
import { createAdminClient } from "@/utils/supabase/admin";

const adminClient = createAdminClient();

// Create a subscription inconsistency alert
await adminClient
  .from('system_alerts')
  .insert({
    alert_type: 'SUBSCRIPTION_INCONSISTENCY',
    severity: 'HIGH',
    message: 'Subscription inconsistency detected for business uuid-123',
    entity_id: 'uuid-123-456-789',
    subscription_id: 'sub_abc123',
    metadata: {
      subscription_status: 'active',
      has_active_subscription: false,
      inconsistency_type: 'should_be_active'
    }
  });
```

### Querying Alerts

```typescript
// Get unresolved critical alerts
const { data: criticalAlerts } = await adminClient
  .from('system_alerts')
  .select('*')
  .eq('severity', 'CRITICAL')
  .eq('resolved', false)
  .order('created_at', { ascending: false });

// Get alerts for a specific entity
const { data: entityAlerts } = await adminClient
  .from('system_alerts')
  .select('*')
  .eq('entity_id', 'uuid-123-456-789')
  .order('created_at', { ascending: false });
```

### Resolving Alerts

```typescript
// Mark alert as resolved
await adminClient
  .from('system_alerts')
  .update({
    resolved: true,
    resolved_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  })
  .eq('id', 'alert-uuid');
```

## Integration with Monitoring System

The `system_alerts` table is integrated with the monitoring utilities in `lib/razorpay/webhooks/monitoring.ts`:

```typescript
import { logCriticalAlert } from "@/lib/razorpay/webhooks/monitoring";

// Automatically log critical alerts
await logCriticalAlert({
  type: 'SUBSCRIPTION_INCONSISTENCY',
  severity: 'HIGH',
  message: 'Subscription inconsistency detected',
  entity_id: 'business-id',
  subscription_id: 'sub-id',
  metadata: { /* additional data */ }
});
```

## Cleanup and Maintenance

### Automated Cleanup
Consider implementing a cleanup function to remove old resolved alerts:

```sql
-- Example cleanup function (not implemented yet)
DELETE FROM system_alerts 
WHERE resolved = true 
  AND resolved_at < NOW() - INTERVAL '90 days';
```

### Monitoring
- Monitor alert volume and patterns
- Set up notifications for CRITICAL alerts
- Regular review of unresolved alerts
- Performance monitoring of alert queries

## Best Practices

1. **Alert Hygiene**: Regularly resolve alerts that have been addressed
2. **Severity Guidelines**: Use appropriate severity levels to avoid alert fatigue
3. **Metadata**: Include relevant context in the metadata field for debugging
4. **Monitoring**: Set up automated monitoring for critical alert types
5. **Cleanup**: Implement regular cleanup of old resolved alerts

## Related Tables

### Monitoring Functions
- `find_subscription_inconsistencies()` - Generates SUBSCRIPTION_INCONSISTENCY alerts
- `get_webhook_error_stats()` - Used to generate HIGH_FAILURE_RATE alerts
- `get_subscription_health_metrics()` - Overall system health monitoring

### Related Tables
- `processed_webhook_events` - Source of webhook failure data
- `payment_subscriptions` - Source of subscription inconsistency data
- `business_profiles` - Related to subscription inconsistencies

## Security Considerations

1. **Access Control**: Only service role can access this table
2. **Sensitive Data**: Avoid storing sensitive information in metadata
3. **Audit Trail**: Alerts provide an audit trail of system issues
4. **Data Retention**: Consider data retention policies for compliance

## Troubleshooting

### Common Issues

1. **High Alert Volume**: 
   - Review alert generation logic
   - Adjust severity thresholds
   - Implement alert deduplication

2. **Missing Alerts**:
   - Check monitoring function execution
   - Verify alert generation logic
   - Review error logs

3. **Performance Issues**:
   - Monitor index usage
   - Optimize queries
   - Consider partitioning for large datasets

### Emergency Procedures

1. **Critical Alerts**: Immediate investigation and resolution
2. **System Overload**: Temporary alert suppression if needed
3. **Data Corruption**: Backup and recovery procedures
