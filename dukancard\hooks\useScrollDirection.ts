"use client";

import { useState, useEffect } from 'react';

interface UseScrollDirectionOptions {
  threshold?: number;
  initialDirection?: 'up' | 'down';
}

interface ScrollState {
  scrollDirection: 'up' | 'down';
  isScrolled: boolean;
  scrollY: number;
}

export function useScrollDirection(options: UseScrollDirectionOptions = {}): ScrollState {
  const { threshold = 10, initialDirection = 'up' } = options;
  
  const [scrollState, setScrollState] = useState<ScrollState>({
    scrollDirection: initialDirection,
    isScrolled: false,
    scrollY: 0,
  });

  useEffect(() => {
    let lastScrollY = window.scrollY;
    let ticking = false;

    const updateScrollDirection = () => {
      const scrollY = window.scrollY;
      const direction = scrollY > lastScrollY ? 'down' : 'up';
      const isScrolled = scrollY > threshold;

      // Only update if the scroll direction has changed or crossed the threshold
      if (
        direction !== scrollState.scrollDirection ||
        Math.abs(scrollY - lastScrollY) > threshold ||
        isScrolled !== scrollState.isScrolled
      ) {
        setScrollState({
          scrollDirection: direction,
          isScrolled,
          scrollY,
        });
      }

      lastScrollY = scrollY > 0 ? scrollY : 0;
      ticking = false;
    };

    const onScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollDirection);
        ticking = true;
      }
    };

    window.addEventListener('scroll', onScroll);

    return () => window.removeEventListener('scroll', onScroll);
  }, [scrollState.scrollDirection, scrollState.isScrolled, threshold]);

  return scrollState;
}
