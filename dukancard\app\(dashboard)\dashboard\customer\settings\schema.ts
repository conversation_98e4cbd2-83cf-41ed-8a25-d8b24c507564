import { z } from "zod";

export const CustomerProfileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  // Email update will be handled separately via Supabase Auth flow
});

export const CustomerEmailSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email." }),
});

export const VerifyEmailOTPSchema = z.object({
  email: z.string().email('Invalid email address.'),
  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),
});

export const CustomerAddressSchema = z.object({
  address: z
    .string()
    .max(100, { message: "Address cannot exceed 100 characters." })
    .optional()
    .or(z.literal("")),
  pincode: z
    .string()
    .min(1, { message: "Pincode is required" })
    .regex(/^\d{6}$/, { message: "Must be a valid 6-digit pincode" }),
  city: z
    .string()
    .min(1, { message: "City is required" })
    .refine((val) => val.trim().length > 0, { message: "City cannot be empty" }),
  state: z
    .string()
    .min(1, { message: "State is required" })
    .refine((val) => val.trim().length > 0, { message: "State cannot be empty" }),
  locality: z
    .string()
    .min(1, { message: "Locality is required" })
    .refine((val) => val.trim().length > 0, { message: "Locality cannot be empty" }),
});


