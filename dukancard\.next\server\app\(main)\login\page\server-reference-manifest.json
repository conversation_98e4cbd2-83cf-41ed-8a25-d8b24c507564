{"node": {"40c4645d03ce93888084da699909d1cb8ed4c08028": {"workers": {"app/(main)/login/page": {"moduleId": "[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/login/page": "action-browser"}}, "40c382d01f32ba231e3b87edecb6a907b27fe5705e": {"workers": {"app/(main)/login/page": {"moduleId": "[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/login/page": "action-browser"}}, "40476f0ade05992429e5520119c8940bc9ac50a28d": {"workers": {"app/(main)/login/page": {"moduleId": "[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(main)/login/page": "action-browser"}}}, "edge": {}}