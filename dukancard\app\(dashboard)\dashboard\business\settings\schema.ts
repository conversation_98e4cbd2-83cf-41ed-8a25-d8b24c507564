import { z } from "zod";

// Schema for updating email
export const EmailSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
});

// Schema for linking email
export const LinkEmailSchema = z.object({
  email: z.string().email('Invalid email address.'),
});

// Schema for verifying email OTP
export const VerifyEmailOTPSchema = z.object({
  email: z.string().email('Invalid email address.'),
  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),
});


