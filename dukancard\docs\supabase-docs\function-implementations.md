# Supabase Function Implementations

This document provides the actual SQL implementations of key functions in the Dukancard Supabase project. These implementations can serve as reference for understanding the database logic in detail.

## Table of Contents

1. [Visit Analytics Functions](#visit-analytics-functions)
2. [Webhook Handling Functions](#webhook-handling-functions)
3. [Custom Ads Functions](#custom-ads-functions)
4. [User Activity Functions](#user-activity-functions)
5. [Product Limit Functions](#product-limit-functions)
6. [Trial Management Functions](#trial-management-functions)
7. [Product Variants Functions](#product-variants-functions)
8. [Location Services Functions](#location-services-functions)

## Visit Analytics Functions

### reset_daily_visit_counts()

```sql
CREATE OR REPLACE FUNCTION public.reset_daily_visit_counts()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  profile_record RECORD;
  ist_latest_date date;
  ist_today date;
  ist_yesterday date;
  today_count integer;
  yesterday_count integer;
BEGIN
  -- Loop through all business profiles
  FOR profile_record IN SELECT id FROM public.business_profiles LOOP
    -- Find the most recent visit date in IST
    SELECT MAX(date(visited_at AT TIME ZONE 'Asia/Kolkata'))
    INTO ist_latest_date
    FROM public.card_visits
    WHERE business_profile_id = profile_record.id;

    -- If no visits, use current date
    IF ist_latest_date IS NULL THEN
      ist_latest_date := date(NOW() AT TIME ZONE 'Asia/Kolkata');
    END IF;

    -- Set today as the latest visit date
    ist_today := ist_latest_date;
    ist_yesterday := ist_today - interval '1 day';

    -- Count today's unique visits (based on IST)
    SELECT COUNT(DISTINCT visitor_identifier) INTO today_count
    FROM public.card_visits
    WHERE business_profile_id = profile_record.id
      AND date(visited_at AT TIME ZONE 'Asia/Kolkata') = ist_today;

    -- Count yesterday's unique visits (based on IST)
    SELECT COUNT(DISTINCT visitor_identifier) INTO yesterday_count
    FROM public.card_visits
    WHERE business_profile_id = profile_record.id
      AND date(visited_at AT TIME ZONE 'Asia/Kolkata') = ist_yesterday;

    -- Update the business_profiles table
    UPDATE public.business_profiles
    SET
      today_visits = today_count,
      yesterday_visits = yesterday_count
    WHERE id = profile_record.id;
  END LOOP;

  RETURN;
END;
$function$
```

### update_period_visit_counts()

```sql
CREATE OR REPLACE FUNCTION public.update_period_visit_counts()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  profile_record RECORD;
  ist_latest_date date;
  ist_today date;
  ist_7_days_ago date;
  ist_30_days_ago date;
  days_7_count integer;
  days_30_count integer;
BEGIN
  -- Loop through all business profiles
  FOR profile_record IN SELECT id FROM public.business_profiles LOOP
    -- Find the most recent visit date in IST
    SELECT MAX(date(visited_at AT TIME ZONE 'Asia/Kolkata'))
    INTO ist_latest_date
    FROM public.card_visits
    WHERE business_profile_id = profile_record.id;

    -- If no visits, use current date
    IF ist_latest_date IS NULL THEN
      ist_latest_date := date(NOW() AT TIME ZONE 'Asia/Kolkata');
    END IF;

    -- Set today as the latest visit date
    ist_today := ist_latest_date;
    ist_7_days_ago := ist_today - interval '7 days';
    ist_30_days_ago := ist_today - interval '30 days';

    -- Count last 7 days unique visits (based on IST)
    SELECT COUNT(DISTINCT visitor_identifier) INTO days_7_count
    FROM public.card_visits
    WHERE business_profile_id = profile_record.id
      AND date(visited_at AT TIME ZONE 'Asia/Kolkata') >= ist_7_days_ago
      AND date(visited_at AT TIME ZONE 'Asia/Kolkata') <= ist_today;

    -- Count last 30 days unique visits (based on IST)
    SELECT COUNT(DISTINCT visitor_identifier) INTO days_30_count
    FROM public.card_visits
    WHERE business_profile_id = profile_record.id
      AND date(visited_at AT TIME ZONE 'Asia/Kolkata') >= ist_30_days_ago
      AND date(visited_at AT TIME ZONE 'Asia/Kolkata') <= ist_today;

    -- Update the business_profiles table
    UPDATE public.business_profiles
    SET
      visits_7_days = days_7_count,
      visits_30_days = days_30_count
    WHERE id = profile_record.id;
  END LOOP;

  RETURN;
END;
$function$
```

## Webhook Handling Functions

### handle_webhook_event()

```sql
CREATE OR REPLACE FUNCTION public.handle_webhook_event(
  event_id text,
  event_type text,
  event_data jsonb,
  created_at timestamp with time zone DEFAULT now()
)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  status_text text := 'processed';
  error_message text := NULL;
BEGIN
  -- Insert the event into the processed_webhook_events table
  INSERT INTO processed_webhook_events (
    event_id,
    event_type,
    event_data,
    status,
    processed_at,
    error_message
  )
  VALUES (
    event_id,
    event_type,
    event_data,
    'processing',
    created_at,
    NULL
  )
  ON CONFLICT (event_id)
  DO UPDATE SET
    status = 'retrying',
    processed_at = created_at,
    error_message = NULL;

  -- Process the event based on its type
  BEGIN
    CASE
      WHEN event_type = 'subscription.charged' THEN
        PERFORM handle_subscription_charged(event_data);
      WHEN event_type = 'subscription.cancelled' THEN
        PERFORM handle_subscription_cancelled(event_data);
      WHEN event_type = 'refund.processed' THEN
        PERFORM handle_refund_processed(event_data);
      WHEN event_type = 'payment.failed' THEN
        -- Currently not handling payment.failed events
        NULL;
      WHEN event_type = 'payment_link.paid' THEN
        -- Currently not handling payment_link.paid events
        NULL;
      WHEN event_type = 'payment.authorized' THEN
        -- Currently not handling payment.authorized events
        NULL;
      WHEN event_type = 'payment.captured' THEN
        -- Currently not handling payment.captured events
        NULL;
      WHEN event_type = 'payment_method.updated' THEN
        PERFORM handle_payment_method_updated(event_data);
      ELSE
        -- Unknown event type
        status_text := 'ignored';
        error_message := 'Unknown event type: ' || event_type;
    END CASE;
  EXCEPTION
    WHEN OTHERS THEN
      status_text := 'failed';
      error_message := SQLERRM;
  END;

  -- Update the event status
  UPDATE processed_webhook_events
  SET
    status = status_text,
    processed_at = NOW(),
    error_message = error_message
  WHERE event_id = event_id;

  RETURN;
END;
$function$
```

### handle_subscription_charged() - DEPRECATED

**⚠️ DEPRECATED**: This function is no longer used. The current implementation uses the `update_subscription_atomic()` RPC function and centralized webhook handlers.

**SECURITY ISSUE FIXED**: The old implementation incorrectly set `trial_end_date = NULL` which created a trial exploitation vulnerability. The new implementation preserves `trial_end_date` to prevent users from repeatedly starting and canceling trials.

```sql
-- OLD IMPLEMENTATION (DEPRECATED - DO NOT USE)
-- This function had a security vulnerability where it set trial_end_date = NULL
-- allowing users to exploit the trial system by repeatedly canceling and restarting trials

-- CURRENT IMPLEMENTATION:
-- Uses update_subscription_atomic() RPC function with proper trial_end_date preservation
-- See: lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionCharged.ts
```

### handle_subscription_cancelled()

```sql
CREATE OR REPLACE FUNCTION public.handle_subscription_cancelled(
  user_id uuid,
  subscription_id text,
  event_id text
)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Update the business profile
  UPDATE business_profiles
  SET
    has_active_subscription = FALSE,
    razorpay_subscription_id = NULL,
    subscription_end_date = NOW()
  WHERE id = user_id;

  -- Insert the event ID for idempotency
  INSERT INTO processed_webhook_events (event_id, processed_at)
  VALUES (event_id, NOW())
  ON CONFLICT (event_id) DO NOTHING;

  RETURN;
END;
$function$
```

### handle_refund_processed()

```sql
CREATE OR REPLACE FUNCTION public.handle_refund_processed(
  user_id uuid,
  payment_id text,
  event_id text
)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Update the business profile
  UPDATE business_profiles
  SET
    refund_processed_at = NOW()
  WHERE id = user_id AND last_payment_id = payment_id;

  -- Insert the event ID for idempotency
  INSERT INTO processed_webhook_events (event_id, processed_at)
  VALUES (event_id, NOW())
  ON CONFLICT (event_id) DO NOTHING;

  RETURN;
END;
$function$
```

### handle_payment_method_updated()

```sql
CREATE OR REPLACE FUNCTION public.handle_payment_method_updated(
  user_id uuid,
  subscription_id text,
  payment_id text,
  event_id text
)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Update the business profile
  UPDATE business_profiles
  SET
    last_payment_id = payment_id
  WHERE id = user_id AND razorpay_subscription_id = subscription_id;

  -- Insert the event ID for idempotency
  INSERT INTO processed_webhook_events (event_id, processed_at)
  VALUES (event_id, NOW())
  ON CONFLICT (event_id) DO NOTHING;

  RETURN;
END;
$function$
```

## Custom Ads Functions

### add_custom_ad()

```sql
CREATE OR REPLACE FUNCTION public.add_custom_ad(
  p_ad_image_url text,
  p_ad_link_url text,
  p_is_active boolean,
  p_is_global boolean,
  p_expiry_date timestamp with time zone DEFAULT NULL::timestamp with time zone,
  p_pincodes text[] DEFAULT NULL::text[]
)
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
DECLARE
  v_ad_id UUID;
  v_pincode TEXT;
BEGIN
  -- Validate inputs
  IF p_is_global = FALSE AND (p_pincodes IS NULL OR array_length(p_pincodes, 1) = 0) THEN
    RAISE EXCEPTION 'Pincodes must be provided for non-global ads';
  END IF;

  -- Insert the ad
  INSERT INTO custom_ads (ad_image_url, ad_link_url, is_active, expiry_date)
  VALUES (
    p_ad_image_url,
    p_ad_link_url,
    p_is_active,
    p_expiry_date
  )
  RETURNING id INTO v_ad_id;

  -- Insert targeting information
  IF p_is_global THEN
    -- Global ad
    INSERT INTO custom_ad_targets (ad_id, pincode, is_global)
    VALUES (v_ad_id, '000000', TRUE);
  ELSE
    -- Pincode-specific ad
    FOREACH v_pincode IN ARRAY p_pincodes LOOP
      INSERT INTO custom_ad_targets (ad_id, pincode, is_global)
      VALUES (v_ad_id, v_pincode, FALSE);
    END LOOP;
  END IF;

  RETURN v_ad_id;
END;
$function$
```

### get_ad_for_pincode()

```sql
CREATE OR REPLACE FUNCTION public.get_ad_for_pincode(target_pincode character varying)
 RETURNS TABLE(id uuid, ad_image_url text, ad_link_url text, is_global boolean, expiry_date timestamp with time zone)
 LANGUAGE sql
AS $function$
  -- Try to find a pincode-specific ad first
  WITH pincode_ad AS (
    SELECT
      c.id,
      c.ad_image_url,
      c.ad_link_url,
      FALSE AS is_global,
      c.expiry_date
    FROM custom_ads c
    JOIN custom_ad_targets t ON c.id = t.ad_id
    WHERE c.is_active = TRUE
    AND t.pincode = target_pincode
    AND t.is_global = FALSE
    AND (c.expiry_date IS NULL OR c.expiry_date > NOW())
    ORDER BY c.created_at DESC
    LIMIT 1
  ),
  -- If no pincode-specific ad, try global ads
  global_ad AS (
    SELECT
      c.id,
      c.ad_image_url,
      c.ad_link_url,
      TRUE AS is_global,
      c.expiry_date
    FROM custom_ads c
    JOIN custom_ad_targets t ON c.id = t.ad_id
    WHERE c.is_active = TRUE
    AND t.is_global = TRUE
    AND (c.expiry_date IS NULL OR c.expiry_date > NOW())
    AND NOT EXISTS (SELECT 1 FROM pincode_ad)
    ORDER BY c.created_at DESC
    LIMIT 1
  )
  -- Return pincode-specific ad if found, otherwise global ad
  SELECT * FROM pincode_ad
  UNION ALL
  SELECT * FROM global_ad;
$function$
```

## User Activity Functions

### add_like_activity()

```sql
CREATE OR REPLACE FUNCTION public.add_like_activity()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Insert a new activity record
  INSERT INTO business_activities (
    business_profile_id,
    user_id,
    activity_type,
    created_at
  ) VALUES (
    NEW.business_profile_id,
    NEW.user_id,
    'like',
    NEW.created_at
  );

  RETURN NEW;
END;
$function$
```

### delete_like_activity()

```sql
CREATE OR REPLACE FUNCTION public.delete_like_activity()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Delete the activity record
  DELETE FROM business_activities
  WHERE business_profile_id = OLD.business_profile_id
  AND user_id = OLD.user_id
  AND activity_type = 'like';

  RETURN OLD;
END;
$function$
```

### update_total_likes()

```sql
CREATE OR REPLACE FUNCTION public.update_total_likes()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  total_count integer;
  profile_id uuid;
BEGIN
  -- Determine the business_profile_id based on the operation type
  IF (TG_OP = 'DELETE') THEN
    profile_id := OLD.business_profile_id;
  ELSE
    profile_id := NEW.business_profile_id;
  END IF;

  -- Count the total likes for the specific business profile
  SELECT COUNT(*) INTO total_count
  FROM public.likes
  WHERE business_profile_id = profile_id;

  -- Update the business_profiles table
  UPDATE public.business_profiles
  SET total_likes = total_count
  WHERE id = profile_id;

  RETURN NULL; -- Result is ignored since this is an AFTER trigger
END;
$function$
```

### add_rating_activity()

```sql
CREATE OR REPLACE FUNCTION public.add_rating_activity()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Check if this is an update or insert
  IF TG_OP = 'UPDATE' THEN
    -- For updates, only add activity if rating changed
    IF NEW.rating = OLD.rating THEN
      RETURN NEW;
    END IF;
  END IF;

  -- Insert a new activity record
  INSERT INTO business_activities (
    business_profile_id,
    user_id,
    activity_type,
    rating_value,
    created_at
  ) VALUES (
    NEW.business_profile_id,
    NEW.user_id,
    'rating',
    NEW.rating,
    NEW.updated_at
  );

  RETURN NEW;
END;
$function$
```

### delete_rating_activity()

```sql
CREATE OR REPLACE FUNCTION public.delete_rating_activity()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Delete the activity record
  DELETE FROM business_activities
  WHERE business_profile_id = OLD.business_profile_id
  AND user_id = OLD.user_id
  AND activity_type = 'rating';

  RETURN OLD;
END;
$function$
```

### update_average_rating()

```sql
CREATE OR REPLACE FUNCTION public.update_average_rating()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  avg_rating numeric;
  profile_id uuid;
BEGIN
  -- Determine the business_profile_id based on the operation type
  IF (TG_OP = 'DELETE') THEN
    profile_id := OLD.business_profile_id;
  ELSE
    profile_id := NEW.business_profile_id;
  END IF;

  -- Calculate the new average rating for the specific business profile
  SELECT AVG(rating)::numeric(2,1) INTO avg_rating
  FROM public.ratings_reviews
  WHERE business_profile_id = profile_id;

  -- Update the business_profiles table
  UPDATE public.business_profiles
  SET average_rating = COALESCE(avg_rating, 0.0) -- Set to 0.0 if no ratings exist
  WHERE id = profile_id;

  RETURN NULL; -- Result is ignored since this is an AFTER trigger
END;
$function$
```

### add_subscription_activity()

```sql
CREATE OR REPLACE FUNCTION public.add_subscription_activity()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Insert a new activity record
  INSERT INTO business_activities (
    business_profile_id,
    user_id,
    activity_type,
    created_at
  ) VALUES (
    NEW.business_profile_id,
    NEW.user_id,
    'subscribe',
    NEW.created_at
  );

  RETURN NEW;
END;
$function$
```

### delete_subscription_activity()

```sql
CREATE OR REPLACE FUNCTION public.delete_subscription_activity()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Delete the activity record
  DELETE FROM business_activities
  WHERE business_profile_id = OLD.business_profile_id
  AND user_id = OLD.user_id
  AND activity_type = 'subscribe';

  RETURN OLD;
END;
$function$
```

### update_total_subscriptions()

```sql
CREATE OR REPLACE FUNCTION public.update_total_subscriptions()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  total_count integer;
  profile_id uuid;
BEGIN
  -- Determine the business_profile_id based on the operation type
  IF (TG_OP = 'DELETE') THEN
    profile_id := OLD.business_profile_id;
  ELSE
    profile_id := NEW.business_profile_id;
  END IF;

  -- Count the total subscriptions for the specific business profile
  SELECT COUNT(*) INTO total_count
  FROM public.subscriptions
  WHERE business_profile_id = profile_id;

  -- Update the business_profiles table
  UPDATE public.business_profiles
  SET total_subscriptions = total_count
  WHERE id = profile_id;

  RETURN NULL; -- Result is ignored since this is an AFTER trigger
END;
$function$
```

### generate_product_slug()

```sql
CREATE OR REPLACE FUNCTION public.generate_product_slug()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  base_slug TEXT;
  new_slug TEXT;
  counter INT := 1;
  slug_exists BOOLEAN;
BEGIN
  -- Convert the name to lowercase and replace spaces and special chars with hyphens
  base_slug := LOWER(REGEXP_REPLACE(NEW.name, '[^a-zA-Z0-9]', '-', 'g'));

  -- Remove consecutive hyphens
  base_slug := REGEXP_REPLACE(base_slug, '-+', '-', 'g');

  -- Remove leading and trailing hyphens
  base_slug := TRIM(BOTH '-' FROM base_slug);

  -- Initial slug attempt
  new_slug := base_slug;

  -- Check if the slug already exists
  LOOP
    EXECUTE 'SELECT EXISTS(SELECT 1 FROM products_services WHERE slug = $1 AND id != $2)'
    INTO slug_exists
    USING new_slug, NEW.id;

    EXIT WHEN NOT slug_exists;

    -- If it exists, append a counter
    counter := counter + 1;
    new_slug := base_slug || '-' || counter;
  END LOOP;

  -- Set the new slug
  NEW.slug := new_slug;

  RETURN NEW;
END;
$function$
```

## Visit Analytics Functions (Additional)

### get_daily_unique_visit_trend()

```sql
CREATE OR REPLACE FUNCTION public.get_daily_unique_visit_trend(business_id uuid, start_date date, end_date date)
 RETURNS TABLE(date text, visits bigint)
 LANGUAGE sql
 STABLE
AS $function$
  SELECT
      to_char(date(v.visited_at AT TIME ZONE 'Asia/Kolkata'), 'YYYY-MM-DD') as date,
      COUNT(DISTINCT v.visitor_identifier) as visits
  FROM public.card_visits v
  WHERE v.business_profile_id = business_id
    AND date(v.visited_at AT TIME ZONE 'Asia/Kolkata') >= start_date
    AND date(v.visited_at AT TIME ZONE 'Asia/Kolkata') <= end_date
  GROUP BY date(v.visited_at AT TIME ZONE 'Asia/Kolkata')
  ORDER BY date(v.visited_at AT TIME ZONE 'Asia/Kolkata') ASC;
$function$
```

### get_hourly_unique_visit_trend()

```sql
CREATE OR REPLACE FUNCTION public.get_hourly_unique_visit_trend(business_id uuid, target_date date)
 RETURNS TABLE(hour integer, visits bigint)
 LANGUAGE sql
 STABLE
AS $function$
  SELECT
      EXTRACT(HOUR FROM v.visited_at AT TIME ZONE 'Asia/Kolkata')::integer as hour,
      COUNT(DISTINCT v.visitor_identifier) as visits
  FROM public.card_visits v
  WHERE v.business_profile_id = business_id
    AND date(v.visited_at AT TIME ZONE 'Asia/Kolkata') = target_date
  GROUP BY EXTRACT(HOUR FROM v.visited_at AT TIME ZONE 'Asia/Kolkata')
  ORDER BY hour ASC;
$function$
```

### get_monthly_unique_visit_trend()

```sql
CREATE OR REPLACE FUNCTION public.get_monthly_unique_visit_trend(
  business_id uuid,
  start_year integer,
  start_month integer,
  end_year integer,
  end_month integer
)
 RETURNS TABLE(year integer, month integer, visits integer)
 LANGUAGE plpgsql
AS $function$
DECLARE
  current_year INTEGER := start_year;
  current_month INTEGER := start_month;
BEGIN
  -- Loop through each month in the range
  WHILE (current_year < end_year OR (current_year = end_year AND current_month <= end_month)) LOOP
    -- Get the visit count for this month
    year := current_year;
    month := current_month;
    visits := get_monthly_unique_visits(business_id, current_year, current_month);

    -- Return this row
    RETURN NEXT;

    -- Move to the next month
    current_month := current_month + 1;
    IF current_month > 12 THEN
      current_month := 1;
      current_year := current_year + 1;
    END IF;
  END LOOP;

  RETURN;
END;
$function$
```

### get_monthly_unique_visits()

```sql
CREATE OR REPLACE FUNCTION public.get_monthly_unique_visits(
  business_id uuid,
  target_year integer,
  target_month integer
)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  visit_count INTEGER;
BEGIN
  -- Try to get the pre-aggregated count from monthly_visit_metrics
  SELECT unique_visits INTO visit_count
  FROM monthly_visit_metrics
  WHERE business_profile_id = business_id
  AND year = target_year
  AND month = target_month;

  -- If no record exists, calculate it directly from card_visits
  IF visit_count IS NULL THEN
    SELECT COUNT(DISTINCT visitor_identifier) INTO visit_count
    FROM card_visits
    WHERE business_profile_id = business_id
    AND EXTRACT(YEAR FROM (visited_at AT TIME ZONE 'Asia/Kolkata')::DATE) = target_year
    AND EXTRACT(MONTH FROM (visited_at AT TIME ZONE 'Asia/Kolkata')::DATE) = target_month;

    -- Insert the calculated value into monthly_visit_metrics for future use
    IF visit_count > 0 THEN
      INSERT INTO monthly_visit_metrics (
        business_profile_id,
        year,
        month,
        unique_visits
      )
      VALUES (
        business_id,
        target_year,
        target_month,
        visit_count
      )
      ON CONFLICT (business_profile_id, year, month)
      DO UPDATE SET
        unique_visits = visit_count,
        updated_at = now();
    END IF;
  END IF;

  -- Return the visit count (or 0 if null)
  RETURN COALESCE(visit_count, 0);
END;
$function$
```

### get_total_unique_visits()

```sql
CREATE OR REPLACE FUNCTION public.get_total_unique_visits(business_id uuid)
 RETURNS bigint
 LANGUAGE sql
 STABLE
AS $function$
  SELECT COUNT(DISTINCT visitor_identifier)::bigint
  FROM public.card_visits
  WHERE business_profile_id = business_id;
$function$
```

### update_visit_counts()

```sql
CREATE OR REPLACE FUNCTION public.update_visit_counts()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  profile_id uuid;
  ist_latest_date date;
  ist_today date;
  ist_yesterday date;
  ist_7_days_ago date;
  ist_30_days_ago date;
  total_count integer;
  today_count integer;
  yesterday_count integer;
  days_7_count integer;
  days_30_count integer;
BEGIN
  -- Get the business profile ID
  profile_id := NEW.business_profile_id;

  -- Find the most recent visit date in IST
  SELECT MAX(date(visited_at AT TIME ZONE 'Asia/Kolkata'))
  INTO ist_latest_date
  FROM public.card_visits
  WHERE business_profile_id = profile_id;

  -- If no visits, use current date
  IF ist_latest_date IS NULL THEN
    ist_latest_date := date(NOW() AT TIME ZONE 'Asia/Kolkata');
  END IF;

  -- Set today as the latest visit date
  ist_today := ist_latest_date;
  ist_yesterday := ist_today - interval '1 day';
  ist_7_days_ago := ist_today - interval '7 days';
  ist_30_days_ago := ist_today - interval '30 days';

  -- Count total unique visits
  SELECT COUNT(DISTINCT visitor_identifier) INTO total_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id;

  -- Count today's unique visits (based on IST)
  SELECT COUNT(DISTINCT visitor_identifier) INTO today_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') = ist_today;

  -- Count yesterday's unique visits (based on IST)
  SELECT COUNT(DISTINCT visitor_identifier) INTO yesterday_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') = ist_yesterday;

  -- Count last 7 days unique visits (based on IST)
  SELECT COUNT(DISTINCT visitor_identifier) INTO days_7_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') >= ist_7_days_ago
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') <= ist_today;

  -- Count last 30 days unique visits (based on IST)
  SELECT COUNT(DISTINCT visitor_identifier) INTO days_30_count
  FROM public.card_visits
  WHERE business_profile_id = profile_id
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') >= ist_30_days_ago
    AND date(visited_at AT TIME ZONE 'Asia/Kolkata') <= ist_today;

  -- Update the business_profiles table
  UPDATE public.business_profiles
  SET
    total_visits = total_count,
    today_visits = today_count,
    yesterday_visits = yesterday_count,
    visits_7_days = days_7_count,
    visits_30_days = days_30_count
  WHERE id = profile_id;

  RETURN NULL; -- Result is ignored since this is an AFTER trigger
END;
$function$
```

## Utility Functions

### get_available_years_for_monthly_metrics()

```sql
CREATE OR REPLACE FUNCTION public.get_available_years_for_monthly_metrics(business_id uuid)
 RETURNS TABLE(year integer)
 LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  SELECT DISTINCT m.year
  FROM monthly_visit_metrics m
  WHERE m.business_profile_id = business_id
  ORDER BY m.year DESC;
END;
$function$
```

### get_clicks_by_type()

```sql
CREATE OR REPLACE FUNCTION public.get_clicks_by_type(business_id uuid)
 RETURNS TABLE(click_type text, count bigint)
 LANGUAGE sql
 STABLE
AS $function$
  SELECT
    cc.click_type,
    COUNT(*) as count
  FROM public.card_clicks cc
  WHERE cc.business_profile_id = get_clicks_by_type.business_id
  GROUP BY cc.click_type
  ORDER BY count DESC;
$function$
```

### get_distinct_cities()

```sql
CREATE OR REPLACE FUNCTION public.get_distinct_cities(search_query text, result_limit integer)
 RETURNS TABLE(city text)
 LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  SELECT DISTINCT "DivisionName" as city
  FROM pincodes
  WHERE "DivisionName" ILIKE search_query
  ORDER BY "DivisionName"
  LIMIT result_limit;
END;
$function$
```

### check_global_target()

```sql
CREATE OR REPLACE FUNCTION public.check_global_target()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- If this is a global target, check if there's already one for this ad
  IF NEW.is_global = TRUE THEN
    IF EXISTS (
      SELECT 1 FROM custom_ad_targets
      WHERE ad_id = NEW.ad_id
      AND is_global = TRUE
      AND id != NEW.id
    ) THEN
      RAISE EXCEPTION 'Only one global target is allowed per ad';
    END IF;
  END IF;
  RETURN NEW;
END;
$function$
```

## Product Limit Functions

### limit_products_for_plan()

```sql
CREATE OR REPLACE FUNCTION public.limit_products_for_plan(
  p_business_id UUID,
  p_plan_id TEXT
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $function$
DECLARE
  plan_limit INTEGER;
  products_updated INTEGER := 0;
  current_available_count INTEGER;
BEGIN
  -- Determine the plan limit based on plan_id
  CASE p_plan_id
    WHEN 'free' THEN plan_limit := 5;
    WHEN 'basic' THEN plan_limit := 15;
    WHEN 'growth' THEN plan_limit := 50;
    WHEN 'pro' THEN plan_limit := NULL; -- NULL means unlimited
    WHEN 'enterprise' THEN plan_limit := NULL; -- NULL means unlimited
    ELSE plan_limit := 5; -- Default to free plan limit
  END CASE;

  -- If the plan has unlimited products, no need to limit anything
  IF plan_limit IS NULL THEN
    RETURN 0;
  END IF;

  -- Count current available products
  SELECT COUNT(*) INTO current_available_count
  FROM products_services
  WHERE business_id = p_business_id
    AND is_available = TRUE;

  -- If current count is within the limit, no action needed
  IF current_available_count <= plan_limit THEN
    RETURN 0;
  END IF;

  -- Need to disable excess products
  -- Keep the most recently created products available, disable the rest
  WITH business_products AS (
    SELECT id, created_at
    FROM products_services
    WHERE business_id = p_business_id
      AND is_available = TRUE
    ORDER BY created_at DESC
  ),
  products_to_keep AS (
    SELECT id
    FROM business_products
    LIMIT plan_limit
  )
  UPDATE products_services
  SET is_available = FALSE,
      updated_at = NOW()
  WHERE business_id = p_business_id
    AND is_available = TRUE
    AND id NOT IN (SELECT id FROM products_to_keep);

  -- Get the count of products that were updated
  GET DIAGNOSTICS products_updated = ROW_COUNT;

  RETURN products_updated;
END;
$function$
```

### check_product_limit()

```sql
CREATE OR REPLACE FUNCTION public.check_product_limit()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
DECLARE
  business_plan_id TEXT;
  plan_limit INTEGER;
  current_available_count INTEGER;
BEGIN
  -- Skip check if the product is being marked as unavailable
  IF (TG_OP = 'UPDATE' AND NEW.is_available = FALSE) THEN
    RETURN NEW;
  END IF;

  -- Only check when trying to make a product available
  IF NOT ((TG_OP = 'INSERT' AND NEW.is_available = TRUE) OR
          (TG_OP = 'UPDATE' AND OLD.is_available = FALSE AND NEW.is_available = TRUE)) THEN
    RETURN NEW;
  END IF;

  -- Get the business's current plan from payment_subscriptions
  SELECT plan_id INTO business_plan_id
  FROM payment_subscriptions
  WHERE business_profile_id = NEW.business_id
  ORDER BY created_at DESC
  LIMIT 1;

  -- Default to free plan if no subscription found
  IF business_plan_id IS NULL THEN
    business_plan_id := 'free';
  END IF;

  -- Set the plan limit based on the plan_id
  CASE business_plan_id
    WHEN 'free' THEN plan_limit := 5;
    WHEN 'basic' THEN plan_limit := 15;
    WHEN 'growth' THEN plan_limit := 50;
    WHEN 'pro' THEN plan_limit := NULL; -- NULL means unlimited
    WHEN 'enterprise' THEN plan_limit := NULL; -- NULL means unlimited
    ELSE plan_limit := 5; -- Default to free plan limit
  END CASE;

  -- If the plan has unlimited products, allow the operation
  IF plan_limit IS NULL THEN
    RETURN NEW;
  END IF;

  -- Count how many products are currently available for this business
  -- For UPDATE operations, exclude the current product from the count
  SELECT COUNT(*) INTO current_available_count
  FROM products_services
  WHERE business_id = NEW.business_id
    AND is_available = TRUE
    AND (TG_OP = 'INSERT' OR id != NEW.id);

  -- Check if adding this product would exceed the plan limit
  IF current_available_count >= plan_limit THEN
    RAISE EXCEPTION 'Cannot make product available: You have reached the limit of % available products for your % plan. Please upgrade your plan or make another product unavailable first.',
      plan_limit, business_plan_id;
  END IF;

  RETURN NEW;
END;
$function$
```

### enforce_product_limits_on_plan_change()

```sql
CREATE OR REPLACE FUNCTION public.enforce_product_limits_on_plan_change()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
DECLARE
  products_updated INTEGER;
BEGIN
  -- Only process if plan_id has actually changed
  IF OLD.plan_id = NEW.plan_id THEN
    RETURN NEW;
  END IF;

  -- Call the generic function to limit products based on the new plan
  SELECT limit_products_for_plan(NEW.business_profile_id, NEW.plan_id) INTO products_updated;

  -- Log the action
  IF products_updated > 0 THEN
    RAISE NOTICE 'Plan changed from % to % for business %. Limited % products to comply with new plan limit.',
      OLD.plan_id, NEW.plan_id, NEW.business_profile_id, products_updated;
  ELSE
    RAISE NOTICE 'Plan changed from % to % for business %. No product limiting needed.',
      OLD.plan_id, NEW.plan_id, NEW.business_profile_id;
  END IF;

  RETURN NEW;
END;
$function$
```

## Trial Management Functions

### check_and_process_expired_trials()

```sql
CREATE OR REPLACE FUNCTION public.check_and_process_expired_trials()
RETURNS INTEGER
LANGUAGE plpgsql
AS $function$
DECLARE
  processed_count INTEGER := 0;
  now_timestamp TIMESTAMPTZ := NOW();
  dynamic_update_sql TEXT;
  col_record RECORD;
  columns_to_clear TEXT[] := ARRAY[]::TEXT[];
BEGIN
  -- Create a temporary table to store profiles that need to be downgraded
  CREATE TEMP TABLE temp_profiles_to_downgrade (
    id UUID,
    business_name TEXT
  ) ON COMMIT DROP;

  -- Find all business profiles with expired trials and insert into temp table
  INSERT INTO temp_profiles_to_downgrade (id, business_name)
  WITH expired_trials AS (
    SELECT bp.id, bp.business_name
    FROM business_profiles bp
    WHERE bp.trial_end_date IS NOT NULL
      AND bp.trial_end_date < now_timestamp
  ),
  active_subscriptions AS (
    SELECT ps.business_profile_id
    FROM payment_subscriptions ps
    WHERE ps.business_profile_id IN (SELECT id FROM expired_trials)
      AND ps.subscription_status IN ('active', 'authenticated')
  )
  SELECT et.id, et.business_name
  FROM expired_trials et
  WHERE et.id NOT IN (SELECT business_profile_id FROM active_subscriptions);

  -- Update business_profiles
  -- For expired trials moving to free plan, has_active_subscription should be FALSE
  UPDATE business_profiles
  SET
    has_active_subscription = FALSE,
    updated_at = now_timestamp
  WHERE id IN (SELECT id FROM temp_profiles_to_downgrade);

  -- Get the count of processed profiles
  GET DIAGNOSTICS processed_count = ROW_COUNT;

  -- Update payment_subscriptions for the downgraded profiles
  IF processed_count > 0 THEN
    -- Dynamically get all columns except the ones we want to keep
    FOR col_record IN
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'payment_subscriptions'
        AND table_schema = 'public'
        AND column_name NOT IN ('id', 'business_profile_id', 'plan_id', 'plan_cycle', 'subscription_status', 'subscription_start_date', 'created_at', 'updated_at')
        AND is_nullable = 'YES'
      ORDER BY column_name
    LOOP
      columns_to_clear := array_append(columns_to_clear, col_record.column_name || ' = NULL');
    END LOOP;

    -- Build the dynamic UPDATE statement
    dynamic_update_sql := 'UPDATE payment_subscriptions SET ' ||
      'subscription_status = ''active'', ' ||
      'plan_id = ''free'', ' ||
      'plan_cycle = ''monthly'', ' ||
      'subscription_start_date = ''' || now_timestamp || ''', ' ||
      'updated_at = ''' || now_timestamp || '''';

    -- Add the dynamic columns to clear if any exist
    IF array_length(columns_to_clear, 1) > 0 THEN
      dynamic_update_sql := dynamic_update_sql || ', ' || array_to_string(columns_to_clear, ', ');
    END IF;

    -- Add the WHERE clause
    dynamic_update_sql := dynamic_update_sql ||
      ' WHERE business_profile_id IN (SELECT id FROM temp_profiles_to_downgrade) AND subscription_status = ''trial''';

    -- Execute the dynamic UPDATE statement
    EXECUTE dynamic_update_sql;

    -- Note: Product limiting is now automatically handled by the
    -- enforce_product_limits_on_plan_change trigger when plan_id changes to 'free'
    RAISE NOTICE 'Processed % expired trials. Dynamically cleared all nullable columns and product limits automatically enforced by database trigger.', processed_count;
  END IF;

  -- Return the number of processed profiles
  RETURN processed_count;
END;
$function$
```

## Product Variants Functions

These functions handle product variants functionality, allowing businesses to create multiple variations of products.

### get_product_with_variants()

```sql
CREATE OR REPLACE FUNCTION public.get_product_with_variants(product_uuid uuid)
RETURNS TABLE (
    product_id uuid,
    product_name text,
    product_description text,
    product_base_price numeric,
    product_discounted_price numeric,
    product_is_available boolean,
    product_images text[],
    product_featured_image_index integer,
    variant_count bigint,
    variants jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT
        ps.id as product_id,
        ps.name as product_name,
        ps.description as product_description,
        ps.base_price as product_base_price,
        ps.discounted_price as product_discounted_price,
        ps.is_available as product_is_available,
        ps.images as product_images,
        ps.featured_image_index as product_featured_image_index,
        COALESCE(v.variant_count, 0) as variant_count,
        COALESCE(v.variants, '[]'::jsonb) as variants
    FROM public.products_services ps
    LEFT JOIN (
        SELECT
            pv.product_id,
            COUNT(*) as variant_count,
            jsonb_agg(
                jsonb_build_object(
                    'id', pv.id,
                    'variant_name', pv.variant_name,
                    'variant_values', pv.variant_values,
                    'base_price', pv.base_price,
                    'discounted_price', pv.discounted_price,
                    'is_available', pv.is_available,
                    'images', pv.images,
                    'featured_image_index', pv.featured_image_index,
                    'created_at', pv.created_at,
                    'updated_at', pv.updated_at
                ) ORDER BY pv.created_at
            ) as variants
        FROM public.product_variants pv
        GROUP BY pv.product_id
    ) v ON ps.id = v.product_id
    WHERE ps.id = product_uuid;
END;
$function$
```

### get_available_product_variants()

```sql
CREATE OR REPLACE FUNCTION public.get_available_product_variants(product_uuid uuid)
RETURNS TABLE (
    id uuid,
    variant_name text,
    variant_values jsonb,
    base_price numeric,
    discounted_price numeric,
    images text[],
    featured_image_index integer
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT
        pv.id,
        pv.variant_name,
        pv.variant_values,
        pv.base_price,
        pv.discounted_price,
        pv.images,
        pv.featured_image_index
    FROM public.product_variants pv
    JOIN public.products_services ps ON pv.product_id = ps.id
    JOIN public.business_profiles bp ON ps.business_id = bp.id
    WHERE pv.product_id = product_uuid
    AND pv.is_available = true
    AND ps.is_available = true
    AND bp.status = 'online'
    ORDER BY pv.created_at;
END;
$function$
```

### get_business_variant_stats()

```sql
CREATE OR REPLACE FUNCTION public.get_business_variant_stats(business_uuid uuid)
RETURNS TABLE (
    total_products bigint,
    products_with_variants bigint,
    total_variants bigint,
    available_variants bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(DISTINCT ps.id) as total_products,
        COUNT(DISTINCT CASE WHEN pv.product_id IS NOT NULL THEN ps.id END) as products_with_variants,
        COUNT(pv.id) as total_variants,
        COUNT(CASE WHEN pv.is_available = true THEN pv.id END) as available_variants
    FROM public.products_services ps
    LEFT JOIN public.product_variants pv ON ps.id = pv.product_id
    WHERE ps.business_id = business_uuid;
END;
$function$
```

### is_variant_combination_unique()

```sql
CREATE OR REPLACE FUNCTION public.is_variant_combination_unique(
    product_uuid uuid,
    variant_vals jsonb,
    exclude_variant_id uuid DEFAULT NULL
)
RETURNS boolean
LANGUAGE plpgsql
AS $function$
DECLARE
    existing_count integer;
BEGIN
    SELECT COUNT(*) INTO existing_count
    FROM public.product_variants
    WHERE product_id = product_uuid
    AND variant_values = variant_vals
    AND (exclude_variant_id IS NULL OR id != exclude_variant_id);

    RETURN existing_count = 0;
END;
$function$
```

### validate_product_variant()

```sql
CREATE OR REPLACE FUNCTION public.validate_product_variant()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
DECLARE
    variant_count INTEGER;
    product_has_variants BOOLEAN;
    duplicate_count INTEGER;
BEGIN
    -- Check maximum variants per product (100)
    SELECT COUNT(*) INTO variant_count
    FROM public.product_variants
    WHERE product_id = NEW.product_id
    AND (TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND id != NEW.id));

    IF variant_count >= 100 THEN
        RAISE EXCEPTION 'Maximum of 100 variants allowed per product';
    END IF;

    -- Check for duplicate variant combinations within the same product
    SELECT COUNT(*) INTO duplicate_count
    FROM public.product_variants
    WHERE product_id = NEW.product_id
    AND variant_values = NEW.variant_values
    AND (TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND id != NEW.id));

    IF duplicate_count > 0 THEN
        RAISE EXCEPTION 'Duplicate variant combination already exists for this product';
    END IF;

    -- Validate that variant_values is not empty
    IF NEW.variant_values = '{}'::jsonb OR NEW.variant_values IS NULL THEN
        RAISE EXCEPTION 'Variant values cannot be empty';
    END IF;

    -- Validate that variant_values contains valid keys (max 5 variant types)
    IF jsonb_object_keys_count(NEW.variant_values) > 5 THEN
        RAISE EXCEPTION 'Maximum of 5 variant types allowed per product';
    END IF;

    RETURN NEW;
END;
$function$
```

### check_product_variant_availability()

```sql
CREATE OR REPLACE FUNCTION public.check_product_variant_availability()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
DECLARE
    available_variant_count INTEGER;
    total_variant_count INTEGER;
BEGIN
    -- Only check if we're making a variant unavailable
    IF TG_OP = 'UPDATE' AND OLD.is_available = true AND NEW.is_available = false THEN
        -- Count available variants for this product (excluding the one being updated)
        SELECT COUNT(*) INTO available_variant_count
        FROM public.product_variants
        WHERE product_id = NEW.product_id
        AND is_available = true
        AND id != NEW.id;

        -- Count total variants for this product
        SELECT COUNT(*) INTO total_variant_count
        FROM public.product_variants
        WHERE product_id = NEW.product_id;

        -- If this product has variants and we're making the last available variant unavailable
        IF total_variant_count > 0 AND available_variant_count = 0 THEN
            RAISE EXCEPTION 'At least one variant must be available if product has variants';
        END IF;
    END IF;

    RETURN NEW;
END;
$function$
```

### update_updated_at_column()

```sql
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$function$
```

### jsonb_object_keys_count()

```sql
CREATE OR REPLACE FUNCTION public.jsonb_object_keys_count(obj jsonb)
RETURNS integer
LANGUAGE sql
IMMUTABLE
AS $function$
    SELECT COUNT(*)::integer FROM jsonb_object_keys(obj);
$function$
```

## Location Services Functions

These functions handle GPS-based location detection and pincode/locality mapping for accurate business location services.

### find_closest_locality()

```sql
CREATE OR REPLACE FUNCTION public.find_closest_locality(
  user_lat FLOAT,
  user_lng FLOAT,
  max_distance_km FLOAT DEFAULT 10
)
RETURNS TABLE (
  pincode TEXT,
  office_name TEXT,
  division_name TEXT,
  state_name TEXT,
  distance_km FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p."Pincode"::TEXT,
    p."OfficeName"::TEXT,
    p."DivisionName"::TEXT,
    p."StateName"::TEXT,
    (
      6371 * acos(
        cos(radians(user_lat)) *
        cos(radians(lat_val)) *
        cos(radians(lng_val) - radians(user_lng)) +
        sin(radians(user_lat)) *
        sin(radians(lat_val))
      )
    )::FLOAT AS distance_km
  FROM (
    SELECT
      "Pincode",
      "OfficeName",
      "DivisionName",
      "StateName",
      CAST("Latitude" AS FLOAT) as lat_val,
      CAST("Longitude" AS FLOAT) as lng_val
    FROM pincodes p
    WHERE
      p."Latitude" IS NOT NULL
      AND p."Longitude" IS NOT NULL
      AND p."Latitude" != ''
      AND p."Longitude" != ''
      AND p."Latitude" != 'NA'
      AND p."Longitude" != 'NA'
      AND p."Latitude" ~ '^-?[0-9]+\.?[0-9]*$'
      AND p."Longitude" ~ '^-?[0-9]+\.?[0-9]*$'
  ) p
  ORDER BY distance_km
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;
```

**Purpose**: Finds the closest locality to given GPS coordinates using the Haversine formula for accurate distance calculation.

**Parameters**:
- `user_lat` (FLOAT): User's GPS latitude coordinate
- `user_lng` (FLOAT): User's GPS longitude coordinate
- `max_distance_km` (FLOAT): Maximum search radius in kilometers (default: 10km)

**Returns**: Table with the closest locality information:
- `pincode`: The pincode of the closest locality
- `office_name`: The name of the locality/office
- `division_name`: The city/division name
- `state_name`: The state name
- `distance_km`: Distance from user's location in kilometers

**Features**:
- **Data Validation**: Filters out invalid coordinates (NA, empty, malformed values)
- **Haversine Formula**: Uses precise mathematical calculation for distance
- **Performance Optimized**: Returns only the single closest match
- **Error Handling**: Handles malformed coordinate data gracefully

**Usage Examples**:
```sql
-- Find closest locality to coordinates in Rourkela, Odisha
SELECT * FROM find_closest_locality(22.1810654, 84.8773225, 10);
-- Returns: pincode="769016", office_name="Rourkela -16", division_name="Rourkela", state_name="Odisha", distance_km=0.65

-- Find closest locality to coordinates in Delhi
SELECT * FROM find_closest_locality(28.6139, 77.2090, 10);
-- Returns: pincode="110001", office_name="Rail Bhawan", division_name="New Delhi Central", state_name="Delhi", distance_km=0.32
```

**Integration**: This function is used by the React Native location service (`findClosestPincodeFromGPS()`) to provide accurate GPS-based address detection for business onboarding and profile management.
