"use client";

import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, ArrowRight } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";

/**
 * BusinessStatusAlert Component
 * 
 * This component displays an alert when a business profile is set to offline status.
 * It informs the user that their business card is not visible to customers and
 * provides a direct link to the card settings page to change the status.
 */
export default function BusinessStatusAlert() {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="mb-6"
    >
      <Alert
        variant="destructive"
        className="relative overflow-hidden rounded-2xl border border-red-200/60 dark:border-red-800/60 bg-red-50/50 dark:bg-red-950/50 backdrop-blur-sm"
      >
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-50/80 to-red-50/40 dark:from-red-950/80 dark:to-red-950/40" />

        <div className="relative z-10">
          <div className="flex items-start gap-4">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-red-100 to-red-50 dark:from-red-900/50 dark:to-red-950/50 border border-red-200 dark:border-red-800">
              <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div className="flex-1 space-y-3">
              <div className="space-y-1">
                <AlertTitle className="text-lg font-bold text-red-800 dark:text-red-300 tracking-tight">
                  Your Business Card is Offline
                </AlertTitle>
                <AlertDescription className="text-red-700 dark:text-red-400 leading-relaxed">
                  Your business card is currently set to offline status and won&apos;t appear in search results or discovery pages.
                </AlertDescription>
              </div>
              <Button
                asChild
                className="bg-red-600 hover:bg-red-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium"
                size="lg"
              >
                <Link href="/dashboard/business/card" className="flex items-center gap-2">
                  <span>Go Online Now</span>
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </Alert>
    </motion.div>
  );
}
