/**
 * Unit tests for src/components/feed/PostOptionsBottomSheet.tsx
 * Tests the PostOptionsBottomSheet component functionality
 */

import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import PostOptionsBottomSheet, {
  PostOptionsBottomSheetRef,
} from "@/src/components/feed/PostOptionsBottomSheet";

// Mock dependencies
jest.mock("@gorhom/bottom-sheet", () => {
  const mockReact = require("react");
  const { View, Text } = require("react-native");

  const MockBottomSheet = mockReact.forwardRef(
    ({ children, ...props }: any, ref: any) => {
      // Expose mock methods to parent
      mockReact.useImperativeHandle(ref, () => ({
        expand: jest.fn(),
        close: jest.fn(),
      }));

      return (
        <View testID="bottom-sheet" {...props}>
          {children}
        </View>
      );
    }
  );

  const MockBottomSheetView = ({ children, ...props }: any) => (
    <View testID="bottom-sheet-view" {...props}>
      {children}
    </View>
  );

  return {
    __esModule: true,
    default: MockBottomSheet,
    BottomSheetView: MockBottomSheetView,
  };
});

jest.mock("@/src/hooks/useTheme", () => ({
  useTheme: () => ({
    colors: {
      primary: "#007AFF",
      textPrimary: "#000000",
      textSecondary: "#666666",
      border: "#E5E5E5",
      background: "#FFFFFF",
    },
    isDark: false,
  }),
}));

jest.mock("lucide-react-native", () => ({
  Edit3: ({ size, color }: any) => {
    const { View } = require("react-native");
    return (
      <View
        testID="edit-icon"
        style={{ width: size, height: size, backgroundColor: color }}
      />
    );
  },
  Package: ({ size, color }: any) => {
    const { View } = require("react-native");
    return (
      <View
        testID="package-icon"
        style={{ width: size, height: size, backgroundColor: color }}
      />
    );
  },
  Trash2: ({ size, color }: any) => {
    const { View } = require("react-native");
    return (
      <View
        testID="trash-icon"
        style={{ width: size, height: size, backgroundColor: color }}
      />
    );
  },
  X: ({ size, color }: any) => {
    const { View } = require("react-native");
    return (
      <View
        testID="close-icon"
        style={{ width: size, height: size, backgroundColor: color }}
      />
    );
  },
  Share: ({ size, color }: any) => {
    const { View } = require("react-native");
    return (
      <View
        testID="share-icon"
        style={{ width: size, height: size, backgroundColor: color }}
      />
    );
  },
}));

describe("src/components/feed/PostOptionsBottomSheet.tsx", () => {
  const defaultProps = {
    postSource: "business" as const,
    isOwner: true,
    onEditPost: jest.fn(),
    onEditProducts: jest.fn(),
    onDeletePost: jest.fn(),
    onSharePost: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("PostOptionsBottomSheet component", () => {
    it("should render bottom sheet with all options for business post owner", () => {
      const { getByTestId } = render(
        <PostOptionsBottomSheet {...defaultProps} />
      );

      expect(getByTestId("bottom-sheet")).toBeTruthy();
      expect(getByTestId("bottom-sheet-view")).toBeTruthy();
      expect(getByTestId("edit-icon")).toBeTruthy();
      expect(getByTestId("package-icon")).toBeTruthy();
      expect(getByTestId("trash-icon")).toBeTruthy();
      expect(getByTestId("share-icon")).toBeTruthy();
    });

    it("should render options for customer post owner without edit products", () => {
      const customerProps = {
        ...defaultProps,
        postSource: "customer" as const,
      };

      const { getByText, queryByText } = render(
        <PostOptionsBottomSheet {...customerProps} />
      );

      expect(getByText("Edit Post")).toBeTruthy();
      expect(queryByText("Edit Products")).toBeNull();
      expect(getByText("Delete Post")).toBeTruthy();
      expect(getByText("Share Post")).toBeTruthy();
    });

    it("should only show share option for non-owners", () => {
      const nonOwnerProps = {
        ...defaultProps,
        isOwner: false,
      };

      const { getByText, queryByText } = render(
        <PostOptionsBottomSheet {...nonOwnerProps} />
      );

      expect(queryByText("Edit Post")).toBeNull();
      expect(queryByText("Edit Products")).toBeNull();
      expect(queryByText("Delete Post")).toBeNull();
      expect(getByText("Share Post")).toBeTruthy();
    });

    it("should call onEditPost when edit post option is pressed", () => {
      const { getByText } = render(
        <PostOptionsBottomSheet {...defaultProps} />
      );

      const editButton = getByText("Edit Post");
      fireEvent.press(editButton);

      expect(defaultProps.onEditPost).toHaveBeenCalledTimes(1);
    });

    it("should call onEditProducts when edit products option is pressed", () => {
      const { getByText } = render(
        <PostOptionsBottomSheet {...defaultProps} />
      );

      const editProductsButton = getByText("Edit Products");
      fireEvent.press(editProductsButton);

      expect(defaultProps.onEditProducts).toHaveBeenCalledTimes(1);
    });

    it("should call onDeletePost when delete option is pressed", () => {
      const { getByText } = render(
        <PostOptionsBottomSheet {...defaultProps} />
      );

      const deleteButton = getByText("Delete Post");
      fireEvent.press(deleteButton);

      expect(defaultProps.onDeletePost).toHaveBeenCalledTimes(1);
    });

    it("should call onSharePost when share option is pressed", () => {
      const { getByText } = render(
        <PostOptionsBottomSheet {...defaultProps} />
      );

      const shareButton = getByText("Share Post");
      fireEvent.press(shareButton);

      expect(defaultProps.onSharePost).toHaveBeenCalledTimes(1);
    });

    it("should handle missing onEditProducts callback gracefully", () => {
      const propsWithoutEditProducts = {
        ...defaultProps,
        onEditProducts: undefined,
      };

      const { getByText } = render(
        <PostOptionsBottomSheet {...propsWithoutEditProducts} />
      );

      // Should still render the edit products option for business posts
      const editProductsButton = getByText("Edit Products");

      // Should not crash when pressed
      expect(() => {
        fireEvent.press(editProductsButton);
      }).not.toThrow();
    });

    it("should render correct icons for each option", () => {
      const { getByTestId } = render(
        <PostOptionsBottomSheet {...defaultProps} />
      );

      expect(getByTestId("edit-icon")).toBeTruthy();
      expect(getByTestId("package-icon")).toBeTruthy();
      expect(getByTestId("trash-icon")).toBeTruthy();
      expect(getByTestId("share-icon")).toBeTruthy();
    });

    it("should expose correct ref methods", () => {
      const ref = React.createRef<PostOptionsBottomSheetRef>();

      render(<PostOptionsBottomSheet {...defaultProps} ref={ref} />);

      expect(ref.current).toBeTruthy();
      expect(typeof ref.current?.present).toBe("function");
      expect(typeof ref.current?.dismiss).toBe("function");
    });

    it("should handle rapid button presses without crashing", () => {
      const { getByText } = render(
        <PostOptionsBottomSheet {...defaultProps} />
      );

      const deleteButton = getByText("Delete Post");

      // Press multiple times rapidly
      fireEvent.press(deleteButton);
      fireEvent.press(deleteButton);
      fireEvent.press(deleteButton);

      expect(defaultProps.onDeletePost).toHaveBeenCalledTimes(3);
    });

    it("should render appropriate descriptions for each option", () => {
      const { getByText } = render(
        <PostOptionsBottomSheet {...defaultProps} />
      );

      expect(getByText("Modify the content of this post")).toBeTruthy();
      expect(getByText("Add or remove linked products")).toBeTruthy();
      expect(getByText("Permanently remove this post")).toBeTruthy();
      expect(getByText("Share this post with others")).toBeTruthy();
    });

    it("should apply correct styling based on theme", () => {
      const { getByTestId } = render(
        <PostOptionsBottomSheet {...defaultProps} />
      );

      const bottomSheet = getByTestId("bottom-sheet");
      expect(bottomSheet).toBeTruthy();
    });
  });
});
