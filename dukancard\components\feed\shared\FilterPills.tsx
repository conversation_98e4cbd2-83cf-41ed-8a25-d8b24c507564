'use client';

import { motion } from 'framer-motion';
import { FeedFilterType } from '@/lib/types/posts';
import { cn } from '@/lib/utils';
import { Sparkles, Users, MapPin, Hash, Building, Globe, List } from 'lucide-react';

interface FilterPillsProps {
  activeFilter: FeedFilterType;
  onFilterChange: (_filter: FeedFilterType) => void;
  isLoading?: boolean;
}

const filterOptions = [
  { value: 'smart' as FeedFilterType, label: 'Smart Feed', icon: Sparkles },
  { value: 'subscribed' as FeedFilterType, label: 'Following', icon: Users },
  { value: 'locality' as FeedFilterType, label: 'Locality', icon: MapPin },
  { value: 'pincode' as FeedFilterType, label: 'Pincode', icon: Hash },
  { value: 'city' as FeedFilterType, label: 'City', icon: Building },
  { value: 'state' as FeedFilterType, label: 'State', icon: Globe },
  { value: 'all' as FeedFilterType, label: 'All Posts', icon: List },
];

export default function FilterPills({ activeFilter, onFilterChange, isLoading = false }: FilterPillsProps) {
  const activeFilterOption = filterOptions.find(option => option.value === activeFilter);

  return (
    <div className="w-full">
      {/* Selected Filter Display - Mobile/Tablet only */}
      <div className="md:hidden mb-3 px-4">
        <div className="text-sm text-muted-foreground">
          Showing: <span className="font-medium text-foreground">{activeFilterOption?.label || 'Smart Feed'}</span>
        </div>
      </div>

      {/* Mobile: Horizontal scroll, Desktop: Centered */}
      <div className="flex gap-2 overflow-x-auto scrollbar-hide pb-2 md:pb-0 md:justify-center">
        <div className="flex gap-2 md:flex-wrap md:justify-center">
          {filterOptions.map((option) => {
            const Icon = option.icon;
            const isActive = activeFilter === option.value;

            return (
              <motion.button
                key={option.value}
                onClick={() => !isLoading && onFilterChange(option.value)}
                disabled={isLoading}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={cn(
                  "flex items-center gap-2 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap relative cursor-pointer",
                  "border border-neutral-200 dark:border-neutral-700",
                  // Mobile/Tablet: Icon only (circular)
                  "w-10 h-10 p-0 justify-center md:px-4 md:py-2.5 md:w-auto md:h-auto",
                  isActive
                    ? "bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)] shadow-md"
                    : "bg-white dark:bg-black text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-900 hover:border-neutral-300 dark:hover:border-neutral-600",
                  isLoading && "opacity-50 cursor-not-allowed"
                )}
              >
                <Icon className="h-4 w-4" />
                <span className="hidden md:inline">{option.label}</span>

                {/* Active indicator */}
                {isActive && (
                  <motion.div
                    layoutId="activeFilter"
                    className="absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10"
                    initial={false}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  />
                )}
              </motion.button>
            );
          })}
        </div>
      </div>
    </div>
  );
}
