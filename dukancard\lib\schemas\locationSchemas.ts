import * as z from "zod";

// Schema for pincode-based search
export const pincodeSchema = z.object({
  pincode: z
    .string()
    .regex(/^\d{6}$/, { message: "Pincode must be exactly 6 digits." }),
  locality: z.string().optional().nullable(), // Optional locality
});

// Schema for city-based search
export const citySchema = z.object({
  city: z
    .string()
    .min(2, { message: "City name must be at least 2 characters." }),
  locality: z.string().optional().nullable(), // Optional locality
});

// Schema for business name search
export const businessNameSchema = z.object({
  businessName: z.string().min(1, { message: "Business name is required." }),
});

// Schema for combined search
export const combinedSearchSchema = z.object({
  businessName: z.string().optional().nullable(),
  pincode: z
    .string()
    .regex(/^\d{6}$/, { message: "Pincode must be exactly 6 digits." })
    .optional()
    .nullable(),
  city: z.string().optional().nullable(),
  locality: z.string().optional().nullable(),
  category: z.string().optional().nullable(),
});

// Schema for pagination parameters
export const paginationSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(50).default(20), // Max 50 items per page
});

// Schema for sorting options
export const sortingSchema = z.object({
  sortBy: z
    .enum([
      "name_asc",
      "name_desc",
      "created_asc",
      "created_desc",
      "likes_asc",
      "likes_desc",
      "subscriptions_asc",
      "subscriptions_desc",
      "rating_asc",
      "rating_desc",
    ])
    .default("created_desc"),
});

// Combined schema for pincode-based discovery search with pagination
export const discoverySearchSchema = pincodeSchema.extend({
  viewType: z.enum(["cards", "products"]),
  ...paginationSchema.shape,
  ...sortingSchema.shape,
});

// Combined schema for business name search with pagination
export const businessNameSearchSchema = businessNameSchema.extend({
  viewType: z.enum(["cards", "products"]),
  ...paginationSchema.shape,
  ...sortingSchema.shape,
});

// Combined schema for combined search with pagination
export const combinedSearchParamsSchema = combinedSearchSchema.extend({
  viewType: z.enum(["cards", "products"]),
  ...paginationSchema.shape,
  ...sortingSchema.shape,
});

export type LocationSearchFormData = z.infer<typeof pincodeSchema>;
export type CitySearchFormData = z.infer<typeof citySchema>;
export type BusinessNameSearchFormData = z.infer<typeof businessNameSchema>;
export type PaginationParams = z.infer<typeof paginationSchema>;
export type SortingParams = z.infer<typeof sortingSchema>;
export type DiscoverySearchParams = z.infer<typeof discoverySearchSchema>;
export type BusinessNameSearchParams = z.infer<typeof businessNameSearchSchema>;
export type CombinedSearchParams = z.infer<typeof combinedSearchParamsSchema>;
export type CombinedSearchFormData = z.infer<typeof combinedSearchSchema>;
