"use client";

import React, { useTransition } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { CustomerProfileSchema, CustomerEmailSchema } from "./schema";
import { updateCustomerEmail } from "./actions";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";



interface CustomerSettingsFormProps {
  initialName: string | null;
  initialEmail: string | null;
}

export function CustomerSettingsForm({ initialName, initialEmail }: CustomerSettingsFormProps) {
  const [isPendingName] = useTransition();
  const [isPendingEmail, startTransitionEmail] = useTransition();


  // Form for updating name
  const nameForm = useForm<z.infer<typeof CustomerProfileSchema>>({
    resolver: zodResolver(CustomerProfileSchema),
    defaultValues: {
      name: initialName || "",
    },
  });

  // Form for updating email
  const emailForm = useForm<z.infer<typeof CustomerEmailSchema>>({
    resolver: zodResolver(CustomerEmailSchema),
    defaultValues: {
      email: initialEmail || "",
    },
  });




  // Handler for name update
  async function onSubmitName() {
    toast.error("Name update is currently disabled.");
  }

  // Handler for email update
  async function onSubmitEmail(values: z.infer<typeof CustomerEmailSchema>) {
    if (values.email === initialEmail) {
      toast.info("Email address is the same.");
      return;
    }
    startTransitionEmail(async () => {
      const formData = new FormData();
      formData.append("newEmail", values.email);
      const result = await updateCustomerEmail({ message: null, success: false }, formData);
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    });
  }




  return (
    <div className="space-y-6">
      {/* Update Name Card */}
      <Card className="bg-card border border-border/20 dark:border-[var(--brand-gold)]/20 shadow-sm">
        <CardHeader>
          <CardTitle>Update Name</CardTitle>
          <CardDescription>Change the name associated with your account.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...nameForm}>
            <form onSubmit={nameForm.handleSubmit(onSubmitName)} className="space-y-4">
              <FormField
                control={nameForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Your Name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button
                type="submit"
                disabled={isPendingName || !nameForm.formState.isDirty}
                className="bg-primary text-primary-foreground hover:bg-primary/90 dark:bg-[var(--brand-gold)] dark:text-black dark:hover:bg-[var(--brand-gold)]/90"
              >
                {isPendingName ? "Updating..." : "Update Name"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      <Separator />

      {/* Update Email Card */}
      <Card className="bg-card border border-border/20 dark:border-[var(--brand-gold)]/20 shadow-sm">
         <CardHeader>
           <CardTitle>Update Email</CardTitle>
           <CardDescription>
             Change your login email. A confirmation link will be sent to the new address.
           </CardDescription>
         </CardHeader>
         <CardContent>
           <Form {...emailForm}>
             <form onSubmit={emailForm.handleSubmit(onSubmitEmail)} className="space-y-4">
               <FormField
                 control={emailForm.control}
                 name="email"
                 render={({ field }) => (
                   <FormItem>
                     <FormLabel>New Email</FormLabel>
                     <FormControl>
                       <Input type="email" placeholder="<EMAIL>" {...field} />
                     </FormControl>
                     <FormDescription>
                        Current email: {initialEmail || "Not set"}
                     </FormDescription>
                     <FormMessage />
                   </FormItem>
                 )}
               />
               <Button
                 type="submit"
                 disabled={isPendingEmail || !emailForm.formState.isDirty || emailForm.getValues("email") === initialEmail}
                 className="bg-primary text-primary-foreground hover:bg-primary/90 dark:bg-[var(--brand-gold)] dark:text-black dark:hover:bg-[var(--brand-gold)]/90"
               >
                 {isPendingEmail ? "Sending..." : "Update Email"}
               </Button>
             </form>
           </Form>
         </CardContent>
       </Card>



    </div>
  );
}
