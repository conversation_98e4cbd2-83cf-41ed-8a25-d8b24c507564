/**
 * Camera detection and permission utilities for web browsers
 */

export interface CameraCapabilities {
  hasCamera: boolean;
  hasPermission: boolean | null; // null means unknown/not requested yet
  isSecureContext: boolean;
  supportedConstraints: MediaTrackSupportedConstraints | null;
  error?: string;
}

export interface CameraDevice {
  deviceId: string;
  label: string;
  kind: 'videoinput';
  groupId: string;
}

/**
 * Check if the browser supports camera access
 * @returns True if getUserMedia is supported
 */
export function isCameraSupported(): boolean {
  return !!(
    navigator.mediaDevices &&
    navigator.mediaDevices.getUserMedia &&
    typeof navigator.mediaDevices.getUserMedia === 'function'
  );
}

/**
 * Check if the current context is secure (HTTPS or localhost)
 * Camera access requires secure context
 * @returns True if context is secure
 */
export function isSecureContext(): boolean {
  return window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';
}

/**
 * Get available camera devices
 * @returns Promise with array of camera devices
 */
export async function getCameraDevices(): Promise<CameraDevice[]> {
  if (!isCameraSupported()) {
    throw new Error('Camera not supported in this browser');
  }

  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    return devices
      .filter(device => device.kind === 'videoinput')
      .map(device => ({
        deviceId: device.deviceId,
        label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,
        kind: device.kind as 'videoinput',
        groupId: device.groupId
      }));
  } catch (error) {
    console.error('Error enumerating camera devices:', error);
    throw new Error('Failed to enumerate camera devices');
  }
}

/**
 * Check camera permission status
 * @returns Permission state: 'granted', 'denied', 'prompt', or null if not supported
 */
export async function checkCameraPermission(): Promise<PermissionState | null> {
  if (!navigator.permissions || !navigator.permissions.query) {
    return null; // Permissions API not supported
  }

  try {
    const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
    return permission.state;
  } catch (error) {
    console.warn('Error checking camera permission:', error);
    return null;
  }
}

/**
 * Request camera access and check capabilities
 * @param constraints - Optional camera constraints
 * @returns Promise with camera capabilities
 */
export async function requestCameraAccess(
  constraints: MediaStreamConstraints = { video: true }
): Promise<{ stream: MediaStream; capabilities: CameraCapabilities }> {
  if (!isCameraSupported()) {
    throw new Error('Camera not supported in this browser');
  }

  if (!isSecureContext()) {
    throw new Error('Camera access requires HTTPS or localhost');
  }

  try {
    const stream = await navigator.mediaDevices.getUserMedia(constraints);
    
    const capabilities: CameraCapabilities = {
      hasCamera: true,
      hasPermission: true,
      isSecureContext: isSecureContext(),
      supportedConstraints: navigator.mediaDevices.getSupportedConstraints()
    };

    return { stream, capabilities };
  } catch (error: unknown) {
    let errorMessage = 'Failed to access camera';
    let hasPermission = false;

    if (error instanceof Error) {
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Camera access denied by user';
        hasPermission = false;
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No camera found on this device';
      } else if (error.name === 'NotReadableError') {
        errorMessage = 'Camera is already in use by another application';
      } else if (error.name === 'OverconstrainedError') {
        errorMessage = 'Camera does not support the requested constraints';
      } else if (error.name === 'SecurityError') {
        errorMessage = 'Camera access blocked due to security restrictions';
      }
    }

    const capabilities: CameraCapabilities = {
      hasCamera: error instanceof Error ? error.name !== 'NotFoundError' : false,
      hasPermission,
      isSecureContext: isSecureContext(),
      supportedConstraints: navigator.mediaDevices?.getSupportedConstraints() || null,
      error: errorMessage
    };

    throw { error: errorMessage, capabilities };
  }
}

/**
 * Get comprehensive camera capabilities without requesting access
 * @returns Promise with camera capabilities
 */
export async function getCameraCapabilities(): Promise<CameraCapabilities> {
  const capabilities: CameraCapabilities = {
    hasCamera: false,
    hasPermission: null,
    isSecureContext: isSecureContext(),
    supportedConstraints: null
  };

  if (!isCameraSupported()) {
    capabilities.error = 'Camera not supported in this browser';
    return capabilities;
  }

  if (!isSecureContext()) {
    capabilities.error = 'Camera access requires HTTPS or localhost';
    return capabilities;
  }

  capabilities.supportedConstraints = navigator.mediaDevices.getSupportedConstraints();

  try {
    // Check permission status
    const permissionStatus = await checkCameraPermission();
    capabilities.hasPermission = permissionStatus === 'granted';

    // Try to enumerate devices to check if camera exists
    const devices = await getCameraDevices();
    capabilities.hasCamera = devices.length > 0;

    if (!capabilities.hasCamera) {
      capabilities.error = 'No camera found on this device';
    }
  } catch (error: unknown) {
    capabilities.error = error instanceof Error ? error.message : 'Failed to check camera capabilities';
  }

  return capabilities;
}

/**
 * Stop a media stream and release camera resources
 * @param stream - The media stream to stop
 */
export function stopCameraStream(stream: MediaStream): void {
  if (stream) {
    stream.getTracks().forEach(track => {
      track.stop();
    });
  }
}

/**
 * Check if the device is likely a mobile device
 * @returns True if device appears to be mobile
 */
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * Get preferred camera constraints for QR scanning
 * @param preferredDeviceId - Optional preferred camera device ID
 * @returns Camera constraints optimized for QR scanning
 */
export function getQRScanConstraints(preferredDeviceId?: string): MediaStreamConstraints {
  const constraints: MediaStreamConstraints = {
    video: {
      width: { ideal: 1280 },
      height: { ideal: 720 },
      facingMode: isMobileDevice() ? 'environment' : 'user', // Back camera on mobile, front on desktop
      frameRate: { ideal: 30 }
    }
  };

  if (preferredDeviceId) {
    (constraints.video as MediaTrackConstraints).deviceId = { exact: preferredDeviceId };
  }

  return constraints;
}
