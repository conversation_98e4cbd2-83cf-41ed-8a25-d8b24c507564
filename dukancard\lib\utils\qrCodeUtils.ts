/**
 * QR Code validation utilities for Next.js
 * Adapted from React Native implementation for web use
 */

export interface QRCodeValidationResult {
  isValid: boolean;
  error?: string;
  businessSlug?: string;
  url?: string;
}

/**
 * Validates if a business slug has the correct format
 * @param slug - The business slug to validate
 * @returns Validation result
 */
function validateBusinessSlug(slug: string): { isValid: boolean; error?: string } {
  if (!slug || typeof slug !== 'string') {
    return {
      isValid: false,
      error: 'Business slug is required'
    };
  }

  const cleanSlug = slug.trim();

  if (!cleanSlug) {
    return {
      isValid: false,
      error: 'Business slug cannot be empty'
    };
  }

  // Check length (3-50 characters)
  if (cleanSlug.length < 3 || cleanSlug.length > 50) {
    return {
      isValid: false,
      error: 'Business slug must be between 3 and 50 characters'
    };
  }

  // Check format: lowercase letters, numbers, hyphens only
  const slugPattern = /^[a-z0-9-]+$/;
  if (!slugPattern.test(cleanSlug)) {
    return {
      isValid: false,
      error: 'Business slug can only contain lowercase letters, numbers, and hyphens'
    };
  }

  // Cannot start or end with hyphen
  if (cleanSlug.startsWith('-') || cleanSlug.endsWith('-')) {
    return {
      isValid: false,
      error: 'Business slug cannot start or end with a hyphen'
    };
  }

  // Cannot have consecutive hyphens
  if (cleanSlug.includes('--')) {
    return {
      isValid: false,
      error: 'Business slug cannot contain consecutive hyphens'
    };
  }

  return {
    isValid: true
  };
}

/**
 * Validates if a QR code contains a valid dukancard.in URL
 * @param qrData - The raw data from the QR code scan
 * @returns Validation result with business slug if valid
 */
export function validateQRCodeUrl(qrData: string): QRCodeValidationResult {
  if (!qrData || typeof qrData !== 'string') {
    return {
      isValid: false,
      error: 'Invalid QR code data'
    };
  }

  // Clean the data
  const cleanData = qrData.trim();

  if (!cleanData) {
    return {
      isValid: false,
      error: 'Empty QR code data'
    };
  }

  // Check if it's a valid URL
  let url: URL;
  try {
    // Handle cases where the QR code might not have a protocol
    const urlString = cleanData.startsWith('http') ? cleanData : `https://${cleanData}`;
    url = new URL(urlString);
  } catch (_error) {
    return {
      isValid: false,
      error: 'QR code does not contain a valid URL'
    };
  }

  // Check if it's a dukancard.in domain
  const validDomains = ['dukancard.in', 'www.dukancard.in'];
  if (!validDomains.includes(url.hostname.toLowerCase())) {
    return {
      isValid: false,
      error: 'QR code is not from Dukancard'
    };
  }

  // Extract the business slug from the path
  const pathSegments = url.pathname.split('/').filter(segment => segment.length > 0);
  
  if (pathSegments.length === 0) {
    return {
      isValid: false,
      error: 'QR code does not contain a business profile URL'
    };
  }

  const businessSlug = pathSegments[0];

  // Validate the business slug format
  const slugValidation = validateBusinessSlug(businessSlug);
  if (!slugValidation.isValid) {
    return {
      isValid: false,
      error: slugValidation.error || 'Invalid business URL format'
    };
  }

  return {
    isValid: true,
    businessSlug,
    url: url.toString()
  };
}

/**
 * Generates a dukancard.in URL for a business slug
 * @param businessSlug - The business slug
 * @returns Complete dukancard.in URL
 */
export function generateDukancardUrl(businessSlug: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in';
  return `${baseUrl}/${businessSlug}`;
}

/**
 * Extracts business slug from a dukancard.in URL
 * @param url - The dukancard.in URL
 * @returns Business slug if valid, null otherwise
 */
export function extractBusinessSlugFromUrl(url: string): string | null {
  const validation = validateQRCodeUrl(url);
  return validation.isValid ? validation.businessSlug! : null;
}

/**
 * Checks if a URL is a valid dukancard.in business URL
 * @param url - The URL to check
 * @returns True if it's a valid dukancard business URL
 */
export function isDukancardBusinessUrl(url: string): boolean {
  const validation = validateQRCodeUrl(url);
  return validation.isValid;
}

/**
 * Validates QR code data and provides user-friendly error messages
 * @param qrData - The raw QR code data
 * @returns User-friendly validation result
 */
export function validateQRCodeForUser(qrData: string): QRCodeValidationResult {
  const result = validateQRCodeUrl(qrData);
  
  if (!result.isValid) {
    // Provide more user-friendly error messages
    let userFriendlyError = result.error;
    
    if (result.error?.includes('not from Dukancard')) {
      userFriendlyError = 'This QR code is not from Dukancard. Please scan a valid Dukancard business QR code.';
    } else if (result.error?.includes('not contain a valid URL')) {
      userFriendlyError = 'Invalid QR code format. Please scan a valid Dukancard business QR code.';
    } else if (result.error?.includes('business profile URL')) {
      userFriendlyError = 'This QR code does not link to a business profile. Please scan a valid Dukancard business QR code.';
    }
    
    return {
      ...result,
      error: userFriendlyError
    };
  }
  
  return result;
}
