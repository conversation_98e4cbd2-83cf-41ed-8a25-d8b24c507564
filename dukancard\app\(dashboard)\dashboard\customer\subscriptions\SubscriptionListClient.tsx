import { useMemo } from 'react';
import { SubscriptionList, SubscriptionData } from '@/app/components/shared/subscriptions';
import { SubscriptionWithProfile } from './actions';

interface SubscriptionListClientProps {
  initialSubscriptions: SubscriptionWithProfile[];
  totalCount: number;
  currentPage: number;
}

export default function SubscriptionListClient({ initialSubscriptions }: SubscriptionListClientProps) {
  const transformedSubscriptions: SubscriptionData[] = useMemo(() => {
    return initialSubscriptions.map(sub => ({
      id: sub.id,
      profile: sub.business_profiles ? {
        id: sub.business_profiles.id,
        name: sub.business_profiles.business_name,
        slug: sub.business_profiles.business_slug,
        logo_url: sub.business_profiles.logo_url,
        city: sub.business_profiles.city,
        state: sub.business_profiles.state,
        locality: sub.business_profiles.locality,
        type: 'business',
      } : null,
    })).filter(sub => sub.profile !== null) as SubscriptionData[];
  }, [initialSubscriptions]);

  return (
    <SubscriptionList
      initialSubscriptions={transformedSubscriptions}
      // Pagination and search props are handled by the parent component
    />
  );
}