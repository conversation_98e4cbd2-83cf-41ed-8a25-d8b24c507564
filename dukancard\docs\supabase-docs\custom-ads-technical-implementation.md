# Custom Ads System - Technical Implementation

This document provides a detailed technical overview of the custom ads system implementation in Dukancard, including database structure, SQL functions, Next.js components, and integration points.

## Database Implementation

### Tables

#### custom_ads

```sql
CREATE TABLE custom_ads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ad_image_url TEXT NOT NULL,
  ad_link_url TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  expiry_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

#### custom_ad_targets

```sql
CREATE TABLE custom_ad_targets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ad_id UUID NOT NULL REFERENCES custom_ads(id) ON DELETE CASCADE,
  pincode VARCHAR(6) NOT NULL,
  is_global BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(ad_id, pincode)
);

CREATE INDEX idx_custom_ad_targets_pincode ON custom_ad_targets(pincode);
CREATE INDEX idx_custom_ad_targets_global ON custom_ad_targets(is_global) WHERE is_global = true;
```

### Triggers

#### check_global_target

Ensures only one global target per ad:

```sql
CREATE OR REPLACE FUNCTION check_global_target()
RETURNS TRIGGER AS $$
BEGIN
  -- If this is a global target, check if there's already one for this ad
  IF NEW.is_global = TRUE THEN
    IF EXISTS (
      SELECT 1 FROM custom_ad_targets
      WHERE ad_id = NEW.ad_id
      AND is_global = TRUE
      AND id != NEW.id
    ) THEN
      RAISE EXCEPTION 'Only one global target is allowed per ad';
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER enforce_single_global_target
BEFORE INSERT OR UPDATE ON custom_ad_targets
FOR EACH ROW
EXECUTE FUNCTION check_global_target();
```

### Views

#### ad_targets_view

```sql
CREATE OR REPLACE VIEW ad_targets_view AS
SELECT
  c.id as ad_id,
  c.ad_image_url,
  c.ad_link_url,
  c.is_active,
  c.expiry_date,
  CASE WHEN c.expiry_date IS NULL THEN NULL
       WHEN c.expiry_date <= NOW() THEN 'Expired'
       WHEN c.expiry_date <= NOW() + INTERVAL '7 days' THEN 'Expiring Soon'
       ELSE 'Active'
  END as expiry_status,
  t.id as target_id,
  t.pincode,
  t.is_global,
  c.created_at as ad_created_at,
  t.created_at as target_created_at
FROM custom_ads c
JOIN custom_ad_targets t ON c.id = t.ad_id
ORDER BY c.created_at DESC, t.created_at DESC;
```

#### expired_ads_view

```sql
CREATE OR REPLACE VIEW expired_ads_view AS
SELECT
  c.id as ad_id,
  c.ad_image_url,
  c.ad_link_url,
  c.is_active,
  c.expiry_date,
  c.created_at,
  CASE
    WHEN c.expiry_date IS NULL THEN 'No Expiry'
    WHEN c.expiry_date <= NOW() THEN 'Expired'
    WHEN c.expiry_date <= NOW() + INTERVAL '7 days' THEN 'Expiring Soon'
    ELSE 'Active'
  END as status,
  NOW() - c.expiry_date as expired_for
FROM custom_ads c
WHERE c.expiry_date IS NOT NULL AND c.expiry_date <= NOW()
ORDER BY c.expiry_date DESC;
```

### Functions

#### get_ad_for_pincode

```sql
CREATE OR REPLACE FUNCTION get_ad_for_pincode(target_pincode VARCHAR)
RETURNS TABLE (
  id UUID,
  ad_image_url TEXT,
  ad_link_url TEXT,
  is_global BOOLEAN,
  expiry_date TIMESTAMPTZ
) AS $$
  -- Try to find a pincode-specific ad first
  WITH pincode_ad AS (
    SELECT
      c.id,
      c.ad_image_url,
      c.ad_link_url,
      FALSE AS is_global,
      c.expiry_date
    FROM custom_ads c
    JOIN custom_ad_targets t ON c.id = t.ad_id
    WHERE c.is_active = TRUE
    AND t.pincode = target_pincode
    AND t.is_global = FALSE
    AND (c.expiry_date IS NULL OR c.expiry_date > NOW())
    ORDER BY c.created_at DESC
    LIMIT 1
  ),
  -- If no pincode-specific ad, try global ads
  global_ad AS (
    SELECT
      c.id,
      c.ad_image_url,
      c.ad_link_url,
      TRUE AS is_global,
      c.expiry_date
    FROM custom_ads c
    JOIN custom_ad_targets t ON c.id = t.ad_id
    WHERE c.is_active = TRUE
    AND t.is_global = TRUE
    AND (c.expiry_date IS NULL OR c.expiry_date > NOW())
    AND NOT EXISTS (SELECT 1 FROM pincode_ad)
    ORDER BY c.created_at DESC
    LIMIT 1
  )
  -- Return pincode-specific ad if found, otherwise global ad
  SELECT * FROM pincode_ad
  UNION ALL
  SELECT * FROM global_ad;
$$ LANGUAGE SQL;
```

#### add_custom_ad

```sql
CREATE OR REPLACE FUNCTION add_custom_ad(
  p_ad_image_url TEXT,
  p_ad_link_url TEXT,
  p_is_active BOOLEAN,
  p_is_global BOOLEAN,
  p_expiry_date TIMESTAMPTZ DEFAULT NULL,
  p_pincodes TEXT[] DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  v_ad_id UUID;
  v_pincode TEXT;
BEGIN
  -- Validate inputs
  IF p_is_global = FALSE AND (p_pincodes IS NULL OR array_length(p_pincodes, 1) = 0) THEN
    RAISE EXCEPTION 'Pincodes must be provided for non-global ads';
  END IF;

  -- Insert the ad
  INSERT INTO custom_ads (ad_image_url, ad_link_url, is_active, expiry_date)
  VALUES (
    p_ad_image_url,
    p_ad_link_url,
    p_is_active,
    p_expiry_date
  )
  RETURNING id INTO v_ad_id;

  -- Insert targeting information
  IF p_is_global THEN
    -- Global ad
    INSERT INTO custom_ad_targets (ad_id, pincode, is_global)
    VALUES (v_ad_id, '000000', TRUE);
  ELSE
    -- Pincode-specific ad
    FOREACH v_pincode IN ARRAY p_pincodes LOOP
      INSERT INTO custom_ad_targets (ad_id, pincode, is_global)
      VALUES (v_ad_id, v_pincode, FALSE);
    END LOOP;
  END IF;

  RETURN v_ad_id;
END;
$$ LANGUAGE plpgsql;
```

#### update_custom_ad

```sql
CREATE OR REPLACE FUNCTION update_custom_ad(
  p_ad_id UUID,
  p_ad_image_url TEXT DEFAULT NULL,
  p_ad_link_url TEXT DEFAULT NULL,
  p_is_active BOOLEAN DEFAULT NULL,
  p_expiry_date TIMESTAMPTZ DEFAULT NULL,
  p_is_global BOOLEAN DEFAULT NULL,
  p_pincodes TEXT[] DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  v_pincode TEXT;
  v_current_is_global BOOLEAN;
  v_update_targets BOOLEAN := FALSE;
BEGIN
  -- Check if ad exists
  IF NOT EXISTS (SELECT 1 FROM custom_ads WHERE id = p_ad_id) THEN
    RAISE EXCEPTION 'Ad with ID % does not exist', p_ad_id;
  END IF;

  -- Update ad fields if provided
  UPDATE custom_ads
  SET
    ad_image_url = COALESCE(p_ad_image_url, ad_image_url),
    ad_link_url = COALESCE(p_ad_link_url, ad_link_url),
    is_active = COALESCE(p_is_active, is_active),
    expiry_date = COALESCE(p_expiry_date, expiry_date)
  WHERE id = p_ad_id;

  -- Check if we need to update targeting
  IF p_is_global IS NOT NULL OR p_pincodes IS NOT NULL THEN
    -- Get current global status
    SELECT is_global INTO v_current_is_global
    FROM custom_ad_targets
    WHERE ad_id = p_ad_id
    LIMIT 1;

    -- If global status is changing or pincodes are provided, update targets
    IF (p_is_global IS NOT NULL AND p_is_global != v_current_is_global) OR
       (p_pincodes IS NOT NULL AND array_length(p_pincodes, 1) > 0) THEN
      v_update_targets := TRUE;
    END IF;

    IF v_update_targets THEN
      -- Delete existing targets
      DELETE FROM custom_ad_targets WHERE ad_id = p_ad_id;

      -- Insert new targets
      IF p_is_global IS NOT NULL AND p_is_global = TRUE THEN
        -- Global ad
        INSERT INTO custom_ad_targets (ad_id, pincode, is_global)
        VALUES (p_ad_id, '000000', TRUE);
      ELSIF p_pincodes IS NOT NULL AND array_length(p_pincodes, 1) > 0 THEN
        -- Pincode-specific ad
        FOREACH v_pincode IN ARRAY p_pincodes LOOP
          INSERT INTO custom_ad_targets (ad_id, pincode, is_global)
          VALUES (p_ad_id, v_pincode, FALSE);
        END LOOP;
      END IF;
    END IF;
  END IF;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
```

#### delete_custom_ad

```sql
CREATE OR REPLACE FUNCTION delete_custom_ad(p_ad_id UUID) RETURNS BOOLEAN AS $$
BEGIN
  -- Check if ad exists
  IF NOT EXISTS (SELECT 1 FROM custom_ads WHERE id = p_ad_id) THEN
    RAISE EXCEPTION 'Ad with ID % does not exist', p_ad_id;
  END IF;

  -- Delete the ad (cascade will delete targets)
  DELETE FROM custom_ads WHERE id = p_ad_id;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
```

#### get_all_ads_for_pincode

```sql
CREATE OR REPLACE FUNCTION get_all_ads_for_pincode(target_pincode VARCHAR)
RETURNS TABLE (
  id UUID,
  ad_image_url TEXT,
  ad_link_url TEXT,
  is_global BOOLEAN,
  expiry_date TIMESTAMPTZ,
  is_active BOOLEAN,
  created_at TIMESTAMPTZ,
  status TEXT
) AS $$
BEGIN
  -- Return all ads for this pincode (both specific and global)
  RETURN QUERY
  SELECT
    c.id,
    c.ad_image_url,
    c.ad_link_url,
    t.is_global,
    c.expiry_date,
    c.is_active,
    c.created_at,
    CASE
      WHEN c.expiry_date IS NULL THEN 'No Expiry'
      WHEN c.expiry_date <= NOW() THEN 'Expired'
      WHEN c.expiry_date <= NOW() + INTERVAL '7 days' THEN 'Expiring Soon'
      ELSE 'Active'
    END as status
  FROM custom_ads c
  JOIN custom_ad_targets t ON c.id = t.ad_id
  WHERE (t.pincode = target_pincode OR t.is_global = TRUE)
  ORDER BY t.is_global, c.created_at DESC;

  RETURN;
END;
$$ LANGUAGE plpgsql;
```

#### get_custom_ads_stats

```sql
CREATE OR REPLACE FUNCTION get_custom_ads_stats()
RETURNS TABLE (
  total_ads BIGINT,
  active_ads BIGINT,
  expired_ads BIGINT,
  global_ads BIGINT,
  pincode_targeted_ads BIGINT,
  unique_pincodes_targeted BIGINT,
  expiring_soon_ads BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    (SELECT COUNT(*) FROM custom_ads) AS total_ads,
    (SELECT COUNT(*) FROM custom_ads WHERE is_active = TRUE AND (expiry_date IS NULL OR expiry_date > NOW())) AS active_ads,
    (SELECT COUNT(*) FROM custom_ads WHERE expiry_date IS NOT NULL AND expiry_date <= NOW()) AS expired_ads,
    (SELECT COUNT(*) FROM custom_ad_targets WHERE is_global = TRUE) AS global_ads,
    (SELECT COUNT(*) FROM custom_ad_targets WHERE is_global = FALSE) AS pincode_targeted_ads,
    (SELECT COUNT(DISTINCT pincode) FROM custom_ad_targets WHERE is_global = FALSE) AS unique_pincodes_targeted,
    (SELECT COUNT(*) FROM custom_ads WHERE expiry_date IS NOT NULL AND expiry_date > NOW() AND expiry_date <= NOW() + INTERVAL '7 days') AS expiring_soon_ads;

  RETURN;
END;
$$ LANGUAGE plpgsql;
```

#### get_most_targeted_pincodes

```sql
CREATE OR REPLACE FUNCTION get_most_targeted_pincodes(limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
  pincode VARCHAR,
  ad_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    t.pincode,
    COUNT(*) AS ad_count
  FROM custom_ad_targets t
  JOIN custom_ads a ON t.ad_id = a.id
  WHERE t.is_global = FALSE
  AND a.is_active = TRUE
  AND (a.expiry_date IS NULL OR a.expiry_date > NOW())
  GROUP BY t.pincode
  ORDER BY ad_count DESC
  LIMIT limit_count;

  RETURN;
END;
$$ LANGUAGE plpgsql;
```

#### cleanup_expired_ads

```sql
CREATE OR REPLACE FUNCTION cleanup_expired_ads()
RETURNS INTEGER AS $$
DECLARE
  expired_count INTEGER;
BEGIN
  -- Set is_active to FALSE for expired ads
  UPDATE custom_ads
  SET is_active = FALSE
  WHERE expiry_date IS NOT NULL
  AND expiry_date <= NOW()
  AND is_active = TRUE;

  GET DIAGNOSTICS expired_count = ROW_COUNT;
  RETURN expired_count;
END;
$$ LANGUAGE plpgsql;
```

### Scheduled Jobs

```sql
SELECT cron.schedule(
  'cleanup-expired-ads',
  '0 0 * * *',  -- Run at midnight every day
  'SELECT cleanup_expired_ads();'
);
```

## Next.js Implementation

### Components

#### GoogleAdScript.tsx

```tsx
"use client";

import { useEffect, useRef } from "react";

interface GoogleAdScriptProps {
  adClient: string;
  adSlot: string;
  adFormat?: string;
  fullWidthResponsive?: boolean;
  adLayout?: string;
  className?: string;
}

export default function GoogleAdScript({
  adClient,
  adSlot,
  adFormat = "auto",
  fullWidthResponsive = true,
  adLayout,
  className = "",
}: GoogleAdScriptProps) {
  const adRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Only run in browser environment
    if (typeof window === "undefined") return;

    // Clear previous ad content if any (for dynamic rerenders)
    if (adRef.current) {
      adRef.current.innerHTML = "";

      // Create the ins element
      const ins = document.createElement("ins");
      ins.className = "adsbygoogle";
      ins.style.display = "block";
      if (adLayout === "in-article") {
        ins.style.textAlign = "center";
      }
      ins.setAttribute("data-ad-client", adClient);
      ins.setAttribute("data-ad-slot", adSlot);
      ins.setAttribute("data-ad-format", adFormat);
      ins.setAttribute(
        "data-full-width-responsive",
        fullWidthResponsive ? "true" : "false"
      );
      if (adLayout) {
        ins.setAttribute("data-ad-layout", adLayout);
      }

      // Append the ins element to the container
      adRef.current.appendChild(ins);
    }

    // Load the AdSense script if it's not already loaded
    const loadAdSenseScript = () => {
      if (!document.querySelector('script[src*="adsbygoogle.js"]')) {
        const script = document.createElement("script");
        script.async = true;
        script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${adClient}`;
        script.crossOrigin = "anonymous";
        document.head.appendChild(script);

        // Wait for script to load before pushing ad
        script.onload = pushAd;
      } else {
        // Script already loaded, push ad directly
        pushAd();
      }
    };

    // Push the ad to AdSense
    const pushAd = () => {
      try {
        // @ts-expect-error: adsbygoogle is not typed on the window object
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      } catch (e) {
        console.error("Error initializing AdSense ad:", e);
      }
    };

    // Start the process
    loadAdSenseScript();

    // Cleanup function
    return () => {
      if (adRef.current) {
        adRef.current.innerHTML = "";
      }
    };
  }, [adClient, adSlot, adFormat, adLayout, fullWidthResponsive]);

  return (
    <div ref={adRef} className={className} data-ad-container="true">
      {/* AdSense ins element will be created dynamically */}
    </div>
  );
}
```

#### AdSlot.tsx

```tsx
"use client";

import React from "react";
import GoogleAdScript from "./GoogleAdScript";

interface AdSlotProps {
  adKey: string; // Unique key for the ad slot (e.g., for Google Ads targeting)
  className?: string; // Optional additional classes
}

const ADSENSE_CLIENT = "ca-pub-3570662930292642";
const TOP_RIGHT_SLOT = "7675098009";
const IN_GRID_SLOT = "8358220755";

const AdSlot: React.FC<AdSlotProps> = ({ adKey, className }) => {
  // Render AdSense ad based on adKey
  if (adKey === "public-card-top-right") {
    return (
      <div className={className} data-ad-key={adKey}>
        <GoogleAdScript
          adClient={ADSENSE_CLIENT}
          adSlot={TOP_RIGHT_SLOT}
          adFormat="auto"
          fullWidthResponsive={true}
          className="w-full h-full"
        />
      </div>
    );
  }

  if (adKey.startsWith("in-grid-")) {
    return (
      <div className={className} data-ad-key={adKey}>
        <GoogleAdScript
          adClient={ADSENSE_CLIENT}
          adSlot={IN_GRID_SLOT}
          adFormat="fluid"
          adLayout="in-article"
          className="w-full h-full"
        />
      </div>
    );
  }

  // Default placeholder
  return (
    <div
      className={`flex items-center justify-center w-full h-full bg-neutral-200 dark:bg-neutral-700 border border-dashed border-neutral-400 dark:border-neutral-600 rounded-lg text-neutral-500 dark:text-neutral-400 text-sm p-4 ${className}`}
      data-ad-key={adKey}
    >
      <div className="text-center">
        <p className="font-medium">Advertisement</p>
        <p className="text-xs">(Slot: {adKey})</p>
      </div>
    </div>
  );
};

export default AdSlot;
```

### Server-Side Implementation

#### app/[cardSlug]/page.tsx

```tsx
// Fetch custom ad if plan allows ads
if (userPlan === "basic" || userPlan === "growth") {
  try {
    // First, check if the custom_ad_targets table exists (for backward compatibility)
    const { count, error: tableCheckError } = await supabase
      .from("custom_ad_targets")
      .select("*", { count: "exact", head: true });

    // If the table exists and migration has been applied
    if (count !== null && !tableCheckError) {
      // Use the get_ad_for_pincode function to find the appropriate ad
      const pincode = businessProfile.pincode || "999999"; // Use a dummy pincode if none provided
      const { data: adData, error: adError } = await supabase.rpc(
        "get_ad_for_pincode",
        { target_pincode: pincode }
      );

      if (adData && adData.length > 0) {
        // Found an ad (either pincode-specific or global)
        topAdData = {
          type: "custom",
          imageUrl: adData[0].ad_image_url,
          linkUrl: adData[0].ad_link_url,
        };
      } else {
        // No custom ads found or error occurred, show placeholder
        if (adError) {
          console.error(`Error fetching ad for pincode ${pincode}:`, adError);
        }
        topAdData = null;
      }
    } else {
      // Fallback to old approach if migration hasn't been applied yet
      // ...
    }
  } catch (adFetchError) {
    console.error(`Error fetching custom ad:`, adFetchError);
    topAdData = { type: "google" }; // fallback on error
  }
}
```

### Client-Side Implementation

#### app/components/PublicCardPageClient.tsx

```tsx
{
  /* Right Column: Dynamic Ad / Placeholder */
}
<div className="flex flex-col justify-start h-full md:sticky md:top-8">
  {/* Ad Slot */}
  <div className="flex-shrink-0 mb-6">
    {topAdData?.type === "custom" && topAdData.imageUrl ? (
      <Link
        href={topAdData.linkUrl || "#"}
        target="_blank"
        rel="noopener noreferrer"
        className="block w-full aspect-video relative rounded-lg overflow-hidden"
      >
        <Image
          src={topAdData.imageUrl}
          alt="Advertisement"
          fill
          style={{ objectFit: "contain" }}
          className="rounded-lg"
        />
      </Link>
    ) : topAdData?.type === "google" ? (
      <div className="w-full aspect-video bg-neutral-100 dark:bg-neutral-800 rounded-lg flex items-center justify-center">
        <AdSlot adKey="public-card-top-right" />
      </div>
    ) : (
      <div className="border border-dashed rounded-lg p-4 flex items-center justify-center h-full w-full text-neutral-500 bg-neutral-50 dark:bg-neutral-800/50 aspect-video">
        {/* Placeholder */}
      </div>
    )}
  </div>

  {/* Review Section */}
  {/* ... */}
</div>;
```

## UI Layout Changes

The public card page layout was updated to:

1. Divide the top section into two equal-width columns for tablet and desktop (single column for mobile)
2. Center the card horizontally in its column
3. Make both columns sticky for better user experience

```tsx
<div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
  {/* Left Column: Card - Sticky on tablet/desktop */}
  <div className="w-full md:sticky md:top-8 h-fit flex justify-center">
    <div className="max-w-[350px] w-full">
      {" "}
      {/* Container to center the card */}
      <BusinessCardPreview
        data={businessProfile}
        isAuthenticated={isAuthenticated}
        userPlan={userPlan}
        totalLikes={businessProfile.total_likes ?? 0}
        totalSubscriptions={businessProfile.total_subscriptions ?? 0}
        averageRating={businessProfile.average_rating ?? 0}
        isSubscribed={isSubscribed}
        hasLiked={hasLiked}
        isLoadingInteraction={isLoadingInteraction}
        onSubscribe={handleSubscribe}
        onUnsubscribe={handleUnsubscribe}
        onLike={handleLike}
        onUnlike={handleUnlike}
      />
    </div>
  </div>

  {/* Right Column: Dynamic Ad / Placeholder - Sticky on tablet/desktop */}
  <div className="flex flex-col justify-start h-full md:sticky md:top-8">
    {/* Ad content */}
  </div>
</div>
```

## Migration Process

The migration from the old JSONB-based approach to the new junction table approach was implemented with backward compatibility in mind:

1. Created a backup of existing data
2. Created the new junction table structure
3. Migrated data from the JSONB format to the new structure
4. Updated the server-side code to check if the migration has been applied and use the appropriate approach

This ensures a smooth transition without any downtime or data loss.
