{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/qr/qr-scanner.css"], "sourcesContent": ["/* Custom styles for html5-qrcode scanner to match Dukancard theme */\r\n\r\n/* Scanner container */\r\n#qr-scanner-region {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: #000;\r\n}\r\n\r\n/* Scanner video element */\r\n#qr-scanner-region video {\r\n  border-radius: 12px;\r\n  width: 100% !important;\r\n  height: auto !important;\r\n  object-fit: cover;\r\n}\r\n\r\n/* Scanner overlay */\r\n#qr-scanner-region canvas {\r\n  border-radius: 12px;\r\n}\r\n\r\n/* Control buttons styling */\r\n#qr-scanner-region button {\r\n  background: rgba(212, 175, 55, 0.9) !important; /* Gold theme */\r\n  border: none !important;\r\n  border-radius: 8px !important;\r\n  color: white !important;\r\n  font-weight: 500 !important;\r\n  padding: 8px 16px !important;\r\n  margin: 4px !important;\r\n  transition: all 0.2s ease !important;\r\n}\r\n\r\n#qr-scanner-region button:hover {\r\n  background: rgba(212, 175, 55, 1) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* Torch/flashlight button */\r\n#qr-scanner-region button[title*=\"torch\"],\r\n#qr-scanner-region button[title*=\"Torch\"],\r\n#qr-scanner-region button[title*=\"flash\"],\r\n#qr-scanner-region button[title*=\"Flash\"] {\r\n  background: rgba(255, 255, 255, 0.1) !important;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n}\r\n\r\n/* Camera selection dropdown */\r\n#qr-scanner-region select {\r\n  background: rgba(0, 0, 0, 0.8) !important;\r\n  color: white !important;\r\n  border: 1px solid rgba(212, 175, 55, 0.5) !important;\r\n  border-radius: 6px !important;\r\n  padding: 6px 12px !important;\r\n  font-size: 14px !important;\r\n}\r\n\r\n/* Scanner region text */\r\n#qr-scanner-region span,\r\n#qr-scanner-region div {\r\n  color: white !important;\r\n  font-family: inherit !important;\r\n}\r\n\r\n/* Hide default file input styling */\r\n#qr-scanner-region input[type=\"file\"] {\r\n  background: rgba(212, 175, 55, 0.9) !important;\r\n  color: white !important;\r\n  border: none !important;\r\n  border-radius: 8px !important;\r\n  padding: 8px 16px !important;\r\n  cursor: pointer !important;\r\n}\r\n\r\n/* Scanner status text */\r\n#qr-scanner-region .qr-scanner-status {\r\n  background: rgba(0, 0, 0, 0.7) !important;\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 8px !important;\r\n  padding: 8px 12px !important;\r\n  margin: 8px !important;\r\n}\r\n\r\n/* Permission request styling */\r\n#qr-scanner-region .qr-scanner-permission {\r\n  background: rgba(212, 175, 55, 0.1) !important;\r\n  border: 1px solid rgba(212, 175, 55, 0.3) !important;\r\n  border-radius: 12px !important;\r\n  padding: 20px !important;\r\n  text-align: center !important;\r\n}\r\n\r\n/* Error messages */\r\n#qr-scanner-region .qr-scanner-error {\r\n  background: rgba(239, 68, 68, 0.1) !important;\r\n  border: 1px solid rgba(239, 68, 68, 0.3) !important;\r\n  border-radius: 8px !important;\r\n  padding: 12px !important;\r\n  color: #ef4444 !important;\r\n}\r\n\r\n/* Success messages */\r\n#qr-scanner-region .qr-scanner-success {\r\n  background: rgba(34, 197, 94, 0.1) !important;\r\n  border: 1px solid rgba(34, 197, 94, 0.3) !important;\r\n  border-radius: 8px !important;\r\n  padding: 12px !important;\r\n  color: #22c55e !important;\r\n}\r\n\r\n/* Loading spinner */\r\n#qr-scanner-region .qr-scanner-loading {\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  justify-content: center !important;\r\n  padding: 20px !important;\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 640px) {\r\n  #qr-scanner-region {\r\n    border-radius: 8px;\r\n  }\r\n  \r\n  #qr-scanner-region button {\r\n    padding: 6px 12px !important;\r\n    font-size: 14px !important;\r\n  }\r\n  \r\n  #qr-scanner-region select {\r\n    font-size: 12px !important;\r\n    padding: 4px 8px !important;\r\n  }\r\n}\r\n\r\n/* Dark mode adjustments */\r\n@media (prefers-color-scheme: dark) {\r\n  #qr-scanner-region {\r\n    background: #0a0a0a;\r\n  }\r\n  \r\n  #qr-scanner-region span,\r\n  #qr-scanner-region div {\r\n    color: #f5f5f5 !important;\r\n  }\r\n}\r\n\r\n/* Animation for scanner frame */\r\n@keyframes qr-scanner-pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 10px rgba(212, 175, 55, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);\r\n  }\r\n}\r\n\r\n#qr-scanner-region .qr-scanner-active {\r\n  animation: qr-scanner-pulse 2s infinite;\r\n}\r\n"], "names": [], "mappings": "AAGA;;;;;;AAOA;;;;;;;AAQA;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;AAUA;;;;;;;;;AAUA;;;;;AAOA;;;;;;;;;AAUA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AASA;;;;;;;AAQA;EACE;;;;EAIA;;;;;EAKA;;;;;;AAOF;EACE;;;;EAIA;;;;;AAOF;;;;;;;;;;;;;;AAYA", "debugId": null}}]}