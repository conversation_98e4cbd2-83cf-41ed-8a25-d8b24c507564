"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// Alert components not used in this file
import { ArrowRight, Crown, ShieldCheck } from "lucide-react";
import Link from "next/link";
import { PricingPlan } from "@/lib/PricingPlans";
import FlipTimer from "./FlipTimer";
import { cn } from "@/lib/utils";
import { SUBSCRIPTION_STATUS } from "@/lib/razorpay/webhooks/handlers/utils";

interface AnimatedSubscriptionStatusProps {
  subscriptionStatus: "active" | "trial" | "inactive";
  planDetails?: PricingPlan;
  trialEndDate?: string | null;
  planCycle?: string | null;
  subscription?: {
    subscription_status?: string | null;
    plan_id?: string | null;
    plan_cycle?: string | null;
  } | null;
}

export default function AnimatedSubscriptionStatus({
  subscriptionStatus,
  planDetails,
  trialEndDate,
  planCycle,
  subscription,
}: AnimatedSubscriptionStatusProps) {

  // Get plan icon based on plan ID - used in badge rendering
  const getPlanIcon = () => {
    if (!planDetails) return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;

    switch (planDetails.id) {
      case "premium":
      case "enterprise":
        return <Crown className="w-3.5 h-3.5 mr-1" />;
      case "pro":
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
      case "growth":
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
      case "basic":
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
      case "free":
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
      default:
        return <ShieldCheck className="w-3.5 h-3.5 mr-1" />;
    }
  };

  // Get badge styles based on plan ID
  const getPlanBadgeStyles = () => {
    if (!planDetails) return "";

    switch (planDetails.id) {
      case "premium":
      case "enterprise":
        return "border-[var(--brand-gold)]/50 text-[var(--brand-gold)]";
      case "pro":
        return "border-blue-500/50 text-blue-500 dark:border-blue-400/50 dark:text-blue-400";
      case "growth":
        return "border-green-500/50 text-green-500 dark:border-green-400/50 dark:text-green-400";
      case "basic":
        return "border-purple-500/50 text-purple-500 dark:border-purple-400/50 dark:text-purple-400";
      case "free":
        return "border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400";
      default:
        return "border-green-500/50 text-green-500 dark:border-green-400/50 dark:text-green-400";
    }
  };

  return (
    <div className="h-full">
      <div
        className="group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6 h-full flex flex-col"
      >
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40" />

        <div className="relative z-10 flex flex-col h-full">
          <div className="flex items-center justify-between gap-3 mb-6">
            <div className="space-y-1">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
                  <ShieldCheck className="w-5 h-5 text-primary" />
                </div>
                <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
                <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
                  Subscription
                </div>
              </div>
              <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
                Plan Status
              </h3>
            </div>
            {subscriptionStatus === "active" && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-emerald-500" />
                <span className="text-xs font-medium text-emerald-600 dark:text-emerald-400">Active</span>
              </div>
            )}
            {subscriptionStatus === "trial" && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-amber-500" />
                <span className="text-xs font-medium text-amber-600 dark:text-amber-400">Trial</span>
              </div>
            )}
          </div>

          <div className="flex-1 flex flex-col justify-center">
            {subscriptionStatus === "active" && planDetails && (
              <div className="flex flex-col items-center justify-center text-center">
                <div>
                  <Badge
                    variant="outline"
                    className={cn("border px-2 py-1 mb-2", getPlanBadgeStyles())}
                  >
                    {getPlanIcon()}
                    {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? "Paused" : "Active"}
                  </Badge>
                </div>

                <h3 className="text-base sm:text-lg font-medium mb-1">
                  {planDetails.name}
                </h3>
                {/* Only show plan cycle for paid plans, not for free plan */}
                {planDetails.id !== "free" && (
                  <p className="text-xs sm:text-sm font-medium text-primary mb-1">
                    {planCycle === "yearly" ? "Yearly Plan" : "Monthly Plan"}
                  </p>
                )}

                {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? (
                  <p className="text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3">
                    Your subscription is currently paused. Your business card is offline until you resume your subscription.
                  </p>
                ) : (
                  <p className="text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3">
                    Your subscription is active and all features are enabled
                  </p>
                )}

                <div className="mt-2 w-full">
                  <Button
                    asChild
                    variant="outline"
                    className="w-full border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-all duration-200 px-6 py-2.5 h-auto font-medium"
                    size="lg"
                  >
                    <Link href="/dashboard/business/plan" className="flex items-center justify-center gap-2">
                      <span className="whitespace-nowrap">Manage Plan</span>
                      <ArrowRight className="w-4 h-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            )}

            {subscriptionStatus === "trial" && trialEndDate && (
              <div className="flex flex-col items-center justify-center text-center">
                <div>
                  <Badge
                    variant="outline"
                    className="border border-[var(--brand-gold)]/50 text-[var(--brand-gold)] px-2 py-1 mb-2"
                  >
                    <Crown className="w-3.5 h-3.5 mr-1" />
                    Trial
                  </Badge>
                </div>

                <h3 className="text-base sm:text-lg font-medium mb-1">
                  {planDetails ? planDetails.name : "Premium"} Trial
                </h3>
                {/* Only show plan cycle for paid plans, not for free plan */}
                {planDetails && planDetails.id !== "free" && (
                  <p className="text-xs sm:text-sm font-medium text-[var(--brand-gold)] mb-2">
                    {planCycle === "yearly" ? "Yearly Plan" : "Monthly Plan"}
                  </p>
                )}

                <div className="w-full overflow-x-auto px-1">
                  <FlipTimer endDate={trialEndDate} label="" tooltipText="Your trial will expire soon. Choose a plan to continue using all features." />
                </div>

                <div className="mt-4 w-full">
                  <Button
                    asChild
                    className="w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium"
                    size="lg"
                  >
                    <Link href="/dashboard/business/plan" className="flex items-center justify-center gap-2">
                      <span className="whitespace-nowrap">Choose a Plan</span>
                      <ArrowRight className="w-4 h-4" />
                    </Link>
                  </Button>
                </div>


              </div>
            )}

            {subscriptionStatus === "inactive" && (
              <div className="flex flex-col items-center justify-center text-center">
                <div>
                  <Badge
                    variant="outline"
                    className={cn("border px-2 py-1 mb-2",
                      subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails
                        ? getPlanBadgeStyles()
                        : "border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400"
                    )}
                  >
                    {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails
                      ? getPlanIcon()
                      : <ShieldCheck className="w-3.5 h-3.5 mr-1" />
                    }
                    {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? "Paused" : "Free Plan"}
                  </Badge>
                </div>

                <h3 className="text-base sm:text-lg font-medium mb-1">
                  {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails
                    ? planDetails.name
                    : "Free Plan"
                  }
                </h3>

                {/* Only show plan cycle for paid plans with halted subscription */}
                {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails && planDetails.id !== "free" && (
                  <p className="text-xs sm:text-sm font-medium text-primary mb-1">
                    {planCycle === "yearly" ? "Yearly Plan" : "Monthly Plan"}
                  </p>
                )}

                {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? (
                  <p className="text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3">
                    Your subscription is currently paused. Your business card is offline until you resume your subscription.
                  </p>
                ) : (
                  <p className="text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3">
                    You are on the free plan with limited features. Upgrade to access more features.
                  </p>
                )}

                <div className="mt-2 w-full">
                  <Button
                    asChild
                    className="w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium"
                    size="lg"
                  >
                    <Link href="/dashboard/business/plan" className="flex items-center justify-center gap-2">
                      <span className="whitespace-nowrap">
                        {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? "Manage Plan" : "Upgrade Plan"}
                      </span>
                      <ArrowRight className="w-4 h-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
