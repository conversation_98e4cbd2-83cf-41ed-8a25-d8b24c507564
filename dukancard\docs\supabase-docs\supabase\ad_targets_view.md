# Ad Targets View Documentation

## View Overview

The `ad_targets_view` in the Dukancard application provides a unified interface for viewing custom ads along with their targeting information. This view combines data from `custom_ads` and `custom_ad_targets` tables to show which ads are targeted to which locations, along with expiry status information.

## View Purpose

This view simplifies ad management and targeting queries by:
- Combining ad content with targeting information in a single query
- Providing computed expiry status for quick filtering
- Enabling efficient location-based ad serving
- Supporting ad analytics and reporting

## View Schema

| Column Name | Data Type | Source | Description |
|-------------|-----------|---------|-------------|
| ad_id | uuid | custom_ads.id | Unique identifier for the ad |
| ad_image_url | text | custom_ads.ad_image_url | URL to the ad image |
| ad_link_url | text | custom_ads.ad_link_url | URL that the ad links to when clicked |
| is_active | boolean | custom_ads.is_active | Whether the ad is currently active |
| expiry_date | timestamptz | custom_ads.expiry_date | When the ad expires (null for no expiry) |
| expiry_status | text | Computed | 'Active', 'Expiring Soon', 'Expired', or NULL |
| target_id | uuid | custom_ad_targets.id | Unique identifier for the targeting rule |
| pincode | text | custom_ad_targets.pincode | Pincode this ad targets (null for global) |
| is_global | boolean | custom_ad_targets.is_global | Whether this targeting rule is global |
| ad_created_at | timestamptz | custom_ads.created_at | When the ad was created |
| target_created_at | timestamptz | custom_ad_targets.created_at | When the targeting rule was created |

## View Definition

```sql
CREATE VIEW ad_targets_view AS
SELECT
    c.id AS ad_id,
    c.ad_image_url,
    c.ad_link_url,
    c.is_active,
    c.expiry_date,
    CASE
        WHEN c.expiry_date IS NULL THEN NULL
        WHEN c.expiry_date <= now() THEN 'Expired'
        WHEN c.expiry_date <= (now() + INTERVAL '7 days') THEN 'Expiring Soon'
        ELSE 'Active'
    END AS expiry_status,
    t.id AS target_id,
    t.pincode,
    t.is_global,
    c.created_at AS ad_created_at,
    t.created_at AS target_created_at
FROM custom_ads c
JOIN custom_ad_targets t ON c.id = t.ad_id
ORDER BY c.created_at DESC, t.created_at DESC;
```

## Security Configuration

This view follows Supabase security best practices:
- **No SECURITY DEFINER**: View uses the permissions of the querying user (not the view creator)
- **Inherited RLS**: Row Level Security policies from source tables are automatically applied
- **User-based access**: Each user sees only the ads and targets they're authorized to access

## Expiry Status Logic

The `expiry_status` field provides quick filtering capabilities:

- **NULL**: Ad has no expiry date (runs indefinitely)
- **'Active'**: Ad expires more than 7 days from now
- **'Expiring Soon'**: Ad expires within the next 7 days
- **'Expired'**: Ad expiry date has passed

## Usage Patterns

### Location-Based Ad Serving
```sql
-- Get active ads for a specific pincode
SELECT ad_id, ad_image_url, ad_link_url
FROM ad_targets_view 
WHERE is_active = true 
  AND expiry_status IN ('Active', 'Expiring Soon')
  AND (pincode = '769001' OR is_global = true)
ORDER BY ad_created_at DESC;

-- Get global ads only
SELECT ad_id, ad_image_url, ad_link_url
FROM ad_targets_view 
WHERE is_active = true 
  AND expiry_status IN ('Active', 'Expiring Soon')
  AND is_global = true;
```

### Ad Management Queries
```sql
-- Get ads expiring soon for alerts
SELECT ad_id, ad_image_url, expiry_date, pincode
FROM ad_targets_view 
WHERE expiry_status = 'Expiring Soon'
ORDER BY expiry_date ASC;

-- Get all targeting rules for a specific ad
SELECT target_id, pincode, is_global
FROM ad_targets_view 
WHERE ad_id = 'specific-ad-uuid'
ORDER BY target_created_at DESC;

-- Count active ads by location
SELECT 
    COALESCE(pincode, 'Global') as location,
    COUNT(DISTINCT ad_id) as active_ads
FROM ad_targets_view 
WHERE is_active = true 
  AND expiry_status IN ('Active', 'Expiring Soon')
GROUP BY pincode
ORDER BY active_ads DESC;
```

### Analytics Queries
```sql
-- Ad performance by expiry status
SELECT 
    expiry_status,
    COUNT(DISTINCT ad_id) as ad_count,
    COUNT(target_id) as total_targets
FROM ad_targets_view 
GROUP BY expiry_status;

-- Most targeted locations
SELECT 
    pincode,
    COUNT(DISTINCT ad_id) as ads_targeting_location
FROM ad_targets_view 
WHERE pincode IS NOT NULL
GROUP BY pincode
ORDER BY ads_targeting_location DESC
LIMIT 10;
```

## Performance Considerations

### Indexes Used
The view leverages indexes from source tables:
- **custom_ads**: Primary key (id), created_at, is_active, expiry_date
- **custom_ad_targets**: Primary key (id), ad_id (foreign key), pincode

### Query Optimization
- **JOIN optimization**: Uses indexed foreign key relationship
- **Computed fields**: Expiry status calculated at query time
- **Ordering**: Pre-sorted by creation dates for common use cases

## Related Tables

### Source Tables
- **custom_ads**: Contains ad content and metadata
- **custom_ad_targets**: Contains targeting rules for ads

### Related Functionality
- **Ad serving**: Used to determine which ads to show to users
- **Ad management**: Used in admin interfaces for ad oversight
- **Analytics**: Used for reporting and performance tracking

## Best Practices

### Query Optimization
1. **Filter by is_active first**: Most queries should filter active ads
2. **Use expiry_status**: More efficient than date comparisons
3. **Index-friendly filters**: Use pincode and is_global for location filtering
4. **Limit results**: Use appropriate LIMIT for pagination

### Application Integration
1. **Cache frequently accessed data**: Global ads and popular location ads
2. **Handle NULL expiry_date**: Ads with no expiry run indefinitely
3. **Monitor expiring ads**: Set up alerts for 'Expiring Soon' status
4. **Respect targeting rules**: Always check both pincode and global targeting

## Monitoring and Maintenance

### Performance Monitoring
- Monitor query execution times, especially for location-based queries
- Track index usage on source tables
- Watch for slow queries during peak ad serving

### Maintenance Tasks
- **Regular cleanup**: Remove expired ads and unused targeting rules
- **Index optimization**: Monitor and optimize source table indexes
- **Analytics updates**: Keep ad performance metrics current

This view provides efficient ad targeting and management capabilities while maintaining good performance for real-time ad serving scenarios.
