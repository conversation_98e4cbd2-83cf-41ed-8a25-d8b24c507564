"use client";

import { motion } from "framer-motion";
import { Heart, Star, UserPlus } from "lucide-react";
import { formatIndianNumberShort } from "@/lib/utils";
import EnhancedMetricCard from "./EnhancedMetricCard";

interface BusinessProfile {
  total_likes: number;
  total_subscriptions: number;
  average_rating: number;
}

interface EngagementMetricsSectionProps {
  profile: BusinessProfile;
  initialProfile: BusinessProfile;
}

export default function EnhancedEngagementMetricsSection({
  profile,
  initialProfile,
}: EngagementMetricsSectionProps) {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      }
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="space-y-6">
        {/* Section Header */}
        <motion.div variants={headerVariants}>
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
                <Heart className="w-5 h-5 text-primary" />
              </div>
              <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
              <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
                User Engagement
              </div>
            </div>
            <div className="space-y-1">
              <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
                Engagement Metrics
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed">
                Track how users interact with your business card and measure customer engagement levels.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Metrics Cards */}
        <div className="grid gap-6 grid-cols-1 md:grid-cols-3">
        {/* Total Likes */}
        <EnhancedMetricCard
          title="Total Likes"
          value={formatIndianNumberShort(profile.total_likes)}
          icon={Heart}
          description="People who liked your card"
          color="rose"
          isUpdated={profile.total_likes !== initialProfile.total_likes}
        />

        {/* Total Subscribers */}
        <EnhancedMetricCard
          title="Total Subscribers"
          value={formatIndianNumberShort(profile.total_subscriptions)}
          icon={UserPlus}
          description="People subscribed to updates"
          color="blue"
          isUpdated={profile.total_subscriptions !== initialProfile.total_subscriptions}
        />

        {/* Average Rating */}
        <EnhancedMetricCard
          title="Average Rating"
          value={profile.average_rating?.toFixed(1) || "0.0"}
          suffix="/5.0"
          icon={Star}
          description="Average customer rating"
          color="amber"
          isUpdated={profile.average_rating !== initialProfile.average_rating}
        />
        </div>
      </div>
    </motion.div>
  );
}
