import { motion } from "framer-motion";
import { Info } from "lucide-react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import Image from "next/image";
import { cn } from "@/lib/utils";
import { GalleryImage } from "../types";
import { ReorderState } from "../types/galleryTypes";
import SortableImageItem from "./SortableImageItem";
import StaticImageItem from "./StaticImageItem";
import ReorderControls from "./ReorderControls";
import EmptyGallery from "./EmptyGallery";

interface GalleryGridProps {
  images: GalleryImage[];
  imagesCount: number;
  galleryLimit: number;
  canAddMore: boolean;
  isDragging: boolean;
  userPlan: string;
  isClient: boolean;
  reorderState: ReorderState;
  activeId: string | null;
  onDragStart: (_event: DragStartEvent) => void;
  onDragEnd: (_event: DragEndEvent) => void;
  onSaveOrder: () => void;
  onResetOrder: () => void;
  onViewImage: (_url: string) => void;
  onDeleteImage: (_image: GalleryImage) => void;
  onUploadClick: () => void;
}

export default function GalleryGrid({
  images: _images,
  imagesCount,
  galleryLimit,
  canAddMore,
  isDragging,
  userPlan,
  isClient,
  reorderState,
  activeId,
  onDragStart,
  onDragEnd,
  onSaveOrder,
  onResetOrder,
  onViewImage,
  onDeleteImage,
  onUploadClick,
}: GalleryGridProps) {
  // Drag and drop sensors for reordering
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px of movement before activating drag
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  return (
    <motion.div
      className="space-y-8"
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.2 }}
    >
      {/* Section Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
            Gallery Collection
          </div>
          <div className="h-px flex-1 bg-gradient-to-r from-neutral-200 to-transparent dark:from-neutral-700" />
          <div className="flex items-center gap-2 text-sm text-neutral-500 dark:text-neutral-400">
            <span className="font-medium text-neutral-900 dark:text-neutral-100">{imagesCount}</span>
            <span>/</span>
            <span>{galleryLimit === Infinity ? "∞" : galleryLimit}</span>
            <Info className="h-4 w-4 opacity-60" />
          </div>
        </div>
        <p className="text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed">
          {imagesCount === 0
            ? "Start building your gallery by uploading your first image."
            : `Manage your ${imagesCount} image${imagesCount === 1 ? '' : 's'} with drag-and-drop reordering.`
          }
        </p>
      </div>

      {/* Gallery Content */}
      <div className="relative">
        {reorderState.orderedImages.length === 0 ? (
          <EmptyGallery
            canAddMore={canAddMore}
            isDragging={isDragging}
            userPlan={userPlan}
            galleryLimit={galleryLimit}
            onUploadClick={onUploadClick}
          />
        ) : (
          <div className="space-y-4">
            {/* Reorder Instructions and Controls - Only show when client-side and drag is available */}
            {reorderState.orderedImages.length > 1 && isClient && (
              <ReorderControls
                isReordering={reorderState.isReordering}
                hasUnsavedChanges={reorderState.hasUnsavedChanges}
                isSavingOrder={reorderState.isSavingOrder}
                onSaveOrder={onSaveOrder}
                onResetOrder={onResetOrder}
              />
            )}

            {isClient ? (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragStart={onDragStart}
                onDragEnd={onDragEnd}
              >
                <SortableContext
                  items={reorderState.orderedImages.map(img => img.id)}
                  strategy={rectSortingStrategy}
                >
                  <div
                    className={cn(
                      "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 transition-all duration-200",
                      reorderState.isReordering && "bg-primary/5 rounded-xl p-4"
                    )}
                  >
                    {reorderState.orderedImages.map((image) => (
                      <SortableImageItem
                        key={image.id}
                        image={image}
                        onViewImage={onViewImage}
                        onDeleteImage={onDeleteImage}
                      />
                    ))}
                  </div>
                </SortableContext>

                <DragOverlay>
                  {activeId ? (
                    <div className="aspect-square relative overflow-hidden rounded-lg border shadow-lg opacity-90 transform rotate-3 scale-105">
                      {(() => {
                        const activeImage = reorderState.orderedImages.find(img => img.id === activeId);
                        return activeImage ? (
                          <Image
                            src={activeImage.url}
                            alt="Dragging image"
                            fill
                            className="object-cover"
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          />
                        ) : null;
                      })()}
                    </div>
                  ) : null}
                </DragOverlay>
              </DndContext>
            ) : (
              // Fallback for server-side rendering - static grid without drag functionality
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {reorderState.orderedImages.map((image) => (
                  <StaticImageItem
                    key={image.id}
                    image={image}
                    onViewImage={onViewImage}
                    onDeleteImage={onDeleteImage}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
}
