"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import { Settings } from "lucide-react";
import { toast } from "sonner";

import { DeleteAccountSection } from "../DeleteAccountSection";
import LinkEmailSection from "./LinkEmailSection";
import LinkPhoneSection from "./LinkPhoneSection";

interface SettingsPageClientProps {
  currentEmail: string | undefined;
  currentPhone: string | null | undefined;
  registrationType: 'google' | 'email' | 'phone';
}

export default function SettingsPageClient({
  currentEmail,
  currentPhone,
  registrationType,
}: SettingsPageClientProps) {
  // Check for email change success on component mount
  useEffect(() => {
    // Check URL params for email change success
    const urlParams = new URLSearchParams(window.location.search);
    const emailChangeSuccess = urlParams.get('email_change_success');

    if (emailChangeSuccess === 'true') {
      toast.success("Email address updated successfully!", {
        description: "Your email address has been changed and verified.",
        duration: 5000,
      });

      // Clean up URL params
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, []);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Modern SaaS Header - Full Width */}
      <motion.div
        className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        <div className="space-y-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
              <Settings className="w-5 h-5 text-primary" />
            </div>
            <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
            <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
              Account Management
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
            Account Settings
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed">
            Manage your account security, contact information, and preferences. Keep your profile up to date.
          </p>
        </div>
      </motion.div>

      {/* Enhanced Settings Sections - Full Width */}
      <div className="space-y-12">
        {/* Link Email - Show for all users with different behaviors */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          <LinkEmailSection
            currentEmail={currentEmail}
            currentPhone={currentPhone}
            registrationType={registrationType}
          />
        </motion.div>

        {/* Link Phone - Show for all users with different behaviors */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <LinkPhoneSection
            currentEmail={currentEmail}
            currentPhone={currentPhone}
            registrationType={registrationType}
          />
        </motion.div>



        {/* Delete Account Section */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <DeleteAccountSection />
        </motion.div>
      </div>
    </motion.div>
  );
}
