import { useMemo } from 'react';
import { LikeList, LikeData } from '@/app/components/shared/likes';
import { LikeWithProfile } from './actions';

interface LikeListClientProps {
  initialLikes: LikeWithProfile[];
}

export default function LikeListClient({ initialLikes }: LikeListClientProps) {
  // Transform the data to match the shared component interface
  const transformedLikes: LikeData[] = useMemo(() => {
    return initialLikes.map(like => ({
      id: like.id,
      profile: like.business_profiles ? {
        id: like.business_profiles.id,
        name: like.business_profiles.business_name,
        slug: like.business_profiles.business_slug,
        logo_url: like.business_profiles.logo_url,
        city: like.business_profiles.city,
        state: like.business_profiles.state,
        locality: like.business_profiles.locality,
        type: 'business' as const,
      } : null
    })).filter(like => like.profile !== null) as LikeData[];
  }, [initialLikes]);

  return (
    <LikeList
      initialLikes={transformedLikes}
      showUnlike={true}
      emptyMessage="You haven't liked any businesses yet."
      emptyDescription="Like businesses to see them here and get updates."
      showDiscoverButton={true}
      showVisitButton={true}
      showAddress={true}
      showRedirectIcon={false}
    />
  );
}