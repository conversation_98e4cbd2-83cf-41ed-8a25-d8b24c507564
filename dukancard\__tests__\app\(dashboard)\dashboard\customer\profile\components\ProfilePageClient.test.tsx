import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ProfilePageClient from '@/app/(dashboard)/dashboard/customer/profile/components/ProfilePageClient';
import { toast } from 'sonner';
import { updateCustomerProfileAndAddress } from '@/app/(dashboard)/dashboard/customer/profile/actions';
import { act } from '@testing-library/react';

// Helper to store the ref.current from mocked components
const mockProfileFormGetFormData = jest.fn();
const mockProfileFormValidateForm = jest.fn();
const mockProfileFormGetFormErrors = jest.fn();

// Mock child components directly within jest.mock
jest.mock('@/app/(dashboard)/dashboard/customer/profile/ProfileForm', () => ({
  ProfileForm: React.forwardRef((props: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      getFormData: mockProfileFormGetFormData,
      validateForm: mockProfileFormValidateForm,
      getFormErrors: mockProfileFormGetFormErrors,
    }));
    const { initialName, hideSubmitButton, ...rest } = props; // Consume props
    return <div data-testid="profile-form" {...rest} />;
  }),
}));

const mockAddressFormGetFormData = jest.fn();
const mockAddressFormValidateForm = jest.fn();
const mockAddressFormGetFormErrors = jest.fn();

jest.mock('@/app/(dashboard)/dashboard/customer/profile/components/AddressForm', () => ({
  __esModule: true,
  default: React.forwardRef((props: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      getFormData: mockAddressFormGetFormData,
      validateForm: mockAddressFormValidateForm,
      getFormErrors: mockAddressFormGetFormErrors,
    }));
    const { initialData, hideSubmitButton, ...rest } = props; // Consume props
    return <div data-testid="address-form" {...rest} />;
  }),
}));

jest.mock('@/app/(dashboard)/dashboard/customer/profile/components/AvatarUpload', () => ({
  __esModule: true,
  default: jest.fn(({ onUpdateAvatar, initialAvatarUrl, userName }: any) => {
    return (
      <div data-testid="avatar-upload">
        <img src={initialAvatarUrl || ''} alt={userName || 'User'} />
        <button onClick={() => onUpdateAvatar('new-avatar-url.jpg')}>Update Avatar</button>
      </div>
    );
  }),
}));

jest.mock('@/app/(dashboard)/dashboard/customer/profile/components/ProfileRequirementDialog', () => ({
  __esModule: true,
  default: jest.fn(({ hasCompleteAddress }: any) => (
    <div data-testid="profile-requirement-dialog">
      {hasCompleteAddress ? 'Address Complete' : 'Address Incomplete'}
    </div>
  )),
}));

// Mock server actions
jest.mock('@/app/(dashboard)/dashboard/customer/profile/actions', () => ({
  updateCustomerProfileAndAddress: jest.fn(),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

describe('ProfilePageClient', () => {
  const initialProps = {
    initialName: 'John Doe',
    initialAvatarUrl: 'http://example.com/avatar.jpg',
    initialAddressData: {
      address: '123 Main St',
      pincode: '123456',
      city: 'Anytown',
      state: 'Anystate',
      locality: 'Anylocality',
    },
    hasCompleteAddress: true,
  };

  beforeEach(() => {
    jest.useFakeTimers();
    jest.clearAllMocks();
    (updateCustomerProfileAndAddress as jest.Mock).mockResolvedValue({ success: true, message: 'Profile updated successfully!' });
    mockProfileFormGetFormData.mockReturnValue({ name: 'Test User' });
    mockProfileFormValidateForm.mockReturnValue(true);
    mockProfileFormGetFormErrors.mockReturnValue({});
    mockAddressFormGetFormData.mockReturnValue({
      address: '123 Test St',
      pincode: '123456',
      city: 'Test City',
      state: 'Test State',
      locality: 'Test Locality',
    });
    mockAddressFormValidateForm.mockReturnValue(true);
    mockAddressFormGetFormErrors.mockReturnValue({});
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('renders correctly with initial props', () => {
    render(<ProfilePageClient {...initialProps} />);

    expect(screen.getByText('Profile Management')).toBeInTheDocument();
    expect(screen.getByText('Manage your personal information and preferences')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByTestId('avatar-upload')).toBeInTheDocument();
    expect(screen.getByTestId('profile-form')).toBeInTheDocument();
    expect(screen.getByTestId('address-form')).toBeInTheDocument();
    expect(screen.getByTestId('profile-requirement-dialog')).toHaveTextContent('Address Complete');
    expect(screen.getByRole('button', { name: /Save Profile/i })).toBeInTheDocument();
  });

  it('updates avatarUrl state when AvatarUpload calls onUpdateAvatar', () => {
    render(<ProfilePageClient {...initialProps} />);
    const avatarUploadButton = screen.getByText('Update Avatar');
    fireEvent.click(avatarUploadButton);
    const avatarImage = screen.getByAltText(initialProps.initialName || 'User');
    expect(avatarImage).toHaveAttribute('src', 'new-avatar-url.jpg');
  });

  it('calls handleUnifiedSubmit on Save Profile button click and shows success toast', async () => {
    render(<ProfilePageClient {...initialProps} />);
    const saveButton = screen.getByRole('button', { name: /Save Profile/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(updateCustomerProfileAndAddress).toHaveBeenCalledTimes(1);
      expect(toast.success).toHaveBeenCalledWith('Profile updated successfully!');
    });
  });

  it('shows error toast if profile form validation fails', async () => {
    render(<ProfilePageClient {...initialProps} />);

    mockProfileFormGetFormData.mockReturnValueOnce({ name: '' });
    mockProfileFormValidateForm.mockReturnValueOnce(false);
    mockProfileFormGetFormErrors.mockReturnValueOnce({ name: ['Name cannot be empty'] });

    const saveButton = screen.getByRole('button', { name: /Save Profile/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Please check your profile information. Name is required.');
      expect(updateCustomerProfileAndAddress).not.toHaveBeenCalled();
    });
  });

  it('shows error toast if address form validation fails when partial data is present', async () => {
    render(<ProfilePageClient {...initialProps} />);

    mockAddressFormGetFormData.mockReturnValueOnce({
      address: '123 Test St',
      pincode: '123', // Invalid pincode
      city: 'Test City',
      state: 'Test State',
      locality: 'Test Locality',
    });
    mockAddressFormValidateForm.mockReturnValueOnce(false);
    mockAddressFormGetFormErrors.mockReturnValueOnce({ pincode: ['Must be a valid 6-digit pincode'] });

    const saveButton = screen.getByRole('button', { name: /Save Profile/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Please complete all required address fields or leave them empty');
      expect(updateCustomerProfileAndAddress).not.toHaveBeenCalled();
    });
  });

  it('handles server action failure and shows error toast', async () => {
    (updateCustomerProfileAndAddress as jest.Mock).mockResolvedValueOnce({ success: false, message: 'Server error occurred' });

    render(<ProfilePageClient {...initialProps} />);
    const saveButton = screen.getByRole('button', { name: /Save Profile/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(updateCustomerProfileAndAddress).toHaveBeenCalledTimes(1);
      expect(toast.error).toHaveBeenCalledWith('Server error occurred');
    });
  });

  it('disables save button during submission', async () => {
    (updateCustomerProfileAndAddress as jest.Mock).mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ success: true }), 100)));

    render(<ProfilePageClient {...initialProps} />);

    // Click the button
    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: /Save Profile/i }));
    });

    // Wait for the button to become disabled and the "Saving..." text to appear
    const savingButton = await screen.findByRole('button', { name: /Saving.../i });
    expect(savingButton).toBeDisabled();

    // Advance timers
    await act(async () => {
      jest.runAllTimers();
    });

    // Wait for the button to become re-enabled and "Saving..." to disappear
    const saveProfileButton = await screen.findByRole('button', { name: /Save Profile/i });
    expect(saveProfileButton).not.toBeDisabled();
    expect(screen.queryByText('Saving...')).not.toBeInTheDocument();
  });

  it('handles unexpected errors during submission', async () => {
    (updateCustomerProfileAndAddress as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    render(<ProfilePageClient {...initialProps} />);
    const saveButton = screen.getByRole('button', { name: /Save Profile/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('An unexpected error occurred. Please try again.');
      expect(screen.getByText('An unexpected error occurred. Please try again.')).toBeInTheDocument();
    });
  });
});