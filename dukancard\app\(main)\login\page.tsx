import type { Metada<PERSON> } from "next";
import { LoginForm } from "./LoginForm";
import { Suspense } from "react";
import AuthPageBackground from "../components/auth/AuthPageBackground";

export async function generateMetadata(): Promise<Metadata> {
  const title = "Sign In";
  const description =
    "Sign in to your Dukancard account or create a new account with just your email address.";

  return {
    title, // Uses template: "Sign In - Dukancard"
    description,
    robots: "noindex, follow", // Prevent indexing
    // Keywords are generally not needed for noindex pages
  };
}

export default function LoginPage() {
  return (
    // Use semantic background and add top padding
    <div className="w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50 dark:bg-neutral-950 p-2 sm:p-4 pt-6 pb-20 md:pb-6 relative overflow-hidden">
      {/* Add animated background */}
      <AuthPageBackground />

      <Suspense
        fallback={
          <div className="flex flex-col justify-center items-center min-h-screen gap-2 relative z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[var(--brand-gold)]"></div>
            <p className="text-muted-foreground">Loading sign in form...</p>
          </div>
        }
      >
        <LoginForm />
      </Suspense>
    </div>
  );
}
