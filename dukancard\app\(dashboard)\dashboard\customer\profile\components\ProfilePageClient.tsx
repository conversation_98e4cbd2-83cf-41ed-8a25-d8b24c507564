"use client";

import { useState, useRef, useTransition } from "react";
import { User, Save, Loader2, MapPin } from "lucide-react";
import { motion } from "framer-motion";
import { ProfileForm, type ProfileFormRef } from "../ProfileForm";
import AddressForm, { type AddressFormRef } from "./AddressForm";
import AvatarUpload from "./AvatarUpload";
import ProfileRequirementDialog from "./ProfileRequirementDialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { toast } from "sonner";
import { updateCustomerProfileAndAddress, type UnifiedProfileFormState } from "../actions";

interface ProfilePageClientProps {
  initialName: string | null;
  initialAvatarUrl?: string | null;
  initialAddressData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  } | null;
  hasCompleteAddress?: boolean;
}

export default function ProfilePageClient({
  initialName,
  initialAvatarUrl,
  initialAddressData,
  hasCompleteAddress = false,
}: ProfilePageClientProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(initialAvatarUrl || undefined);
  const profileFormRef = useRef<ProfileFormRef>(null);
  const addressFormRef = useRef<AddressFormRef>(null);
  const [isPending, startTransition] = useTransition();
  console.log('ProfilePageClient - current isPending state:', isPending);
  const [formState, setFormState] = useState<UnifiedProfileFormState>({
    message: null,
    errors: {},
    success: false
  });

  const handleUnifiedSubmit = async () => {
    // Get data from both forms
    const profileData = profileFormRef.current?.getFormData();
    const addressData = addressFormRef.current?.getFormData();

    // Validate profile form (required)
    const isProfileValid = profileFormRef.current?.validateForm() ?? false;
    if (!isProfileValid) {
      toast.error('Please check your profile information. Name is required.');
      return;
    }

    // Validate address form (optional)
    const isAddressValid = addressFormRef.current?.validateForm() ?? true; // Address is optional

    // Double-check if name is provided (required) - this should now work correctly
    if (!profileData?.name?.trim()) {
      toast.error('Name is required');
      return;
    }

    // If address data is partially filled, validate that required fields are present
    const hasAnyAddressField = addressData && (
      addressData.pincode || addressData.city || addressData.state || addressData.locality
    );

    if (hasAnyAddressField && !isAddressValid) {
      toast.error('Please complete all required address fields or leave them empty');
      return;
    }

    // Create FormData for submission
    const formData = new FormData();
    formData.append('name', profileData.name);

    // Add address data if provided
    if (addressData) {
      formData.append('address', addressData.address || '');
      formData.append('pincode', addressData.pincode || '');
      formData.append('city', addressData.city || '');
      formData.append('state', addressData.state || '');
      formData.append('locality', addressData.locality || '');
    }

    // Submit the unified form
    startTransition(async () => {
      try {
        const initialState: UnifiedProfileFormState = {
          message: null,
          errors: {},
          success: false
        };

        const result = await updateCustomerProfileAndAddress(initialState, formData);
        setFormState(result);
        console.log('ProfilePageClient - formState after server action:', result);

        if (result.success) {
          toast.success(result.message || 'Profile updated successfully!');
        } else {
          toast.error(result.message || 'Failed to update profile');
        }
      } catch (error) {
        console.error('Error submitting unified form:', error);
        const errorState: UnifiedProfileFormState = {
          message: 'An unexpected error occurred. Please try again.',
          success: false,
          errors: {}
        };
        setFormState(errorState);
        toast.error('An unexpected error occurred. Please try again.');
      }
    });
  };

  // Animation variants for modern SaaS feel
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <>
      {/* Profile Requirement Dialog */}
      <ProfileRequirementDialog
        hasCompleteAddress={hasCompleteAddress}
      />

      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="space-y-8"
      >
        {/* Modern SaaS Header Section */}
        <motion.div variants={itemVariants} className="space-y-6">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60">
            <div className="space-y-1">
              <div className="flex items-center gap-3 mb-2">
                <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
                  <User className="w-5 h-5 text-primary" />
                </div>
                <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
                <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
                  Customer Profile
                </div>
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-600 dark:from-neutral-100 dark:to-neutral-400 bg-clip-text text-transparent">
                Profile Management
              </h1>
              <p className="text-neutral-600 dark:text-neutral-400 text-lg">
                Manage your personal information and preferences
              </p>
            </div>
          </div>
        </motion.div>

        {/* Full Width Two-Column Layout - Matching Business Pages */}
        <div className="grid gap-8 grid-cols-1 lg:grid-cols-2">
          {/* Left Column - Avatar and Personal Information */}
          <motion.div variants={itemVariants} className="space-y-8">
            {/* Avatar and Profile Overview Card */}
            <Card className="bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8">
                <div className="text-center space-y-6">
                  {/* Enhanced Avatar Section */}
                  <div className="relative">
                    <AvatarUpload
                      initialAvatarUrl={avatarUrl}
                      userName={initialName}
                      onUpdateAvatar={(url) => setAvatarUrl(url)}
                    />
                  </div>

                  {/* User Info */}
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-foreground">
                      {initialName || "Customer"}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Dukancard Customer
                    </p>
                  </div>

                  {/* Profile Completion Stats */}
                  <div className="grid grid-cols-2 gap-4 pt-4 border-t border-neutral-200/60 dark:border-neutral-800/60">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {hasCompleteAddress ? "✓" : "○"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Address
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {avatarUrl ? "✓" : "○"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Avatar
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Personal Information Form Card */}
            <Card className="bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="border-b border-neutral-200/60 dark:border-neutral-800/60">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/10 to-blue-500/5 border border-blue-500/20">
                    <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-foreground">
                      Personal Information
                    </h2>
                    <p className="text-sm text-muted-foreground">
                      Update your name and personal details
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-8">
                <ProfileForm
                  ref={profileFormRef}
                  initialName={initialName}
                  hideSubmitButton={true}
                />
              </CardContent>
            </Card>
          </motion.div>

          {/* Right Column - Address Information */}
          <motion.div variants={itemVariants} className="space-y-8">

            {/* Address Information Card - Full Height */}
            <Card className="bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300 h-fit">
              <CardHeader className="border-b border-neutral-200/60 dark:border-neutral-800/60">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/10 to-emerald-500/5 border border-emerald-500/20">
                    <MapPin className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-foreground">
                      Address Information
                    </h2>
                    <p className="text-sm text-muted-foreground">
                      Update your address details (optional)
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-8">
                <AddressForm
                  ref={addressFormRef}
                  initialData={initialAddressData || undefined}
                  hideSubmitButton={true}
                />
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Full Width Action Section - Matching Business Pages */}
        <motion.div variants={itemVariants} className="space-y-6">
          {/* Save Button Section - Full Width */}
          <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20 shadow-lg">
            <CardContent className="p-8">
              <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
                <div className="text-center lg:text-left">
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Ready to save your changes?
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Make sure all information is correct before saving
                  </p>
                </div>
                <Button
                  onClick={handleUnifiedSubmit}
                  disabled={isPending}
                  size="lg"
                  className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-200 min-w-[160px]"
                >
                  {isPending ? (
                    <>
                      <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-5 h-5 mr-2" />
                      Save Profile
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {formState.message && !formState.success && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg"
            >
              <p className="text-sm text-destructive">{formState.message}</p>
            </motion.div>
          )}
        </motion.div>
      </motion.div>
    </>
  );
}
