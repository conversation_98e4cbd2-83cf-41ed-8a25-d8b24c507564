"use strict";exports.id=3243,exports.ids=[3243,5453],exports.modules={301:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{j:()=>l});var n=s(32032),a=s(65193),i=s(28485),o=s(94230),c=e([i]);async function l(e,t,s){let r=null;try{let t=e.payload.subscription;if(!t||!t.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription completed: ${c}`);let l=(0,a.extractWebhookTimestamp)(e);r={subscriptionId:c,eventType:"subscription.completed",eventId:s||`completed_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let u=await i.webhookProcessor.processWebhookEvent(r);if(!u.shouldProcess)return{success:u.success,message:u.message};let d=await (0,n.createClient)(),{data:p,error:_}=await d.from("payment_subscriptions").select("subscription_status, plan_id, business_profile_id").eq("razorpay_subscription_id",c).maybeSingle();if(_)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${c}:`,_),{success:!1,message:`Error fetching subscription: ${_.message}`};if(!p)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${c}, skipping completion processing`),{success:!0,message:"No subscription found to complete"};console.log(`[RAZORPAY_WEBHOOK] Completing subscription ${c}, downgrading to free plan`);let b=new Date().toISOString(),g=await (0,o.M)({subscription_id:c,business_profile_id:p.business_profile_id,subscription_status:"active",has_active_subscription:!1,additional_data:{plan_id:"free",plan_cycle:"monthly",subscription_start_date:b,cancelled_at:b,razorpay_subscription_id:null,razorpay_customer_id:null,razorpay_plan_id:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,updated_at:b}});if(g.success)return await i.webhookProcessor.markEventAsSuccess(r.eventId,"Subscription completed and downgraded to free plan"),console.log(`[RAZORPAY_WEBHOOK] Successfully completed subscription ${c} and downgraded to free plan`),{success:!0,message:"Subscription completed and downgraded to free plan"};return await i.webhookProcessor.markEventAsFailed(r.eventId,g.message),console.error(`[RAZORPAY_WEBHOOK] Failed to complete subscription ${c}:`,g.message),g}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription completed:",e),{success:!1,message:`Error handling subscription completed: ${e instanceof Error?e.message:String(e)}`}}}i=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},5432:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{s:()=>l});var n=s(32032),a=s(65193),i=s(28485),o=s(94230),c=e([i]);async function l(e,t,s){let r=null;try{let t=e.payload.subscription;if(!t||!t.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription halted: ${c}`);let l=(0,a.extractWebhookTimestamp)(e);r={subscriptionId:c,eventType:"subscription.halted",eventId:s||`halted_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let u=await i.webhookProcessor.processWebhookEvent(r);if(!u.shouldProcess)return{success:u.success,message:u.message};let d=await (0,n.createClient)(),{data:p,error:_}=await d.from("payment_subscriptions").select("subscription_status, plan_id, plan_cycle, business_profile_id").eq("razorpay_subscription_id",c).maybeSingle();if(_)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${c}:`,_),{success:!1,message:`Error fetching subscription: ${_.message}`};if(!p)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${c}, skipping halted processing`),{success:!0,message:"No subscription found to halt"};if("free"===p.plan_id||(0,a.isTerminalStatus)(p.subscription_status))return console.log(`[RAZORPAY_WEBHOOK] Subscription ${c} is in terminal state (plan_id: ${p.plan_id}, status: ${p.subscription_status}), skipping halted update`),{success:!0,message:"Subscription is in terminal state, skipping halted update"};console.log(`[RAZORPAY_WEBHOOK] Halting subscription ${c}, preserving original plan ${p.plan_id}/${p.plan_cycle}`);let b=new Date().toISOString(),g=await (0,o.M)({subscription_id:c,business_profile_id:p.business_profile_id,subscription_status:"halted",has_active_subscription:!1,additional_data:{plan_id:"free",plan_cycle:"monthly",original_plan_id:p.plan_id,original_plan_cycle:p.plan_cycle,subscription_paused_at:b,updated_at:b}});if(g.success)return await i.webhookProcessor.markEventAsSuccess(r.eventId,"Subscription halted and temporarily downgraded to free plan"),console.log(`[RAZORPAY_WEBHOOK] Successfully halted subscription ${c} with original plan preserved`),{success:!0,message:"Subscription halted and temporarily downgraded to free plan"};return await i.webhookProcessor.markEventAsFailed(r.eventId,g.message),console.error(`[RAZORPAY_WEBHOOK] Failed to halt subscription ${c}:`,g.message),g}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription halted:",e),{success:!1,message:`Error handling subscription halted: ${e instanceof Error?e.message:String(e)}`}}}i=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},7885:(e,t,s)=>{s.d(t,{Z:()=>o});var r=s(65193);class n{validateWebhookSequence(e,t){let s=this.getValidStateTransitions(),r=this.isValidStateTransition(t.subscription_status,e.eventType,t.plan_id,s);return r.valid?{should:!0,reason:`STATE APPROVED: Valid transition from ${t.subscription_status} via ${e.eventType} - ${r.businessRule}`}:(console.warn(`[STATE_SEQUENCE] REJECTED: ${r.reason}`),{should:!1,reason:`STATE REJECTION: ${r.reason} - ${r.businessRule}`})}getValidStateTransitions(){return{trial:{allowedEvents:["subscription.authenticated","subscription.activated","subscription.cancelled","subscription.expired","subscription.updated"],description:"Trial users can authenticate, activate, cancel, or expire"},authenticated:{allowedEvents:["subscription.activated","subscription.cancelled","subscription.halted","subscription.charged","subscription.expired","subscription.updated"],description:"Authenticated subscriptions can activate, cancel, halt, charge, or expire"},active:{allowedEvents:["subscription.charged","subscription.cancelled","subscription.halted","subscription.expired","subscription.completed","subscription.updated"],description:"Active subscriptions can be charged, cancelled, halted, expired, or completed"},pending:{allowedEvents:["subscription.authenticated","subscription.activated","subscription.cancelled","subscription.expired","subscription.updated"],description:"Pending subscriptions can authenticate, activate, cancel, or expire"},halted:{allowedEvents:["subscription.cancelled","subscription.expired","subscription.activated","subscription.updated"],description:"Halted subscriptions can be cancelled, expired, or reactivated"},cancelled:{allowedEvents:[],description:"TERMINAL: Cancelled subscriptions cannot transition to any other state"},expired:{allowedEvents:[],description:"TERMINAL: Expired subscriptions cannot transition to any other state"},completed:{allowedEvents:[],description:"TERMINAL: Completed subscriptions cannot transition to any other state"}}}isValidStateTransition(e,t,s,r){if("trial"===e,"trial"===e&&"free"===s){let s=r[e];if(s&&s.allowedEvents.includes(t))return{valid:!0,reason:"Valid transition for fresh trial user",businessRule:s.description}}let n=r[e];return n?0===n.allowedEvents.length?{valid:!1,reason:`Terminal state reached: ${e}`,businessRule:n.description}:n.allowedEvents.includes(t)?{valid:!0,reason:`Valid transition from ${e} via ${t}`,businessRule:n.description}:{valid:!1,reason:`Invalid transition from ${e} via ${t}`,businessRule:`${n.description}. Allowed events: ${n.allowedEvents.join(", ")}`}:{valid:!1,reason:`Unknown current state: ${e}`,businessRule:"State not defined in transition matrix"}}}let a=new n;class i{async validateWebhookEventOrder(e,t){try{if(!t.last_webhook_timestamp)return{shouldProcess:!0,reason:"First webhook for subscription"};let s=new Date(t.last_webhook_timestamp).getTime()/1e3,r=e.webhookTimestamp,n=r-s;if("subscription.authenticated"===e.eventType&&"trial"===t.subscription_status&&"free"!==t.plan_id&&t.cancelled_at)return console.warn(`[WEBHOOK_ORDER] Rejecting authenticated webhook for cancelled subscription ${e.subscriptionId} - likely came after cancellation`),{shouldProcess:!1,reason:`Authenticated webhook rejected: subscription is in trial state (likely cancelled), plan: ${t.plan_id}`};if(n<-30)return{shouldProcess:!1,reason:`Webhook timestamp ${r} is ${Math.abs(n)} seconds older than last processed webhook ${s}`};return{shouldProcess:!0,reason:n>=0?`Webhook is newer than last processed (${n}s difference)`:`Webhook is within tolerance (${n}s difference, tolerance: 30s)`}}catch(e){return console.error("[WEBHOOK_ORDER] Error validating webhook order:",e),{shouldProcess:!0,reason:"Error during validation, allowing processing"}}}async shouldProcessEvent(e,t){let s=a.validateWebhookSequence(e,t);if(!s.should)return s;let n=r.SubscriptionStateManager.isTerminalStatus(t.subscription_status)||"free"===t.plan_id,i="halted"===t.subscription_status,o="subscription.activated"===e.eventType;if(n&&!["subscription.cancelled","subscription.expired","subscription.completed","subscription.halted"].includes(e.eventType)&&(!i||!o))return{should:!1,reason:`Subscription is in terminal state (${t.subscription_status}, plan: ${t.plan_id}), ignoring ${e.eventType} event`};return("subscription.cancelled"===e.eventType&&t.subscription_status===r.SUBSCRIPTION_STATUS.CANCELLED,t.subscription_status===r.SUBSCRIPTION_STATUS.TRIAL&&("subscription.authenticated"===e.eventType||e.eventType),"free"===t.plan_id&&("subscription.authenticated"===e.eventType||e.eventType),"subscription.activated"===e.eventType&&t.subscription_status===r.SUBSCRIPTION_STATUS.ACTIVE)?{should:!1,reason:"Subscription is already active"}:{should:!0,reason:"Event should be processed"}}}let o=new i},8233:(e,t,s)=>{s.d(t,{KH:()=>n,jr:()=>a});var r=s(32032);async function n(e,t,s,n){try{let t=await (0,r.createClient)(),{data:s,error:n}=await t.from("processed_webhook_events").select("event_id, event_type, status, processed_at").eq("event_id",e).maybeSingle();if(n)return console.error("[RAZORPAY_WEBHOOK_IDEMPOTENCY] Error checking processed events:",n),!1;if(s)return console.log(`[RAZORPAY_WEBHOOK_IDEMPOTENCY] Event ${e} already processed at ${s.processed_at} with status ${s.status}`),!0;return console.log(`[RAZORPAY_WEBHOOK_IDEMPOTENCY] Event ${e} not found in processed events - allowing processing`),!1}catch(e){return console.error("[RAZORPAY_WEBHOOK_IDEMPOTENCY] Exception checking processed events:",e),!1}}async function a(e,t,s,n="processed",i,o,c){try{let a=await (0,r.createClient)(),c=null,l=o||null;t.startsWith("subscription.")?(c="subscription",l=l||s.payload.subscription?.id||null):t.startsWith("payment.")?(c="payment",l=l||s.payload.payment?.entity?.id||null):t.startsWith("refund.")?(c="refund",l=l||s.payload.refund?.entity?.id||null):t.startsWith("invoice.")&&(c="invoice",l=l||s.payload.payment?.entity?.id||null);let{error:u}=await a.from("processed_webhook_events").upsert({event_id:e,event_type:t,entity_type:c,entity_id:l,payload:{...s,_metadata:{entity_type:c,entity_id:l,processed_timestamp:new Date().toISOString()}},status:n,error_message:i,processed_at:new Date().toISOString(),created_at:new Date().toISOString()},{onConflict:"event_id"});if(u)return console.error("[RAZORPAY_WEBHOOK_IDEMPOTENCY] Error marking event as processed:",u),!1;return console.log(`[RAZORPAY_WEBHOOK_IDEMPOTENCY] Successfully marked event ${e} as ${n}`),!0}catch(e){return console.error("[RAZORPAY_WEBHOOK_IDEMPOTENCY] Exception marking event as processed:",e),!1}}},10567:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{h:()=>l});var n=s(11337),a=s(32032),i=s(65193),o=s(28485),c=e([o]);async function l(e,t,r){let c=null;try{let t=e.payload.subscription;if(!t||!t.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let l=t.entity,u=l.id;console.log(`[RAZORPAY_WEBHOOK] Subscription authenticated: ${u}`);let d=(0,i.extractWebhookTimestamp)(e);c={subscriptionId:u,eventType:"subscription.authenticated",eventId:r||`auth_${u}_${Date.now()}`,payload:e,webhookTimestamp:d};let p=await o.webhookProcessor.processWebhookEvent(c);if(!p.shouldProcess)return{success:p.success,message:p.message};let _=await (0,a.createClient)(),{data:b,error:g}=await _.from("payment_subscriptions").select("cancelled_at, plan_id, subscription_status, last_webhook_timestamp").eq("razorpay_subscription_id",u).maybeSingle();if(g)console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${u}:`,g);else if(b){if(b.subscription_status===i.SUBSCRIPTION_STATUS.TRIAL&&"free"!==b.plan_id&&b.cancelled_at){if(console.warn(`[CANCELLATION_CHECK] Subscription ${u} was already cancelled:`),console.warn(`  Current status: ${b.subscription_status} (trial indicates cancellation)`),console.warn(`  Current plan: ${b.plan_id} (preserved from cancellation)`),console.warn(`  Authenticated webhook timestamp: ${d} (${new Date(1e3*d).toISOString()})`),!b.last_webhook_timestamp)return console.warn("  ENTERPRISE DECISION: Rejecting authenticated webhook - subscription is in cancelled state (no previous webhook)"),{success:!0,message:"ENTERPRISE PROTECTION: Authenticated webhook rejected - subscription is in cancelled trial state"};{let e=new Date(b.last_webhook_timestamp).getTime()/1e3,t=d-e;return console.warn(`  Last webhook timestamp: ${e} (${new Date(1e3*e).toISOString()})`),console.warn(`  Time difference: ${t} seconds`),console.warn("  ENTERPRISE DECISION: Rejecting authenticated webhook to preserve cancellation state"),{success:!0,message:`ENTERPRISE PROTECTION: Authenticated webhook rejected - subscription was already cancelled and is in trial state with plan ${b.plan_id}`}}}if("free"===b.plan_id||(0,i.isTerminalStatus)(b.subscription_status))return console.log(`[RAZORPAY_WEBHOOK] Subscription ${u} is in terminal state (plan_id: ${b.plan_id}, status: ${b.subscription_status}), skipping authentication`),{success:!0,message:"Subscription is in terminal state, skipping authentication"};b.subscription_status===i.SUBSCRIPTION_STATUS.TRIAL&&"free"===b.plan_id&&console.log(`[RAZORPAY_WEBHOOK] Fresh trial user ${u} authenticating subscription - this is allowed`)}let O=l.notes?.business_profile_id||l.notes?.user_id,m=l.notes?.old_subscription_id;if(m&&console.log(`[RAZORPAY_WEBHOOK] Plan switch detected. Old subscription ${m} will be cancelled when new subscription becomes active`),O){console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Checking for existing authenticated subscriptions for business ${O} before processing Plan B (${u})`);let{data:e,error:t}=await _.from("payment_subscriptions").select("razorpay_subscription_id, subscription_status").eq("business_profile_id",O).eq("subscription_status",n.SO._AUTHENTICATED).neq("razorpay_subscription_id",u);if(t)console.error(`[RAZORPAY_WEBHOOK_SCENARIO_3] Error finding existing authenticated subscriptions for business ${O}:`,t),console.log("[RAZORPAY_WEBHOOK_SCENARIO_3] Continuing despite find error");else if(e&&e.length>0)for(let t of(console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Found ${e.length} existing authenticated subscription(s) for business ${O}.`),e)){console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Attempting to cancel previously authenticated Plan A: ${t.razorpay_subscription_id}`);let{cancelSubscription:e}=await s.e(1546).then(s.bind(s,31546)),r=await e(t.razorpay_subscription_id,!1);if(r.success){console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Successfully triggered Razorpay cancellation for Plan A (${t.razorpay_subscription_id}).`);let e=new Date().toISOString(),{error:s}=await _.from("payment_subscriptions").update({subscription_status:i.SUBSCRIPTION_STATUS.CANCELLED,cancellation_requested_at:e,cancelled_at:e,updated_at:e}).eq("razorpay_subscription_id",t.razorpay_subscription_id);s?(console.error(`[RAZORPAY_WEBHOOK_SCENARIO_3] Error updating DB record for cancelled Plan A (${t.razorpay_subscription_id}):`,s),console.log("[RAZORPAY_WEBHOOK_SCENARIO_3] Continuing despite DB update error for Plan A.")):console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Successfully updated DB record for cancelled Plan A (${t.razorpay_subscription_id}).`)}else console.error(`[RAZORPAY_WEBHOOK_SCENARIO_3] Error cancelling authenticated Plan A (${t.razorpay_subscription_id}) via Razorpay:`,r.error),console.log("[RAZORPAY_WEBHOOK_SCENARIO_3] Continuing despite Razorpay cancellation error for Plan A.")}else console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] No other existing authenticated subscriptions found for business ${O}.`)}let y=l.start_at?new Date(1e3*l.start_at).toISOString():l.current_start?new Date(1e3*l.current_start).toISOString():null;console.log(`[SUBSCRIPTION_AUTHENTICATED] Using start date: ${y}, start_at: ${l.start_at}, current_start: ${l.current_start}`);let h=null;try{let e=l.notes?.last_payment_id;if(e){let{getPayment:t}=await s.e(4976).then(s.bind(s,94976)),r=await t(e);r.success&&r.data?h=r.data.method:console.error(`[RAZORPAY_WEBHOOK] Error fetching payment ${e}:`,r.error)}}catch(e){console.error("[RAZORPAY_WEBHOOK] Error fetching payment method:",e)}let E=null,{data:A,error:f}=await _.from("payment_subscriptions").select("subscription_status, subscription_expiry_time").eq("razorpay_subscription_id",u).maybeSingle();f?console.error(`[RAZORPAY_WEBHOOK] Error fetching current subscription state for ${u} during trial check:`,f):A&&A.subscription_status===i.SUBSCRIPTION_STATUS.TRIAL&&(E=A.subscription_expiry_time,console.log(`[RAZORPAY_WEBHOOK] Subscription ${u} is currently in trial. Preserving trial expiry: ${E}`));let R={subscription_start_date:y,subscription_expiry_time:E??(l.current_end?new Date(1e3*l.current_end).toISOString():null),subscription_charge_time:l.charge_at?new Date(1e3*l.charge_at).toISOString():null,razorpay_customer_id:l.customer_id||null,cancellation_requested_at:null};h&&(R.last_payment_method=h),console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Updating Plan B subscription ${u} with data:`,R);let w=await o.webhookProcessor.updateSubscriptionStatus(u,i.SUBSCRIPTION_STATUS.AUTHENTICATED,R,d);return console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Result of updating Plan B (${u}) in DB:`,w),w.success?await o.webhookProcessor.markEventAsSuccess(c.eventId,w.message):await o.webhookProcessor.markEventAsFailed(c.eventId,w.message),w}catch(t){console.error("[RAZORPAY_WEBHOOK] Error handling subscription authenticated:",t);let e=`Error handling subscription authenticated: ${t instanceof Error?t.message:String(t)}`;return c&&await o.webhookProcessor.markEventAsFailed(c.eventId,e),{success:!1,message:e}}}o=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},14055:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{V:()=>l});var n=s(11337),a=s(65193),i=s(32032),o=s(28485),c=e([o]);async function l(e,t,s){let r=null;try{let t=e.payload.subscription;if(!t||!t.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription pending: ${c}`);let l=(0,a.extractWebhookTimestamp)(e);r={subscriptionId:c,eventType:"subscription.pending",eventId:s||`pending_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let u=await o.webhookProcessor.processWebhookEvent(r);if(!u.shouldProcess)return{success:u.success,message:u.message};let d=await (0,i.createClient)(),{data:p,error:_}=await d.from("payment_subscriptions").select("cancelled_at, plan_id, subscription_status").eq("razorpay_subscription_id",c).maybeSingle();if(_)console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${c}:`,_);else if(p){let e="free"===p.plan_id||(0,a.isTerminalStatus)(p.subscription_status),t="authenticated"===p.subscription_status&&p.cancelled_at;if(e&&!t)return console.log(`[RAZORPAY_WEBHOOK] Subscription ${c} is in terminal state (plan_id: ${p.plan_id}, status: ${p.subscription_status}), skipping pending update`),{success:!0,message:"Subscription is in terminal state, skipping pending update"}}return await (0,a.nV)(d,c,n.SO._PENDING,{})}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription pending:",e),{success:!1,message:`Error handling subscription pending: ${e instanceof Error?e.message:String(e)}`}}}o=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},24703:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{e_:()=>p,et:()=>d,rt:()=>u,u$:()=>l});var n=s(11337),a=s(65193),i=s(32032),o=s(28485),c=e([o]);async function l(e,t,r){let c=null;try{let t=e.payload.payment;if(!t)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let l=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Payment authorized: ${l}`);let u=t.entity.notes?.subscription_id;if(!u)return{success:!0,message:"Not a subscription payment, no update needed"};let d=(0,a.extractWebhookTimestamp)(e);c={subscriptionId:u,eventType:"payment.authorized",eventId:r||`payment_auth_${l}_${Date.now()}`,payload:e,webhookTimestamp:d};let p=await o.webhookProcessor.processWebhookEvent(c);if(!p.shouldProcess)return{success:p.success,message:p.message};let _=t.entity.notes?.old_subscription_id;_&&console.log(`[RAZORPAY_WEBHOOK] Plan switch payment detected: ${_} -> ${u}`);let{getSubscription:b}=await s.e(1546).then(s.bind(s,31546)),g=await b(u);g.success&&g.data||console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription details for ${u}:`,g.error);let O=g.data;try{let{updateSubscription:e}=await s.e(1546).then(s.bind(s,31546)),t=await e(u,{notes:{...O?.notes||{},last_payment_id:l}});t.success||console.error(`[RAZORPAY_WEBHOOK] Error updating subscription notes for ${u}:`,t.error)}catch(e){console.error("[RAZORPAY_WEBHOOK] Error updating subscription notes:",e)}let m=(0,i.createClient)(),y=await m;return await (0,a.nV)(y,u,n.SO._AUTHENTICATED,{last_payment_id:l,last_payment_date:new Date(1e3*t.entity.created_at).toISOString(),last_payment_method:t.entity.method,cancellation_requested_at:null,...O&&{razorpay_customer_id:O.customer_id||null,subscription_start_date:O.current_start?new Date(1e3*O.current_start).toISOString():null,subscription_expiry_time:O.current_end?new Date(1e3*O.current_end).toISOString():null,subscription_charge_time:O.charge_at?new Date(1e3*O.charge_at).toISOString():null}})}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling payment authorized:",e),{success:!1,message:`Error handling payment authorized: ${e instanceof Error?e.message:String(e)}`}}}async function u(e,t,r){try{let t=e.payload.payment;if(!t)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let r=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Payment captured: ${r}`);let o=t.entity.notes?.subscription_id;if(!o)return{success:!0,message:"Not a subscription payment, no update needed"};let{getSubscription:c}=await s.e(1546).then(s.bind(s,31546)),l=await c(o);l.success&&l.data||console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription details for ${o}:`,l.error);let u=l.data;try{let{updateSubscription:e}=await s.e(1546).then(s.bind(s,31546)),t=await e(o,{notes:{...u?.notes||{},last_payment_id:r}});t.success||console.error(`[RAZORPAY_WEBHOOK] Error updating subscription notes for ${o}:`,t.error)}catch(e){console.error("[RAZORPAY_WEBHOOK] Error updating subscription notes:",e)}let d=await (0,i.createClient)();return await (0,a.nV)(d,o,n.SO._ACTIVE,{last_payment_id:r,last_payment_date:new Date(1e3*t.entity.created_at).toISOString(),last_payment_method:t.entity.method,cancellation_requested_at:null,...u&&{razorpay_customer_id:u.customer_id||null,subscription_start_date:u.current_start?new Date(1e3*u.current_start).toISOString():null,subscription_expiry_time:u.current_end?new Date(1e3*u.current_end).toISOString():null,subscription_charge_time:u.charge_at?new Date(1e3*u.charge_at).toISOString():null}})}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling payment captured:",e),{success:!1,message:`Error handling payment captured: ${e instanceof Error?e.message:String(e)}`}}}async function d(e,t){try{let{webhookProcessor:r}=await Promise.resolve().then(s.bind(s,28485)),{extractWebhookTimestamp:n,SUBSCRIPTION_STATUS:a}=await Promise.resolve().then(s.bind(s,65193)),i=e.payload.payment;if(!i)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let o=i.entity.id;console.log(`[RAZORPAY_WEBHOOK] Payment failed: ${o}`);let c=i.entity.notes?.subscription_id;if(!c)return{success:!0,message:"Not a subscription payment, no update needed"};let l=n(e),u=await r.updateSubscriptionStatus(c,a.PAYMENT_FAILED,{last_payment_id:o,last_payment_date:new Date(1e3*i.entity.created_at).toISOString(),last_payment_method:i.entity.method},l);return u.success?await r.markEventAsSuccess(t.eventId,u.message):await r.markEventAsFailed(t.eventId,u.message),u}catch(r){console.error("[RAZORPAY_WEBHOOK] Error handling payment failed:",r);let e=`Error handling payment failed: ${r instanceof Error?r.message:String(r)}`;if(t){let{webhookProcessor:r}=await Promise.resolve().then(s.bind(s,28485));await r.markEventAsFailed(t.eventId,e)}return{success:!1,message:e}}}async function p(e,t){try{let{webhookProcessor:r}=await Promise.resolve().then(s.bind(s,28485)),{extractWebhookTimestamp:n,SUBSCRIPTION_STATUS:a}=await Promise.resolve().then(s.bind(s,65193)),i=e.payload.invoice,o=e.payload.payment;if(!i)return console.error("[RAZORPAY_WEBHOOK] Invoice data not found in payload"),{success:!1,message:"Invoice data not found in payload"};if(!o)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let c=i.entity.id,l=o.entity.id,u=i.entity.subscription_id;if(console.log(`[RAZORPAY_WEBHOOK] Invoice paid: ${c}, Payment: ${l}, Subscription: ${u}`),!u)return{success:!0,message:"Not a subscription invoice, no update needed"};let d=o.entity.method,p=n(e),_=await r.updateSubscriptionStatus(u,a.ACTIVE,{last_payment_id:l,last_payment_date:new Date(1e3*o.entity.created_at).toISOString(),last_payment_method:d,cancellation_requested_at:null},p);return _.success?await r.markEventAsSuccess(t.eventId,_.message):await r.markEventAsFailed(t.eventId,_.message),_}catch(r){console.error("[RAZORPAY_WEBHOOK] Error handling invoice paid:",r);let e=`Error handling invoice paid: ${r instanceof Error?r.message:String(r)}`;if(t){let{webhookProcessor:r}=await Promise.resolve().then(s.bind(s,28485));await r.markEventAsFailed(t.eventId,e)}return{success:!1,message:e}}}o=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},28485:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{webhookProcessor:()=>u});var n=s(32032),a=s(90258),i=s(29743),o=s(7885),c=e([a,i]);[a,i]=c.then?(await c)():c;class l{constructor(){this.adminClient=null}async getAdminClient(){return this.adminClient||(this.adminClient=await (0,n.createClient)()),this.adminClient}async processWebhookEvent(e){try{if(await i.K.checkEventIdempotency(e.eventId))return{success:!0,message:"Event already processed",shouldProcess:!1};let t=await a.D.getCurrentSubscriptionState(e.subscriptionId);if(!t){if(console.warn(`[WEBHOOK_PROCESSOR] Subscription ${e.subscriptionId} not found in database. This could be a race condition or the subscription was created outside our system.`),!this.shouldCreateMissingSubscription(e.eventType))return{success:!0,message:`Subscription not found for event ${e.eventType} - this may be expected`,shouldProcess:!1};{let s=await a.D.createMissingSubscriptionRecord(e);if(!s)return console.error(`[WEBHOOK_PROCESSOR] Failed to create missing subscription record for ${e.subscriptionId}`),{success:!1,message:"Subscription not found and could not be created",shouldProcess:!1};t=s}}if(t){let s=await o.Z.validateWebhookEventOrder(e,t);if(!s.shouldProcess)return console.warn(`[WEBHOOK_PROCESSOR] Rejecting out-of-order webhook: ${s.reason}`),{success:!0,message:`Webhook rejected: ${s.reason}`,shouldProcess:!1};let r=await o.Z.shouldProcessEvent(e,t);if(!r.should)return{success:!0,message:r.reason,shouldProcess:!1}}return await i.K.markEventAsProcessing(e.eventId,e.eventType),{success:!0,message:"Event validated and ready for processing",shouldProcess:!0}}catch(t){return console.error(`[WEBHOOK_PROCESSOR] Error processing webhook event ${e.eventId}:`,t),{success:!1,message:`Error processing webhook: ${t instanceof Error?t.message:String(t)}`,shouldProcess:!1}}}shouldCreateMissingSubscription(e){return["subscription.authenticated","subscription.activated","subscription.charged","subscription.halted","subscription.cancelled"].includes(e)}async markEventAsSuccess(e,t="Successfully processed"){await i.K.markEventAsSuccess(e,t)}async markEventAsFailed(e,t){await i.K.markEventAsFailed(e,t)}async updateSubscriptionStatus(e,t,s={},r){return await a.D.updateSubscriptionStatus(e,t,s,r)}}let u=new l;r()}catch(e){r(e)}})},28539:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{J:()=>c});var n=s(32032),a=s(65193),i=s(28485),o=e([i]);async function c(e,t,r){let o=null;try{let t=e.payload.subscription;if(!t||!t.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=t.entity,l=c.id;console.log(`[RAZORPAY_WEBHOOK] Subscription activated: ${l}`);let u=(0,a.extractWebhookTimestamp)(e);o={subscriptionId:l,eventType:"subscription.activated",eventId:r||`activated_${l}_${Date.now()}`,payload:e,webhookTimestamp:u};let d=await i.webhookProcessor.processWebhookEvent(o);if(!d.shouldProcess)return{success:d.success,message:d.message};let p=(0,n.createClient)(),{isTerminalStatus:_}=await Promise.resolve().then(s.bind(s,65193)),b=await p,{data:g,error:O}=await b.from("payment_subscriptions").select("cancelled_at, plan_id, subscription_status").eq("razorpay_subscription_id",l).maybeSingle();if(O)console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${l}:`,O);else if(g){if("halted"===g.subscription_status)console.log(`[RAZORPAY_WEBHOOK] Halted subscription ${l} can be reactivated - this is allowed`);else if("free"===g.plan_id&&"active"===g.subscription_status||_(g.subscription_status))return console.log(`[RAZORPAY_WEBHOOK] Subscription is in terminal state (${g.subscription_status}, plan: ${g.plan_id}), ignoring subscription.activated event`),{success:!0,message:`Subscription is in terminal state (${g.subscription_status}, plan: ${g.plan_id}), ignoring subscription.activated event`};g.subscription_status===a.SUBSCRIPTION_STATUS.TRIAL&&console.log(`[RAZORPAY_WEBHOOK] Trial user ${l} activating subscription - this is allowed even with cancelled_at timestamp`)}let{data:m,error:y}=await b.from("payment_subscriptions").select("original_plan_id, original_plan_cycle, subscription_paused_at, plan_cycle").eq("razorpay_subscription_id",l).maybeSingle();y&&console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription details for resume check ${l}:`,y);let h=!1,E=null,A=null;m&&m.subscription_paused_at&&m.original_plan_id&&(console.log(`[RAZORPAY_WEBHOOK] Subscription ${l} is being resumed. Original plan: ${m.original_plan_id}, Original cycle: ${m.original_plan_cycle}`),h=!0,E=m.original_plan_id,A=m.original_plan_cycle);let f=c.notes?.is_plan_switch==="true",R=c.notes?.old_subscription_id,w=c.notes?.cancelled_old_subscription==="true";if(console.log("[RAZORPAY_WEBHOOK] Plan switch detection:",{isPlanSwitch:f,oldSubscriptionId:R,oldSubscriptionAlreadyCancelled:w,isResumeFlow:h,subscriptionNotes:c.notes}),f&&!h)if(console.log(`[RAZORPAY_WEBHOOK] ✅ Processing plan switch from subscription ${R} to ${l}`),R&&!w){console.log(`[RAZORPAY_WEBHOOK] Cancelling old subscription ${R}`);let{cancelSubscription:e}=await s.e(1546).then(s.bind(s,31546)),t=await e(R,!1);if(!t.success)return console.error(`[RAZORPAY_WEBHOOK] CRITICAL: Failed to cancel old subscription ${R}:`,t.error),{success:!1,message:`Critical error: Failed to cancel old subscription ${R}. This could result in double billing.`};{console.log(`[RAZORPAY_WEBHOOK] Successfully cancelled old subscription ${R}`),console.log(`[RAZORPAY_WEBHOOK] Updating old subscription record ${R} with new subscription ID ${l}`);let{error:e}=await b.from("payment_subscriptions").update({razorpay_subscription_id:l,updated_at:new Date().toISOString()}).eq("razorpay_subscription_id",R);if(e)return console.error(`[RAZORPAY_WEBHOOK] Error updating old subscription record ${R} with new ID ${l}:`,e),{success:!1,message:"Failed to update old subscription record with new subscription ID"};console.log(`[RAZORPAY_WEBHOOK] Successfully updated old subscription record ${R} with new subscription ID ${l}`)}}else R&&w&&console.log(`[RAZORPAY_WEBHOOK] Old subscription ${R} was already cancelled, skipping cancellation`);let S="card";try{let e=c.notes?.last_payment_id;if(e){let{getPaymentDetails:t}=await s.e(4976).then(s.bind(s,94976)),r=await t(e);r.success&&r.data?S=r.data.method:console.error(`[RAZORPAY_WEBHOOK] Error fetching payment ${e}:`,r.error)}}catch(e){console.error("[RAZORPAY_WEBHOOK] Error fetching payment method:",e)}let v=c.notes?.plan_type,P=c.notes?.plan_cycle,$={subscription_start_date:new Date(1e3*c.current_start).toISOString(),subscription_expiry_time:c.current_end?new Date(1e3*c.current_end).toISOString():null,subscription_charge_time:c.charge_at?new Date(1e3*c.charge_at).toISOString():null,razorpay_customer_id:c.customer_id||null,last_payment_method:S,cancellation_requested_at:null,razorpay_plan_id:c.plan_id};h&&E?(console.log(`[RAZORPAY_WEBHOOK] Restoring original plan ${E} for subscription ${l}`),$.plan_id=E,A?$.plan_cycle=A:($.plan_cycle=P||"monthly",console.warn(`[RAZORPAY_WEBHOOK] Original plan cycle not available or null for resume of ${l}. Using fallback/notes: ${$.plan_cycle}`)),$.subscription_paused_at=null,$.original_plan_id=null,$.original_plan_cycle=null):($.plan_id=v,$.plan_cycle=P),console.log(`[RAZORPAY_WEBHOOK] Updating subscription ${l} with data:`,$),console.log(`[RAZORPAY_WEBHOOK] Updating subscription ${l} with status ACTIVE`);let I=await i.webhookProcessor.updateSubscriptionStatus(l,a.SUBSCRIPTION_STATUS.ACTIVE,$,u);return I.success?await i.webhookProcessor.markEventAsSuccess(o.eventId,I.message):await i.webhookProcessor.markEventAsFailed(o.eventId,I.message),I}catch(t){console.error("[RAZORPAY_WEBHOOK] Error handling subscription activated:",t);let e=`Error handling subscription activated: ${t instanceof Error?t.message:String(t)}`;return o&&await i.webhookProcessor.markEventAsFailed(o.eventId,e),{success:!1,message:e}}}i=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},29743:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{K:()=>a});var n=s(32032);class e{constructor(e){this.supabase=e}static async build(){let t=await (0,n.createClient)();return new e(t)}async checkEventIdempotency(e){let{data:t,error:s}=await this.supabase.from("processed_webhook_events").select("event_id").eq("event_id",e).in("status",["processed","success"]).maybeSingle();return s?(console.error("[EventManager] Error checking event idempotency:",s),!1):!!t}async markEventAsProcessing(e,t){let{error:s}=await this.supabase.from("processed_webhook_events").upsert({event_id:e,event_type:t,status:"processing",processed_at:new Date().toISOString()});s&&console.error("[EventManager] Error marking event as processing:",s)}async markEventAsSuccess(e,t="Successfully processed"){let{error:s}=await this.supabase.from("processed_webhook_events").update({status:"processed",processed_at:new Date().toISOString(),error_message:null,notes:t}).eq("event_id",e);s&&console.error("[EventManager] Error marking event as success:",s)}async markEventAsFailed(e,t){let{error:s}=await this.supabase.from("processed_webhook_events").update({status:"failed",processed_at:new Date().toISOString(),error_message:t}).eq("event_id",e);s&&console.error("[EventManager] Error marking event as failed:",s)}}let a=await e.build();r()}catch(e){r(e)}},1)},30260:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{Y:()=>c});var n=s(32032),a=s(65193),i=s(28485),o=e([i]);async function c(e,t,s){let r=null;try{let t=e.payload.payment,o=e.payload.subscription;if(!t||!t.entity)return console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload"),{success:!1,message:"Payment data not found in payload"};let c=t.entity.subscription_id;if(!c)return console.error("[RAZORPAY_WEBHOOK] Subscription ID not found in payment data"),{success:!1,message:"Subscription ID not found in payment data"};let l=null;o&&o.entity&&(l=o.entity),console.log(`[RAZORPAY_WEBHOOK] Subscription charged: ${c}`);let u=(0,a.extractWebhookTimestamp)(e);r={subscriptionId:c,eventType:"subscription.charged",eventId:s||`charged_${c}_${Date.now()}`,payload:e,webhookTimestamp:u};let d=await i.webhookProcessor.processWebhookEvent(r);if(!d.shouldProcess)return{success:d.success,message:d.message};let p=await (0,n.createClient)(),{data:_,error:b}=await p.from("payment_subscriptions").select("cancelled_at, plan_id, subscription_status").eq("razorpay_subscription_id",c).maybeSingle();if(b)console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${c}:`,b);else if(_){if("free"===_.plan_id||(0,a.isTerminalStatus)(_.subscription_status))return console.log(`[RAZORPAY_WEBHOOK] Subscription ${c} is in terminal state (plan_id: ${_.plan_id}, status: ${_.subscription_status}), skipping charge processing`),{success:!0,message:"Subscription is in terminal state, skipping charge processing"};_.subscription_status===a.SUBSCRIPTION_STATUS.TRIAL&&console.log(`[RAZORPAY_WEBHOOK] Trial user ${c} processing charge - this is allowed even with cancelled_at timestamp`)}let g=l?.notes?.plan_type||t.entity.notes?.plan_type,O=l?.notes?.plan_cycle||t.entity.notes?.plan_cycle,m=await i.webhookProcessor.updateSubscriptionStatus(c,a.SUBSCRIPTION_STATUS.ACTIVE,{last_payment_id:t.entity.id,last_payment_date:new Date(1e3*t.entity.created_at).toISOString(),last_payment_method:t.entity.method,subscription_start_date:l?.current_start?new Date(1e3*l.current_start).toISOString():null,subscription_expiry_time:l?.current_end?new Date(1e3*l.current_end).toISOString():null,subscription_charge_time:l?.charge_at?new Date(1e3*l.charge_at).toISOString():null,razorpay_customer_id:l?.customer_id||null,cancellation_requested_at:null,plan_id:g,plan_cycle:O,razorpay_plan_id:l?.plan_id},u);return m.success?await i.webhookProcessor.markEventAsSuccess(r.eventId,m.message):await i.webhookProcessor.markEventAsFailed(r.eventId,m.message),m}catch(t){console.error("[RAZORPAY_WEBHOOK] Error handling subscription charged:",t);let e=`Error handling subscription charged: ${t instanceof Error?t.message:String(t)}`;return r&&await i.webhookProcessor.markEventAsFailed(r.eventId,e),{success:!1,message:e}}}i=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},33243:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{S:()=>n.S});var n=s(33419),a=e([n]);n=(a.then?(await a)():a)[0],r()}catch(e){r(e)}})},33419:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{S:()=>p});var n=s(32032),a=s(95453),i=s(11337),o=s(42651),c=s(8233),l=s(67332),u=s(92021),d=e([l,u]);async function p(e,t,r,d,p){try{let _,b,g=e.payload.subscription?.id||e.payload.payment?.entity?.notes?.subscription_id||void 0;if(e.event.startsWith("subscription.")){let{extractEntityId:t}=await s.e(5357).then(s.bind(s,55357));_=t(e)||void 0}else e.event.startsWith("payment.")?_=e.payload.payment?.entity?.id:e.event.startsWith("refund.")?_=e.payload.refund?.entity?.id:e.event.startsWith("invoice.")&&(_=e.payload.payment?.entity?.id);if(!r&&t&&""!==t.trim()){let s=process.env.RAZORPAY_WEBHOOK_SECRET;if(!s){console.error("[RAZORPAY_WEBHOOK] Webhook secret not configured");let t=await (0,o.m0)(e.event,_,g,"Webhook secret not configured",e);return{success:!1,message:"Webhook secret not configured",error_id:t.error_id}}let r=d||JSON.stringify(e);if(!(0,a.t6)(r,t,s)){console.error("[RAZORPAY_WEBHOOK] Invalid webhook signature");let t=await (0,o.m0)(e.event,_,g,"Invalid webhook signature",e);return{success:!1,message:"Invalid webhook signature",error_id:t.error_id}}}else r||""!==t||console.log("[RAZORPAY_WEBHOOK] Skipping signature verification for testing");let O=e.event,m=await (0,n.createClient)();if(!r&&p&&await (0,c.KH)(p,O,e,m))return{success:!0,message:"Event already processed (idempotent)"};try{switch(O){case i.mz._SUBSCRIPTION_AUTHENTICATED:b=await (0,u.h)(e,m,p);break;case i.mz._SUBSCRIPTION_ACTIVATED:b=await (0,u.J0)(e,m,p);break;case i.mz._SUBSCRIPTION_CHARGED:b=await (0,u.Y4)(e,m,p);break;case i.mz._SUBSCRIPTION_PENDING:b=await (0,u.VA)(e,m,p);break;case i.mz._SUBSCRIPTION_HALTED:b=await (0,u.s1)(e,m,p);break;case i.mz._SUBSCRIPTION_CANCELLED:b=await (0,u.X7)(e,m,p);break;case i.mz._SUBSCRIPTION_COMPLETED:b=await (0,u.je)(e,m,p);break;case i.mz._SUBSCRIPTION_EXPIRED:b=await (0,u.$O)(e,m,p);break;case i.mz._SUBSCRIPTION_UPDATED:b=await (0,u.Tb)(e,m,p);break;case i.mz._PAYMENT_AUTHORIZED:b=await (0,l.u$)(e,m,p);break;case i.mz._PAYMENT_CAPTURED:b=await (0,l.rt)(e,m,p);break;case i.mz._PAYMENT_FAILED:b=await (0,l.et)(e,{eventId:p||`payment_failed_${Date.now()}`});break;case i.mz._INVOICE_PAID:b=await (0,l.e_)(e,{eventId:p||`invoice_paid_${Date.now()}`});break;case i.mz._REFUND_CREATED:b=await (0,l.j_)(e,m,p);break;case i.mz._REFUND_PROCESSED:b=await (0,l.F9)(e,m,p);break;case i.mz._REFUND_FAILED:b=await (0,l.eA)(e,m,p);break;default:b={success:!0,message:"Event acknowledged but not processed"}}return r&&b.success&&await (0,o.z5)(r,"resolved"),!r&&b.success&&p&&await (0,c.jr)(p,O,e,"processed",void 0,_),b}catch(n){if(console.error(`[RAZORPAY_WEBHOOK] Error processing ${O} webhook:`,n),r){let e=n instanceof Error?n.message:String(n);return await (0,o.z5)(r,"retrying",void 0,e),{success:!1,message:`Error processing webhook: ${e}`,error_id:r}}let t=n instanceof Error?n.message:String(n),s=await (0,o.m0)(O,_,g,t,e,m);return{success:!1,message:`Error processing webhook: ${t}`,error_id:s.error_id}}}catch(r){console.error("[RAZORPAY_WEBHOOK] Unhandled error in webhook handler:",r);let t=r instanceof Error?r.message:String(r),s=await (0,o.m0)(e.event||"unknown",void 0,void 0,t,e);return{success:!1,message:`Unhandled error in webhook handler: ${t}`,error_id:s.error_id}}}[l,u]=d.then?(await d)():d,r()}catch(e){r(e)}})},37931:(e,t,s)=>{s.a(e,async(e,t)=>{try{var r=s(92021),n=e([r]);r=(n.then?(await n)():n)[0],t()}catch(e){t(e)}})},42651:(e,t,s)=>{s.d(t,{gz:()=>i,m0:()=>n,z5:()=>a});var r=s(32032);async function n(e,t,s,n,a,i){try{let o=i||await (0,r.createClient)(),c={event_type:e,event_id:t,subscription_id:s,error_message:n,payload:a,retry_count:0,status:"pending",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:l,error:u}=await o.from("webhook_error_logs").insert(c).select("id").single();if(u)return console.error("[RAZORPAY_WEBHOOK] Error logging webhook error:",u),{success:!1};return{success:!0,error_id:l?.id}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Exception logging webhook error:",e),{success:!1}}}async function a(e,t,s,n,a){try{let i=a||await (0,r.createClient)(),o={status:t,updated_at:new Date().toISOString()};void 0!==s&&(o.retry_count=s),n&&(o.error_message=n);let{error:c}=await i.from("webhook_error_logs").update(o).eq("id",e);if(c)return console.error("[RAZORPAY_WEBHOOK] Error updating webhook error log:",c),{success:!1};return{success:!0}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Exception updating webhook error log:",e),{success:!1}}}async function i(e=3,t){try{let s=t||await (0,r.createClient)(),{data:n,error:a}=await s.from("webhook_error_logs").select("*").in("status",["pending","retrying"]).lt("retry_count",e).order("created_at",{ascending:!0});if(a)return console.error("[RAZORPAY_WEBHOOK] Error getting pending webhook errors:",a),[];return(n||[]).map(e=>({...e,event_id:e.event_id||void 0}))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Exception getting pending webhook errors:",e),[]}}},47249:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{$:()=>l});var n=s(32032),a=s(65193),i=s(28485),o=s(94230),c=e([i]);async function l(e,t,s){let r=null;try{let t=e.payload.subscription;if(!t||!t.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let c=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription expired: ${c}`);let l=(0,a.extractWebhookTimestamp)(e);r={subscriptionId:c,eventType:"subscription.expired",eventId:s||`expired_${c}_${Date.now()}`,payload:e,webhookTimestamp:l};let u=await i.webhookProcessor.processWebhookEvent(r);if(!u.shouldProcess)return{success:u.success,message:u.message};let d=await (0,n.createClient)(),{data:p,error:_}=await d.from("payment_subscriptions").select("subscription_status, plan_id, business_profile_id").eq("razorpay_subscription_id",c).maybeSingle();if(_)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${c}:`,_),{success:!1,message:`Error fetching subscription: ${_.message}`};if(!p)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${c}, skipping expiry processing`),{success:!0,message:"No subscription found to expire"};console.log(`[RAZORPAY_WEBHOOK] Transitioning expired subscription ${c} to free plan`);let b=new Date().toISOString(),g=await (0,o.M)({subscription_id:c,business_profile_id:p.business_profile_id,subscription_status:"active",has_active_subscription:!1,additional_data:{plan_id:"free",plan_cycle:"monthly",subscription_start_date:b,subscription_expiry_time:null,subscription_charge_time:null,cancelled_at:b,razorpay_subscription_id:null,razorpay_customer_id:null,razorpay_plan_id:null,last_payment_id:null,last_payment_date:null,last_payment_method:null,updated_at:b}});if(g.success)return await i.webhookProcessor.markEventAsSuccess(r.eventId,"Subscription expired and transitioned to free plan"),console.log(`[RAZORPAY_WEBHOOK] Successfully transitioned subscription ${c} to free plan`),{success:!0,message:"Subscription expired and transitioned to free plan"};return await i.webhookProcessor.markEventAsFailed(r.eventId,g.message),console.error(`[RAZORPAY_WEBHOOK] Failed to transition subscription ${c} to free plan:`,g.message),g}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription expired:",e),{success:!1,message:`Error handling subscription expired: ${e instanceof Error?e.message:String(e)}`}}}i=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},61225:(e,t,s)=>{async function r(e,t,s){try{let t=e.payload.refund;if(!t)return console.error("[RAZORPAY_WEBHOOK] Refund data not found in payload"),{success:!1,message:"Refund data not found in payload"};let s=t.entity.id,r=t.entity.payment_id,n=t.entity.amount/100,a=t.entity.currency;return t.entity.status,console.log(`[RAZORPAY_WEBHOOK] Refund created: ${s} for payment ${r} - ${n} ${a}`),t.entity.speed_requested,t.entity.notes&&Object.keys(t.entity.notes).length,{success:!0,message:`Refund created event processed for refund ${s}`}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling refund created:",e),{success:!1,message:`Error handling refund created: ${e instanceof Error?e.message:String(e)}`}}}async function n(e,t,s){try{let t=e.payload.refund;if(!t)return console.error("[RAZORPAY_WEBHOOK] Refund data not found in payload"),{success:!1,message:"Refund data not found in payload"};let s=t.entity.id,r=t.entity.status;return t.entity.speed_processed,console.log(`[RAZORPAY_WEBHOOK] Refund processed: ${s} - status: ${r}`),{success:!0,message:`Refund processed event handled for refund ${s}`}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling refund processed:",e),{success:!1,message:`Error handling refund processed: ${e instanceof Error?e.message:String(e)}`}}}async function a(e,t,s){try{let t=e.payload.refund;if(!t)return console.error("[RAZORPAY_WEBHOOK] Refund data not found in payload"),{success:!1,message:"Refund data not found in payload"};let s=t.entity.id,r=t.entity.status,n=t.entity.payment_id;return console.log(`[RAZORPAY_WEBHOOK] Refund failed: ${s} for payment ${n} - status: ${r}`),t.entity.notes&&Object.keys(t.entity.notes).length,{success:!0,message:`Refund failed event handled for refund ${s}`}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling refund failed:",e),{success:!1,message:`Error handling refund failed: ${e instanceof Error?e.message:String(e)}`}}}s.d(t,{F9:()=>n,eA:()=>a,j_:()=>r})},67332:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{F9:()=>o.F9,eA:()=>o.eA,e_:()=>i.e_,et:()=>i.et,j_:()=>o.j_,rt:()=>i.rt,u$:()=>i.u$});var n=s(33419),a=s(37931),i=s(24703),o=s(61225);s(65193);var c=e([n,a,i]);[n,a,i]=c.then?(await c)():c,r()}catch(e){r(e)}})},69423:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{X:()=>l});var n=s(32032),a=s(65193),i=s(28485),o=s(30255),c=e([i]);async function l(e,t,r){let c=null;try{let t=e.payload.subscription;if(!t||!t.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let l=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription cancelled: ${l}`);let u=(0,a.extractWebhookTimestamp)(e);c={subscriptionId:l,eventType:"subscription.cancelled",eventId:r||`cancelled_${l}_${Date.now()}`,payload:e,webhookTimestamp:u};let d=await i.webhookProcessor.processWebhookEvent(c);if(!d.shouldProcess)return{success:d.success,message:d.message};let p=await (0,n.createClient)(),{data:_,error:b}=await p.from("payment_subscriptions").select("subscription_status, plan_id, business_profile_id").eq("razorpay_subscription_id",l).maybeSingle();if(b)return console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${l}:`,b),{success:!1,message:`Error fetching subscription: ${b.message}`};if(!_)return console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${l}, skipping cancellation processing`),{success:!0,message:"No subscription found to cancel"};if("authenticated"===_.subscription_status){console.log(`[RAZORPAY_WEBHOOK] Reverting authenticated subscription ${l} to trial (Plan A cancellation)`);let e=await (0,o.DE)(l,_.business_profile_id);if(!e.success)return await i.webhookProcessor.markEventAsFailed(c.eventId,e.message),e;{console.log(`[RAZORPAY_WEBHOOK] Clearing Razorpay IDs for cancelled authenticated subscription ${l}`);let{clearRazorpayColumnsAfterCancellation:e}=await Promise.resolve().then(s.bind(s,30255)),t=await e(l,_.business_profile_id);return t.success?console.log(`[RAZORPAY_WEBHOOK] Successfully cleared Razorpay IDs for subscription ${l}`):console.error(`[RAZORPAY_WEBHOOK] Failed to clear Razorpay IDs: ${t.message}`),await i.webhookProcessor.markEventAsSuccess(c.eventId,"Authenticated subscription reverted to trial and Razorpay IDs cleared"),{success:!0,message:"Authenticated subscription reverted to trial and Razorpay IDs cleared"}}}if("trial"===_.subscription_status&&"free"!==_.plan_id)return console.log(`[RAZORPAY_WEBHOOK] Subscription ${l} already in trial status (Plan A cancellation already processed)`),await i.webhookProcessor.markEventAsSuccess(c.eventId,"Authenticated subscription already reverted to trial (idempotent)"),{success:!0,message:"Authenticated subscription already reverted to trial (idempotent)"};{console.log(`[RAZORPAY_WEBHOOK] Downgrading cancelled subscription ${l} to free plan`);let e=await (0,o.VC)(l,_.business_profile_id,"cancelled");if(e.success)return await i.webhookProcessor.markEventAsSuccess(c.eventId,"Subscription cancelled and downgraded to free plan"),{success:!0,message:"Subscription cancelled and downgraded to free plan"};return await i.webhookProcessor.markEventAsFailed(c.eventId,e.message),e}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription cancelled:",e),{success:!1,message:`Error handling subscription cancelled: ${e instanceof Error?e.message:String(e)}`}}}i=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},75447:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{T:()=>o});var n=s(65193),a=s(28485),i=e([a]);async function o(e,t,s){let r=null;try{let t=e.payload.subscription;if(!t||!t.entity)return console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload"),{success:!1,message:"Subscription data not found in payload"};let i=t.entity.id;console.log(`[RAZORPAY_WEBHOOK] Subscription updated: ${i}`);let o=(0,n.extractWebhookTimestamp)(e);r={subscriptionId:i,eventType:"subscription.updated",eventId:s||`updated_${i}_${Date.now()}`,payload:e,webhookTimestamp:o};let c=await a.webhookProcessor.processWebhookEvent(r);if(!c.shouldProcess)return{success:c.success,message:c.message};return{success:!0,message:"Subscription update acknowledged"}}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error handling subscription updated:",e),{success:!1,message:`Error handling subscription updated: ${e instanceof Error?e.message:String(e)}`}}}a=(i.then?(await i)():i)[0],r()}catch(e){r(e)}})},90258:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{D:()=>i});var n=s(32032),a=s(65193);class e{constructor(e){this.supabase=e}static async build(){let t=await (0,n.createClient)();return new e(t)}async getCurrentSubscriptionState(e){let{data:t,error:s}=await this.supabase.from("payment_subscriptions").select("id, business_profile_id, subscription_status, plan_id, cancelled_at, razorpay_subscription_id, updated_at, last_webhook_timestamp").eq("razorpay_subscription_id",e).maybeSingle();return s?(console.error("[SubscriptionManager] Error fetching subscription state:",s),null):t}async createMissingSubscriptionRecord(e){try{let t=e.payload,s=t?.payload?.subscription?.entity||t?.subscription?.entity;if(!s)return console.error(`[SubscriptionManager] No subscription data in webhook payload for ${e.subscriptionId}`),null;let r=s.notes?.business_profile_id||s.notes?.user_id;if(!r)return console.error(`[SubscriptionManager] No business_profile_id in subscription notes for ${e.subscriptionId}`),null;let{data:n,error:a}=await this.supabase.from("business_profiles").select("id").eq("id",r).single();if(a||!n)return console.error(`[SubscriptionManager] Business profile ${r} not found for subscription ${e.subscriptionId}`),null;let i=s.notes?.plan_type||"basic",o=s.notes?.plan_cycle||"monthly",c="created";"subscription.authenticated"===e.eventType?c="authenticated":"subscription.activated"===e.eventType&&(c="active");let{data:l,error:u}=await this.supabase.from("payment_subscriptions").insert({business_profile_id:r,razorpay_subscription_id:e.subscriptionId,plan_id:i,plan_cycle:o,subscription_status:c,razorpay_customer_id:s.customer_id||null,subscription_start_date:s.current_start?new Date(1e3*s.current_start).toISOString():null,subscription_expiry_time:s.current_end?new Date(1e3*s.current_end).toISOString():null,subscription_charge_time:s.charge_at?new Date(1e3*s.charge_at).toISOString():null,last_webhook_timestamp:e.webhookTimestamp?new Date(1e3*e.webhookTimestamp).toISOString():null}).select().single();if(u)return console.error("[SubscriptionManager] Error creating missing subscription record:",u),null;return l}catch(e){return console.error("[SubscriptionManager] Error in createMissingSubscriptionRecord:",e),null}}async updateSubscriptionStatus(e,t,s={},r){try{let n=await this.getCurrentSubscriptionState(e);if(!n)return console.warn(`[SubscriptionManager] Subscription ${e} not found during status update`),{success:!1,message:"Subscription not found"};let i=a.SubscriptionStateManager.shouldHaveActiveSubscription(t,s.plan_id||n.plan_id||"free"),{data:o,error:c}=await this.supabase.rpc("update_subscription_atomic",{p_subscription_id:e,p_new_status:t,p_business_profile_id:n.business_profile_id,p_has_active_subscription:i,p_additional_data:s,p_webhook_timestamp:r?new Date(1e3*r).toISOString():null});if(c)return console.error("[SubscriptionManager] RPC error updating subscription:",c),{success:!1,message:c.message};if(!o?.success)return console.error("[SubscriptionManager] RPC function returned error:",o),{success:!1,message:o?.error||"Unknown RPC error"};return{success:!0,message:`Atomically updated subscription to ${t} with has_active_subscription=${i}`}}catch(e){return console.error("[SubscriptionManager] Exception updating subscription:",e),{success:!1,message:`Exception: ${e instanceof Error?e.message:String(e)}`}}}}let i=await e.build();r()}catch(e){r(e)}},1)},92021:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{$O:()=>d.$,J0:()=>a.J,Tb:()=>p.T,VA:()=>o.V,X7:()=>l.X,Y4:()=>i.Y,h:()=>n.h,je:()=>u.j,s1:()=>c.s});var n=s(10567),a=s(28539),i=s(30260),o=s(14055),c=s(5432),l=s(69423),u=s(301),d=s(47249),p=s(75447),_=e([n,a,i,o,c,l,u,d,p]);[n,a,i,o,c,l,u,d,p]=_.then?(await _)():_,r()}catch(e){r(e)}})},95453:(e,t,s)=>{s.d(t,{ST:()=>a,bG:()=>o,t6:()=>c});var r=s(55511),n=s.n(r);let a="https://api.razorpay.com/v2",i=()=>{let e,t;if(e=process.env.RAZORPAY_LIVE_KEY_ID||"***********************",t=process.env.RAZORPAY_LIVE_SECRET_KEY||"ZE2AurACFXhx0b1sQaLG4YfQ",!e||!t)throw console.error("[RAZORPAY] Missing credentials:",{keyId:!!e,keySecret:!!t,env:"production"}),Error("Razorpay credentials not configured");return{keyId:e,keySecret:t}},o=()=>{let{keyId:e,keySecret:t}=i(),s=Buffer.from(`${e}:${t}`).toString("base64");return{Authorization:`Basic ${s}`,"Content-Type":"application/json"}},c=(e,t,s)=>{try{let r=n().createHmac("sha256",s).update(e).digest("hex");return n().timingSafeEqual(Buffer.from(t),Buffer.from(r))}catch(e){return console.error("[RAZORPAY_WEBHOOK] Error verifying webhook signature:",e),!1}}}};