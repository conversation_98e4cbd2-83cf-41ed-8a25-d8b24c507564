'use client';

import { MessageCircle, Phone, Heart, Share2 } from 'lucide-react';
import WhatsAppIcon from '@/app/components/icons/WhatsAppIcon';

interface PostActionsProps {
  business: {
    business_name: string | null;
    whatsapp_number: string | null;
    phone: string | null;
  };
  hasWhatsApp: boolean;
  hasPhone: boolean;
  _postId: string;
  onShare?: () => void;
}

export default function PostActions({ business, hasWhatsApp, hasPhone, _postId, onShare }: PostActionsProps) {
  // Handle contact actions
  const handleWhatsAppClick = () => {
    if (hasWhatsApp) {
      const whatsappNumber = business.whatsapp_number?.replace(/\D/g, ''); // Remove non-digits
      const message = `Hi ${business.business_name}, I saw your post and would like to know more.`;
      window.open(`https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`, '_blank');
    }
  };

  const handlePhoneClick = () => {
    if (hasPhone) {
      window.open(`tel:${business.phone}`, '_self');
    }
  };

  const handleShareClick = () => {
    if (onShare) {
      onShare();
    }
  };

  const _buttonVariants = {
    hover: { scale: 1.02, transition: { duration: 0.2 } },
    tap: { scale: 0.98, transition: { duration: 0.1 } }
  };

  // Only show buttons if they have valid contact info - match React Native behavior
  const showWhatsApp = hasWhatsApp;
  const showPhone = hasPhone;

  // If neither button should be shown, don't render anything
  if (!showWhatsApp && !showPhone) {
    return null;
  }

  return (
    <div className="flex items-center justify-between w-full">
      {/* Left side - Social interaction icons */}
      <div className="flex gap-4">
        <button
          className="p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors"
          title="Like"
          disabled
        >
          <Heart className="h-5 w-5" />
        </button>
        <button
          className="p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors"
          title="Comment"
          disabled
        >
          <MessageCircle className="h-5 w-5" />
        </button>
        <button
          className="p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors"
          title="Share"
          onClick={handleShareClick}
        >
          <Share2 className="h-5 w-5" />
        </button>
      </div>

      {/* Right side - Contact actions */}
      <div className="flex gap-4">
        {showWhatsApp && (
          <button
            onClick={handleWhatsAppClick}
            className="p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors"
            title="WhatsApp"
          >
            <WhatsAppIcon className="h-5 w-5" />
          </button>
        )}

        {showPhone && (
          <button
            onClick={handlePhoneClick}
            className="p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors"
            title="Call Now"
          >
            <Phone className="h-5 w-5" />
          </button>
        )}
      </div>
    </div>
  );
}
