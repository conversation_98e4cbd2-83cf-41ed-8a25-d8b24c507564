import React from "react";
import { <PERSON>ada<PERSON> } from "next";
import ChooseRole<PERSON>lient from "./ChooseRoleClient";
import { redirect as nextRedirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { TABLES, COLUMNS } from "@/lib/supabase/constants";


// getAuthenticatedUser
async function getAuthenticatedUser() {
  const supabase = await createClient();
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error("Error fetching authenticated user:", error.message);
      return { user: null, error: "User not found or authentication error." };
    }
    return { user, error: null };
  } catch (err) {
    console.error("Unexpected error fetching authenticated user:", err);
    return { user: null, error: "An unexpected error occurred." };
  }
}

// checkIfCustomerProfileExists
async function checkIfCustomerProfileExists(userId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, userId)
      .maybeSingle();

    if (error) {
      console.error("Error checking existing profile:", error.message);
      return { exists: false, error: "Database error checking profile." };
    }

    return { exists: !!data, error: null };
  } catch (err) {
    console.error("Unexpected error checking profile:", err);
    return { exists: false, error: "An unexpected error occurred." };
  }
}

// checkIfBusinessProfileExists
async function checkIfBusinessProfileExists(userId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, userId)
      .maybeSingle();

    if (error) {
      console.error("Error checking existing business profile:", error.message);
      return { exists: false, error: "Database error checking business profile." };
    }

    return { exists: !!data, error: null };
  } catch (err) {
    console.error("Unexpected error checking business profile:", err);
    return { exists: false, error: "An unexpected error occurred." };
  }
}

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Choose Your Role", // Uses template: "Choose Your Role - Dukancard"
    description: "Select how you will use Dukancard.",
    robots: "noindex, nofollow", // Keep preventing indexing and following
  };
}

// This page should only be accessible to logged-in users without a profile
export default async function ChooseRolePage({
  searchParams,
}: {
  searchParams: Promise<{ redirect?: string; message?: string }>;
}) {
  const { redirect, message } = await searchParams;
  const redirectSlug = redirect || null;
  const messageParam = message || null;

  const { user, error: _userError } = await getAuthenticatedUser();

  if (!user) {
    // Should be handled by middleware, but good safeguard
    return nextRedirect("/login");
  }

  // Check if profile already exists in either table (middleware should prevent this, but double-check)
  const [customerProfileRes, businessProfileRes] = await Promise.all([
    checkIfCustomerProfileExists(user.id),
    checkIfBusinessProfileExists(user.id),
  ]);

  if (customerProfileRes.error || businessProfileRes.error) {
    // Handle error appropriately - redirect to login for safety
    return nextRedirect("/login?message=Error checking profile status");
  }

  if (customerProfileRes.exists || businessProfileRes.exists) {
    // User already has a profile, redirect them away
    const userType = customerProfileRes.exists ? "customer" : "business";
    const redirectPath =
      userType === "business" ? "/dashboard/business" : "/dashboard/customer";
    return nextRedirect(redirectPath);
  }

  // If no profile exists and user is logged in, render the choice component
  // This div acts as a minimal layout for this specific route
  return (
      <ChooseRoleClient userId={user.id} redirectSlug={redirectSlug} message={messageParam} />
  );
}
