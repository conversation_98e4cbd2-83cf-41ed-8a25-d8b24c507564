"use client";

import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface EnhancedMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description: string;
  color: "rose" | "blue" | "amber" | "green" | "purple" | "indigo";
  isUpdated?: boolean;
  suffix?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export default function EnhancedMetricCard({
  title,
  value,
  icon: Icon,
  description,
  color,
  isUpdated = false,
  suffix,
  trend,
}: EnhancedMetricCardProps) {
  // Define color variants for modern SaaS design
  const colorVariants = {
    rose: {
      bgLight: "bg-rose-50",
      bgDark: "dark:bg-rose-900/20",
      textLight: "text-rose-600",
      textDark: "dark:text-rose-400",
    },
    blue: {
      bgLight: "bg-blue-50",
      bgDark: "dark:bg-blue-900/20",
      textLight: "text-blue-600",
      textDark: "dark:text-blue-400",
    },
    amber: {
      bgLight: "bg-amber-50",
      bgDark: "dark:bg-amber-900/20",
      textLight: "text-amber-600",
      textDark: "dark:text-amber-400",
    },
    green: {
      bgLight: "bg-emerald-50",
      bgDark: "dark:bg-emerald-900/20",
      textLight: "text-emerald-600",
      textDark: "dark:text-emerald-400",
    },
    purple: {
      bgLight: "bg-purple-50",
      bgDark: "dark:bg-purple-900/20",
      textLight: "text-purple-600",
      textDark: "dark:text-purple-400",
    },
    indigo: {
      bgLight: "bg-indigo-50",
      bgDark: "dark:bg-indigo-900/20",
      textLight: "text-indigo-600",
      textDark: "dark:text-indigo-400",
    },
  };

  const selectedColor = colorVariants[color];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  const counterVariants = {
    initial: { scale: 1 },
    update: {
      scale: [1, 1.1, 1],
      transition: { duration: 0.3 },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
    >
      <div
        className="group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6"
      >
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40" />
        <div className="relative z-10">
          <div className="flex items-start justify-between mb-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400 tracking-wide">
                {title}
              </p>
              <div className="flex items-baseline gap-2">
                <motion.h3
                  className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 tracking-tight"
                  variants={counterVariants}
                  initial="initial"
                  animate={isUpdated ? "update" : "initial"}
                >
                  {value}
                  {suffix && <span className="text-lg text-neutral-500 dark:text-neutral-400 ml-1">{suffix}</span>}
                </motion.h3>
                {/* Trend indicator */}
                {trend && (
                  <motion.div
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className={cn(
                      "text-sm font-medium flex items-center",
                      trend.isPositive ? "text-emerald-600 dark:text-emerald-400" : "text-red-600 dark:text-red-400"
                    )}
                  >
                    {trend.isPositive ? "↗" : "↘"} {Math.abs(trend.value)}%
                  </motion.div>
                )}
              </div>
            </div>

            <div className={cn(
              "flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300",
              selectedColor.bgLight, selectedColor.bgDark, selectedColor.textLight, selectedColor.textDark
            )}>
              <Icon className="w-6 h-6" />
            </div>
          </div>
          <div className="space-y-3">
            <p className="text-sm text-neutral-500 dark:text-neutral-400 leading-relaxed">
              {description}
            </p>

            {/* Update indicator */}
            {isUpdated && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 text-xs font-medium text-primary"
              >
                <div className="w-2 h-2 rounded-full bg-primary animate-pulse" />
                Updated
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}
