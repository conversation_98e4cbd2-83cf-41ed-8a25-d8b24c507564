"use client";

import { useState, useEffect, useCallback } from "react";
import { Bell } from "lucide-react";
import { useRouter } from "next/navigation";
import SubscriptionListClient from "../SubscriptionListClient";
import { SubscriptionSearch, SubscriptionListSkeleton } from "@/app/components/shared/subscriptions";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { formatIndianNumberShort } from "@/lib/utils";
import { SubscriptionWithProfile } from "../actions";

interface SubscriptionsPageClientProps {
  initialSubscriptions: SubscriptionWithProfile[];
  totalCount: number;
  currentPage: number;
  searchTerm: string;
}

export default function SubscriptionsPageClient({
  initialSubscriptions,
  totalCount,
  currentPage,
  searchTerm,
}: SubscriptionsPageClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Calculate total pages
  const itemsPerPage = 12; // Optimized for 3-column grid
  const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));

  // Reset loading state when component receives new data
  useEffect(() => {
    setIsLoading(false);
  }, [initialSubscriptions]);

  // Handle search - use useCallback to prevent infinite re-renders
  const handleSearch = useCallback((term: string) => {
    setIsLoading(true); // Show loading state
    const params = new URLSearchParams();
    if (term) {
      params.set('search', term);
    }
    params.set('page', '1'); // Reset to page 1 on new search
    router.push(`/dashboard/customer/subscriptions?${params.toString()}`);
  }, [router]);

  // Handle page change - use useCallback to prevent infinite re-renders
  const handlePageChange = useCallback((page: number) => {
    setIsLoading(true); // Show loading state
    const params = new URLSearchParams();
    if (searchTerm) {
      params.set('search', searchTerm);
    }
    params.set('page', page.toString());
    router.push(`/dashboard/customer/subscriptions?${params.toString()}`);
  }, [router, searchTerm]);

  return (
    <div className="space-y-6">
      {/* Header Section with proper layout */}
      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-xl bg-muted hidden sm:block">
              <Bell className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-foreground">
                Subscribed Businesses
              </h1>
              <p className="text-muted-foreground mt-1">
                {formatIndianNumberShort(totalCount)} {totalCount === 1 ? 'business' : 'businesses'} you&apos;re following for updates
              </p>
            </div>
          </div>

          {/* Search Section - aligned to the right on desktop */}
          <div className="w-full sm:w-80">
            <SubscriptionSearch
              onSearch={handleSearch}
              initialSearchTerm={searchTerm}
              placeholder="Search businesses..."
            />
          </div>
        </div>

        {/* Results count */}
        {searchTerm && !isLoading && (
          <div className="text-sm text-muted-foreground border-l-4 border-primary pl-4">
            Found {formatIndianNumberShort(totalCount)} {totalCount === 1 ? 'business' : 'businesses'}
            {searchTerm ? ` matching "${searchTerm}"` : ''}
          </div>
        )}
      </div>

      {/* Content Section */}
      <div className="space-y-6">
        {/* Show skeleton loader when loading */}
        {isLoading ? (
          <SubscriptionListSkeleton />
        ) : (
          <>
            {/* Subscription List */}
            <SubscriptionListClient
              initialSubscriptions={initialSubscriptions}
              totalCount={totalCount}
              currentPage={currentPage}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center pt-6">
                <Pagination>
                  <PaginationContent>
                    {currentPage > 1 && (
                      <PaginationItem>
                        <PaginationPrevious
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(currentPage - 1);
                          }}
                        />
                      </PaginationItem>
                    )}

                    {/* Generate page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(pageNum);
                            }}
                            isActive={currentPage === pageNum}
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    {currentPage < totalPages && (
                      <PaginationItem>
                        <PaginationNext
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(currentPage + 1);
                          }}
                        />
                      </PaginationItem>
                    )}
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
