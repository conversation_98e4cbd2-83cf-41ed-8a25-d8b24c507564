# Supabase Cron Jobs Documentation

This document provides an overview of all the cron jobs running in the Dukancard Supabase project. These scheduled tasks automate various maintenance and data processing operations.

## Overview of Cron Jobs

| Job ID | Job Name | Schedule | Description |
|--------|----------|----------|-------------|
| 1 | cleanup-expired-ads | 0 0 * * * | Deactivates expired advertisements daily at midnight |
| 8 | process-expired-trials | 0 0 * * * | Processes expired trials and downgrades to free plan daily at midnight |
| 4 | cleanup-webhook-events | 0 0 * * 0 | Cleans up old webhook events weekly on Sundays at midnight |
| 2 | reset-visit-counts | 30 18 * * * | Resets daily visit counts and updates period visit counts daily at 6:30 PM |
| 5 | update-monthly-visit-metrics | 30 18 1 * * | Updates monthly visit metrics on the 1st day of each month at 6:30 PM |
| 3 | clean-card-visits | 30 19 * * * | Removes card visit records older than 31 days daily at 7:30 PM |

## Detailed Job Descriptions

### 1. cleanup-expired-ads
- **Schedule**: Daily at midnight (0 0 * * *)
- **Function**: `cleanup_expired_ads()`
- **Purpose**: Automatically deactivates advertisements that have reached their expiration date.
- **Operation**: Updates the `is_active` field to `FALSE` for all ads in the `custom_ads` table where the expiration date has passed.
- **Returns**: The number of ads that were deactivated.

### 2. process-expired-trials
- **Schedule**: Daily at midnight (0 0 * * *)
- **Function**: `check_and_process_expired_trials()`
- **Purpose**: Identifies business profiles with expired trial periods and downgrades them to the free plan if they don't have an active paid subscription.
- **Operation**:
  1. Identifies business profiles where the trial has expired
  2. Checks if they have an active subscription
  3. For those without active subscriptions:
     - Sets `has_active_subscription` to `FALSE` in the `business_profiles` table (expired trials are not active subscriptions)
     - Updates `subscription_status` to `'active'`, `plan_id` to `'free'`, `plan_cycle` to `'monthly'`, and `subscription_start_date` to current date in the `payment_subscriptions` table
     - Dynamically clears all other nullable columns in `payment_subscriptions` (Razorpay fields, payment data, etc.) for clean free plan records
     - Product limits are automatically enforced by the `enforce_product_limits_on_plan_change` trigger when `plan_id` changes to 'free'
- **Implementation**: Runs entirely on Supabase using `SELECT check_and_process_expired_trials();` - no application-level code needed
- **Returns**: The number of profiles that were downgraded.

### 3. cleanup-webhook-events
- **Schedule**: Weekly on Sundays at midnight (0 0 * * 0)
- **Function**: `cleanup_webhook_events()`
- **Purpose**: Maintains the webhook events table by removing old records to prevent excessive database growth.
- **Operation**:
  - Deletes successfully processed events older than 30 days
  - Deletes failed or retrying events older than 90 days
  - Uses Common Table Expressions (CTEs) to count deleted records
  - Logs the number of deleted records via RAISE NOTICE
- **Implementation**:
  ```sql
  CREATE OR REPLACE FUNCTION public.cleanup_webhook_events()
   RETURNS void
   LANGUAGE plpgsql
  AS $function$
  DECLARE
    success_cutoff TIMESTAMP WITH TIME ZONE := NOW() - INTERVAL '30 days';
    failed_cutoff TIMESTAMP WITH TIME ZONE := NOW() - INTERVAL '90 days';
    deleted_success INTEGER;
    deleted_failed INTEGER;
  BEGIN
    -- Delete successfully processed events older than 30 days
    WITH deleted AS (
      DELETE FROM processed_webhook_events
      WHERE status = 'processed' AND processed_at < success_cutoff
      RETURNING *
    )
    SELECT COUNT(*) INTO deleted_success FROM deleted;

    -- Delete failed events older than 90 days
    WITH deleted AS (
      DELETE FROM processed_webhook_events
      WHERE status IN ('failed', 'retrying') AND processed_at < failed_cutoff
      RETURNING *
    )
    SELECT COUNT(*) INTO deleted_failed FROM deleted;

    RAISE NOTICE 'Deleted % processed events and % failed events',
      deleted_success, deleted_failed;
  END;
  $function$
  ```
- **Returns**: None, but logs the number of deleted records via RAISE NOTICE.

### 4. reset-visit-counts
- **Schedule**: Daily at 6:30 PM (30 18 * * *)
- **Function**: `reset_all_visit_counts()`
- **Purpose**: Updates daily visit statistics for all business profiles.
- **Operation**:
  1. Calls `reset_daily_visit_counts()` which:
     - Updates `today_visits` and `yesterday_visits` in the `business_profiles` table based on unique visitors
     - Uses IST (Indian Standard Time) for date calculations
  2. Calls `update_period_visit_counts()` which:
     - Updates `visits_7_days` and `visits_30_days` in the `business_profiles` table
     - Counts unique visitors over the respective time periods
- **Returns**: None.

### 5. update-monthly-visit-metrics
- **Schedule**: Monthly on the 1st day at 6:30 PM (30 18 1 * *)
- **Function**: `update_all_monthly_visit_metrics()`
- **Purpose**: Calculates and stores monthly visit metrics for all business profiles.
- **Operation**:
  - For each business profile, inserts or updates a record in the `monthly_visit_metrics` table
  - Calculates unique visits for the current month based on the `card_visits` table
  - Uses IST (Indian Standard Time) for date calculations
- **Returns**: The number of business profiles that were updated.

### 6. clean-card-visits
- **Schedule**: Daily at 7:30 PM (30 19 * * *)
- **Function**: `clean_old_card_visits()`
- **Purpose**: Prevents the `card_visits` table from growing too large by removing old visit records.
- **Operation**: Deletes all visit records from the `card_visits` table that are older than 31 days.
- **Returns**: None, but logs a notice about the cleanup.

## Time Zone Considerations

All cron jobs run on the server's time zone (UTC), but several functions internally convert timestamps to IST (Asia/Kolkata) for proper date calculations relevant to the Indian market.
