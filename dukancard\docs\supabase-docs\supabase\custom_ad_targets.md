# Custom Ad Targets Table Documentation

## Table Overview

The `custom_ad_targets` table in the Dukancard application manages the geographic targeting rules for advertisements. It associates ads with specific postal codes (pincodes) or marks them as globally visible, enabling location-based ad delivery throughout the platform. 

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the ad target record |
| ad_id | uuid | NO | | Foreign key to custom_ads.id |
| pincode | varchar(6) | NO | | 6-digit Indian postal code for targeting |
| is_global | boolean | NO | false | Flag indicating whether the ad is globally visible (not restricted to specific pincodes) |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |

## Constraints

### Primary Key
- `custom_ad_targets_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `custom_ad_targets_ad_id_fkey` - Foreign key constraint linking `ad_id` to `custom_ads.id`

### Unique Constraints
- `custom_ad_targets_ad_id_pincode_key` - Ensures each ad can target a specific pincode only once
  - Columns: `ad_id`, `pincode`

### Not Null Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| custom_ad_targets_pkey | UNIQUE | id | Primary key index |
| custom_ad_targets_ad_id_pincode_key | UNIQUE | ad_id, pincode | Ensures unique combination of ad and pincode |
| idx_custom_ad_targets_pincode | BTREE | pincode | Index for faster pincode lookups |
| idx_custom_ad_targets_global | BTREE | is_global | Partial index for quickly finding global ads |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Authenticated users can manage all ad targets | ALL | true | |
| Public can read ad targets | SELECT | true | |

These policies ensure that:
1. Public users can view all ad targeting rules
2. Authenticated users (likely administrators) have full control over ad targeting

## Related Tables

### custom_ads
The `custom_ads` table is referenced by the `ad_id` foreign key and contains the actual advertisement content and status information.

## Usage Notes

1. **Geographic Targeting**:
   - The table enables pincode-level targeting for advertisements
   - Each record associates an ad with a specific 6-digit Indian postal code
   - This allows for highly localized ad delivery based on user location

2. **Global Ads**:
   - The `is_global` flag allows ads to be marked as globally visible
   - Global ads are shown to all users regardless of their location
   - The partial index `idx_custom_ad_targets_global` optimizes queries for global ads

3. **Ad Targeting Strategy**:
   - An ad can have multiple targeting records, allowing it to be shown in multiple locations
   - An ad can be both targeted to specific pincodes and marked as global
   - The unique constraint ensures an ad cannot target the same pincode multiple times

4. **Ad Delivery Logic**:
   - When determining which ads to show a user, the application should:
     - Find ads targeted to the user's pincode
     - Include ads marked as global (`is_global = true`)
     - Filter out inactive or expired ads (using fields from the `custom_ads` table)

5. **Security**:
   - RLS policies allow public read access to all targeting rules
   - Only authenticated users (likely administrators) can create, update, or delete targeting rules
   - This separation ensures that ad targeting management is restricted to authorized personnel

6. **Performance Considerations**:
   - The `idx_custom_ad_targets_pincode` index optimizes lookups by pincode
   - The partial index on `is_global` optimizes the common query pattern of finding global ads
   - These indexes support efficient ad delivery based on user location

7. **Data Relationships**:
   - Each targeting rule is associated with exactly one ad
   - An ad can have multiple targeting rules (one-to-many relationship)
   - This allows for complex geographic targeting strategies

8. **Integration with Pincodes Table**:
   - While not directly linked via foreign key, the `pincode` field should contain values that exist in the `pincodes` table
   - This ensures that ads are targeted to valid geographic locations
