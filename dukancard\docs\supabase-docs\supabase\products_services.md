# Products and Services Table Documentation

## Table Overview

The `products_services` table in the Dukancard application stores information about products and services offered by businesses. It allows businesses to showcase their offerings with details such as name, description, pricing, images, and availability status.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the product/service record |
| business_id | uuid | NO | | Foreign key to business_profiles.id |
| name | varchar(100) | NO | | Name of the product or service |
| description | varchar(500) | YES | | Detailed description of the product or service |
| base_price | numeric | YES | | Original price of the product or service |
| is_available | boolean | NO | true | Flag indicating whether the product/service is currently available |
| image_url | text | YES | | URL to the main product image (legacy field) |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |
| product_type | text | YES | 'physical' | Type of product (e.g., 'physical', 'digital', 'service') |
| discounted_price | numeric | YES | | Discounted/sale price of the product or service |
| slug | text | YES | | URL-friendly version of the product name (auto-generated) |
| images | text[] | YES | '{}' | Array of image URLs for the product/service |
| featured_image_index | integer | YES | 0 | Index of the featured image in the images array |

## Constraints

### Primary Key
- `products_services_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `products_services_business_id_fkey` - Foreign key constraint linking `business_id` to `business_profiles.id`

### Unique Constraints
- `products_services_slug_key` - Ensures product slugs are unique

### Check Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| products_services_pkey | UNIQUE | id | Primary key index |
| products_services_slug_key | UNIQUE | slug | Unique constraint index for slug |
| idx_products_services_business_id | BTREE | business_id | Index for faster business profile lookups |

## Triggers

### enforce_product_limit
- **Events**: INSERT, UPDATE
- **Function**: check_product_limit()
- **Description**: Enforces product limits based on the business's subscription plan

### set_product_slug
- **Events**: INSERT, UPDATE
- **Function**: generate_product_slug()
- **Description**: Automatically generates a URL-friendly slug from the product name

### Trigger Function Definitions

#### check_product_limit()

```sql
CREATE OR REPLACE FUNCTION public.check_product_limit()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
DECLARE
  business_plan_id TEXT;
  plan_limit INTEGER;
  current_available_count INTEGER;
BEGIN
  -- Skip check if the product is being marked as unavailable
  IF (TG_OP = 'UPDATE' AND NEW.is_available = FALSE) THEN
    RETURN NEW;
  END IF;

  -- Get the business's current plan from payment_subscriptions
  SELECT plan_id INTO business_plan_id
  FROM payment_subscriptions
  WHERE business_profile_id = NEW.business_id
  ORDER BY created_at DESC
  LIMIT 1;

  -- Default to free plan if no subscription found
  IF business_plan_id IS NULL THEN
    business_plan_id := 'free';
  END IF;

  -- Set the plan limit based on the plan_id
  CASE business_plan_id
    WHEN 'free' THEN plan_limit := 5;
    WHEN 'basic' THEN plan_limit := 15;
    WHEN 'growth' THEN plan_limit := 50;
    WHEN 'pro' THEN plan_limit := NULL; -- NULL means unlimited
    WHEN 'enterprise' THEN plan_limit := NULL; -- NULL means unlimited
    ELSE plan_limit := 5; -- Default to free plan limit
  END CASE;

  -- If the plan has unlimited products, allow the operation
  IF plan_limit IS NULL THEN
    RETURN NEW;
  END IF;

  -- Count how many products are currently available for this business
  -- For INSERT operations, we need to check if adding one more would exceed the limit
  -- For UPDATE operations, we only need to check if the product is being marked as available
  IF (TG_OP = 'INSERT' AND NEW.is_available = TRUE) OR (TG_OP = 'UPDATE' AND OLD.is_available = FALSE AND NEW.is_available = TRUE) THEN
    SELECT COUNT(*) INTO current_available_count
    FROM products_services
    WHERE business_id = NEW.business_id
      AND is_available = TRUE
      AND (TG_OP = 'UPDATE' AND id != NEW.id); -- Exclude the current product for UPDATE operations
    
    -- For INSERT operations, we need to add 1 to account for the new product
    IF TG_OP = 'INSERT' THEN
      current_available_count := current_available_count + 1;
    END IF;

    -- Check if the count exceeds the plan limit
    IF current_available_count > plan_limit THEN
      RAISE EXCEPTION 'Cannot make product available: You have reached the limit of % available products for your % plan.', 
        plan_limit, business_plan_id;
    END IF;
  END IF;

  RETURN NEW;
END;
$function$
```

#### generate_product_slug()

```sql
CREATE OR REPLACE FUNCTION public.generate_product_slug()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
DECLARE
  base_slug TEXT;
  new_slug TEXT;
  counter INT := 1;
  slug_exists BOOLEAN;
BEGIN
  -- Convert the name to lowercase and replace spaces and special chars with hyphens
  base_slug := LOWER(REGEXP_REPLACE(NEW.name, '[^a-zA-Z0-9]', '-', 'g'));
  
  -- Remove consecutive hyphens
  base_slug := REGEXP_REPLACE(base_slug, '-+', '-', 'g');
  
  -- Remove leading and trailing hyphens
  base_slug := TRIM(BOTH '-' FROM base_slug);
  
  -- Initial slug attempt
  new_slug := base_slug;
  
  -- Check if the slug already exists
  LOOP
    EXECUTE 'SELECT EXISTS(SELECT 1 FROM products_services WHERE slug = $1 AND id != $2)' 
    INTO slug_exists
    USING new_slug, NEW.id;
    
    EXIT WHEN NOT slug_exists;
    
    -- If it exists, append a counter
    counter := counter + 1;
    new_slug := base_slug || '-' || counter;
  END LOOP;
  
  -- Set the new slug
  NEW.slug := new_slug;
  
  RETURN NEW;
END;
$function$
```

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Allow full access to owners | ALL | (auth.uid() = business_id) | |
| Allow public read access for items on online cards | SELECT | EXISTS (SELECT 1 FROM business_profiles bp WHERE bp.id = products_services.business_id AND bp.status = 'online') | |

These policies ensure that:
1. Business owners have full control over their own products and services
2. The public can only view products and services from businesses that are online

## Related Tables

### business_profiles
The `business_profiles` table is referenced by the `business_id` foreign key and contains information about the business that offers the products or services.

### payment_subscriptions
The `payment_subscriptions` table is referenced in the `check_product_limit` trigger function to determine the business's subscription plan and enforce product limits.

## Usage Notes

1. **Product Limits by Subscription Plan**:
   - Free plan: 5 products
   - Basic plan: 15 products
   - Growth plan: 50 products
   - Pro and Enterprise plans: Unlimited products
   - The `enforce_product_limit` trigger enforces these limits

2. **Product Images**:
   - The table supports multiple images per product (up to 5 as per project memory)
   - Images are stored as an array of URLs in the `images` field
   - The `featured_image_index` indicates which image should be displayed as the main image
   - The legacy `image_url` field is maintained for backward compatibility
 
3. **SEO and URL Structure**:
   - The `slug` field is automatically generated from the product name
   - It ensures URL-friendly product pages (e.g., `/business-name/product-name`)
   - The `set_product_slug` trigger handles slug generation and uniqueness

4. **Categorization**:
   - Products can be categorized using both `categories` and `tags` arrays
   - This allows for flexible filtering and searching of products

5. **Pricing**:
   - The table supports both regular (`base_price`) and sale (`discounted_price`) pricing
   - Prices are stored as numeric values to support proper currency handling

6. **Availability**:
   - The `is_available` flag controls whether a product is visible to customers
   - This allows businesses to temporarily hide products without deleting them
   - Products are only visible to the public if both the business is online and the product is available

7. **Product Types**:
   - The `product_type` field distinguishes between physical products, digital products, and services
   - This can be used for filtering and display customization

8. **Security**:
   - RLS policies ensure businesses can only manage their own products
   - Public access is restricted to products from online businesses
