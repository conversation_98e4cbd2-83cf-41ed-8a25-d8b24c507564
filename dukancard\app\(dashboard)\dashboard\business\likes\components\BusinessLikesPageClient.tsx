"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Star } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";

import { cn, formatIndianNumberShort } from "@/lib/utils";
import { LikeSearch, LikePagination, LikeListSkeleton } from "@/app/components/shared/likes";
import BusinessLikesReceivedList from "./BusinessLikesReceivedList";
import BusinessMyLikesList from "./BusinessMyLikesList";
import { BusinessLikeReceived, BusinessMyLike } from "../actions";

interface BusinessLikesPageClientProps {
  initialLikesReceived: BusinessLikeReceived[];
  likesReceivedCount: number;
  likesReceivedCurrentPage: number;
  initialMyLikes: BusinessMyLike[];
  myLikesCount: number;
  myLikesCurrentPage: number;
  searchTerm: string;
  activeTab: string;
}

export default function BusinessLikesPageClient({
  initialLikesReceived,
  likesReceivedCount,
  likesReceivedCurrentPage,
  initialMyLikes,
  myLikesCount,
  myLikesCurrentPage,
  searchTerm: initialSearchTerm,
  activeTab: initialActiveTab
}: BusinessLikesPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [activeTab, setActiveTab] = useState(initialActiveTab);

  // Calculate current data based on active tab
  const _currentData = activeTab === 'my-likes' ? initialMyLikes : initialLikesReceived;
  const currentCount = activeTab === 'my-likes' ? myLikesCount : likesReceivedCount;
  const currentPage = activeTab === 'my-likes' ? myLikesCurrentPage : likesReceivedCurrentPage;
  const totalPages = Math.ceil(currentCount / 10);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };



  // Handle tab change
  const handleTabChange = useCallback((newTab: string) => {
    setIsLoading(true);
    setActiveTab(newTab);

    const params = new URLSearchParams(searchParams);
    if (newTab === 'my-likes') {
      params.set('tab', 'my-likes');
    } else {
      params.delete('tab');
    }
    params.delete('page'); // Reset to first page when changing tabs
    params.delete('search'); // Clear search when changing tabs
    setSearchTerm('');

    router.push(`/dashboard/business/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Handle search
  const handleSearch = useCallback((newSearchTerm: string) => {
    setIsLoading(true);
    setSearchTerm(newSearchTerm);

    const params = new URLSearchParams(searchParams);
    if (newSearchTerm) {
      params.set('search', newSearchTerm);
    } else {
      params.delete('search');
    }
    params.delete('page'); // Reset to first page when searching

    router.push(`/dashboard/business/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setIsLoading(true);

    const params = new URLSearchParams(searchParams);
    if (page > 1) {
      params.set('page', page.toString());
    } else {
      params.delete('page');
    }

    router.push(`/dashboard/business/likes?${params.toString()}`);
  }, [router, searchParams]);

  // Reset loading state when data changes
  useEffect(() => {
    setIsLoading(false);
  }, [initialLikesReceived, initialMyLikes]);

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Modern SaaS Header - Full Width */}
      <motion.div
        className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        <div className="space-y-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
              <Heart className="w-5 h-5 text-primary" />
            </div>
            <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
            <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
              Engagement Management
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
            Business Likes
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed">
            Manage your business likes and discover who appreciates your services. Track engagement and build meaningful connections.
          </p>
        </div>
      </motion.div>

      {/* Enhanced Tabs Section - Full Width */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <div className="w-full flex justify-center mb-8">
          <div className="flex p-1.5 rounded-2xl bg-neutral-100/80 dark:bg-neutral-800/80 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleTabChange('likes-received')}
              className={cn(
                "rounded-xl py-3 px-6 font-medium flex items-center gap-2 transition-all duration-200",
                activeTab === 'likes-received'
                  ? "bg-white dark:bg-neutral-900 text-neutral-900 dark:text-neutral-50 shadow-lg border-primary/20"
                  : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200"
              )}
            >
              <Heart className="w-4 h-4 text-rose-500" />
              <span>Likes Received</span>
              <span className="ml-1 px-2 py-0.5 text-xs bg-neutral-200 dark:bg-neutral-700 rounded-full">
                {formatIndianNumberShort(likesReceivedCount)}
              </span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleTabChange('my-likes')}
              className={cn(
                "rounded-xl py-3 px-6 font-medium flex items-center gap-2 transition-all duration-200",
                activeTab === 'my-likes'
                  ? "bg-white dark:bg-neutral-900 text-neutral-900 dark:text-neutral-50 shadow-lg border-primary/20"
                  : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200"
              )}
            >
              <Star className="w-4 h-4 text-amber-500" />
              <span>My Likes</span>
              <span className="ml-1 px-2 py-0.5 text-xs bg-neutral-200 dark:bg-neutral-700 rounded-full">
                {formatIndianNumberShort(myLikesCount)}
              </span>
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Enhanced Search Section - Full Width */}
      {activeTab === 'my-likes' && (
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="p-6 bg-neutral-50/50 dark:bg-neutral-900/20 rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50"
        >
          <div className="flex items-center gap-3 mb-4">
            <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Search your liked businesses:
            </span>
          </div>
          <LikeSearch
            onSearch={handleSearch}
            initialSearchTerm={searchTerm}
            placeholder="Search businesses by name..."
          />
        </motion.div>
      )}

      {/* Enhanced Content Section - Full Width */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        {/* Enhanced Count display - Only show for My Likes tab when searching */}
        {activeTab === 'my-likes' && searchTerm && !isLoading && (
          <div className="mb-8 p-4 bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-xl">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-primary"></div>
              <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                Found {formatIndianNumberShort(currentCount)} {currentCount === 1 ? 'result' : 'results'}
                {searchTerm ? ` matching "${searchTerm}"` : ''}
              </span>
            </div>
          </div>
        )}

        {/* Enhanced content with loading states */}
        {isLoading ? (
          <div className="space-y-6">
            <LikeListSkeleton />
          </div>
        ) : (
          <div className="space-y-8">
            {/* Enhanced Tab Content */}
            <div className="min-h-[400px]">
              {activeTab === 'likes-received' && (
                <BusinessLikesReceivedList initialLikes={initialLikesReceived} />
              )}
              {activeTab === 'my-likes' && (
                <BusinessMyLikesList initialLikes={initialMyLikes} />
              )}
            </div>

            {/* Enhanced Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center pt-8 border-t border-neutral-200/60 dark:border-neutral-700/60">
                <LikePagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
