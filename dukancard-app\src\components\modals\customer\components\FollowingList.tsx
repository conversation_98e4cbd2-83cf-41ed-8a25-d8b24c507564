import React, { useState, useCallback, useEffect } from "react";
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from "react-native";
import { useAuth } from "@/src/contexts/AuthContext";
import {
  subscriptionsService,
  SubscriptionWithProfile,
} from "@/backend/supabase/services/posts/socialService";
import { SubscriptionCard } from "@/src/components/social/SubscriptionCard";
import { FollowingModalSkeleton } from "@/src/components/skeletons/modals/FollowingModalSkeleton";
import { ErrorState } from "@/src/components/ui/ErrorState";
import { SocialEmptyState } from "@/src/components/shared/ui/SocialEmptyState";
import { useTheme } from "@/src/hooks/useTheme";
import { createFollowingModalStyles } from "@/styles/modals/customer/following-modal";
import { logError, handleNetworkError } from "@/src/utils/errorHandling";

interface FollowingListProps {
  searchTerm: string;
  onDiscoverPress: () => void;
}

export function FollowingList({ searchTerm, onDiscoverPress }: FollowingListProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const styles = createFollowingModalStyles(theme);

  const [subscriptions, setSubscriptions] = useState<SubscriptionWithProfile[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<any>(null);

  const fetchSubscriptions = useCallback(
    async (isRefresh = false) => {
      if (!user?.id) return;

      const currentPage = isRefresh ? 1 : page;
      if (isLoadingMore || (isLoading && !isRefresh)) return;

      if (isRefresh) {
        setIsRefreshing(true);
      } else if (currentPage === 1) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }
      setError(null);

      try {
        const result = await subscriptionsService.fetchSubscriptions(
          user.id,
          currentPage,
          20,
          searchTerm
        );
        setSubscriptions((prev) =>
          currentPage === 1 ? result.items : [...prev, ...result.items]
        );
        setHasMore(result.hasMore);
        if (currentPage === 1) setPage(2);
        else setPage((prev) => prev + 1);
      } catch (err) {
        const appError = handleNetworkError(err);
        setError(appError);
        logError(appError, "FollowingList.fetchSubscriptions");
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
        setIsLoadingMore(false);
      }
    },
    [user?.id, page, isLoading, isLoadingMore, searchTerm]
  );

  useEffect(() => {
    fetchSubscriptions(true);
  }, [searchTerm, fetchSubscriptions]);

  const handleRefresh = () => {
    setPage(1);
    fetchSubscriptions(true);
  };

  const handleLoadMore = () => {
    if (hasMore && !isLoadingMore && !isRefreshing) {
      fetchSubscriptions();
    }
  };

  const handleUnsubscribe = async (subscriptionId: string) => {
    try {
      await subscriptionsService.unsubscribe(subscriptionId);
      setSubscriptions((prev) =>
        prev.filter((sub) => sub.id !== subscriptionId)
      );
    } catch (err) {
      Alert.alert("Error", "Failed to unsubscribe. Please try again.");
      logError(err as Error, "FollowingList.handleUnsubscribe");
    }
  };

  const renderFooter = () => {
    if (!isLoadingMore) return null;
    return <ActivityIndicator style={{ marginVertical: 20 }} />;
  };

  if (isLoading && subscriptions.length === 0) {
    return <FollowingModalSkeleton />;
  }

  if (error && subscriptions.length === 0) {
    return (
      <ErrorState
        title={error.title}
        message={error.message}
        onRetry={handleRefresh}
      />
    );
  }

  return (
    <View style={styles.listContainer}>
      <FlatList
        data={subscriptions}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <SubscriptionCard
            subscription={item}
            onUnsubscribe={handleUnsubscribe}
          />
        )}
        contentContainerStyle={styles.listContentContainer}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          !isLoading ? (
            <SocialEmptyState
              type="following"
              searchTerm={searchTerm}
              isBusinessProfile={false}
              actionText="Discover businesses"
              onAction={onDiscoverPress}
            />
          ) : null
        }
      />
    </View>
  );
}
