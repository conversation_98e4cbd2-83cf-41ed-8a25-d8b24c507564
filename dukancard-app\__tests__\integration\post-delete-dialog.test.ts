/**
 * Integration tests for React Native post delete dialog functionality
 * Tests the migration from Alert.alert to PostDeleteDialog component
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import Toast from 'react-native-toast-message';
import { PostDeleteDialog } from '@/src/components/shared/PostDeleteDialog';
import { deleteBusinessPost } from '@/lib/actions/businessPosts';
import { deleteCustomerPost } from '@/lib/actions/customerPosts';

// Mock dependencies
jest.mock('@/lib/actions/businessPosts');
jest.mock('@/lib/actions/customerPosts');
jest.mock('react-native-toast-message');

const mockDeleteBusinessPost = deleteBusinessPost as jest.MockedFunction<typeof deleteBusinessPost>;
const mockDeleteCustomerPost = deleteCustomerPost as jest.MockedFunction<typeof deleteCustomerPost>;
const mockToastShow = Toast.show as jest.MockedFunction<typeof Toast.show>;

describe('Post Delete Dialog Integration', () => {
  const mockBusinessPost = {
    id: 'business-post-123',
    content: 'Test business post',
    post_source: 'business' as const,
    author_name: 'Business User',
    business_slug: 'test-business',
    created_at: '2024-01-01T00:00:00Z',
    image_url: null,
    product_ids: [],
    user_id: 'user-123',
  };

  const mockCustomerPost = {
    id: 'customer-post-456',
    content: 'Test customer post',
    post_source: 'customer' as const,
    author_name: 'Customer User',
    business_slug: null,
    created_at: '2024-01-01T00:00:00Z',
    image_url: null,
    product_ids: [],
    user_id: 'user-456',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Business Post Deletion', () => {
    it('should show delete dialog and handle successful deletion', async () => {
      mockDeleteBusinessPost.mockResolvedValue({
        success: true,
      });

      const mockOnDelete = jest.fn();
      const mockOnCancel = jest.fn();

      const { getByText } = render(
        <PostDeleteDialog
          visible={true}
          post={mockBusinessPost}
          onDelete={mockOnDelete}
          onCancel={mockOnCancel}
        />
      );

      // Verify delete dialog is shown
      expect(getByText('Delete Post')).toBeTruthy();
      expect(getByText('Are you sure you want to delete this post?')).toBeTruthy();

      // Confirm deletion
      const confirmButton = getByText('Delete');
      fireEvent.press(confirmButton);

      // Wait for deletion to complete
      await waitFor(() => {
        expect(mockDeleteBusinessPost).toHaveBeenCalledWith('business-post-123');
      });

      // Verify success toast is shown
      await waitFor(() => {
        expect(mockToastShow).toHaveBeenCalledWith({
          type: 'success',
          text1: 'Post deleted successfully',
          position: 'bottom',
          visibilityTime: 3000,
        });
      });

      // Verify callback is called
      expect(mockOnDelete).toHaveBeenCalled();
    });

    it('should handle business post deletion failure', async () => {
      mockDeleteBusinessPost.mockResolvedValue({
        success: false,
        error: 'Failed to delete business post',
      });

      const mockOnDelete = jest.fn();
      const mockOnCancel = jest.fn();

      const { getByText } = render(
        <PostDeleteDialog
          visible={true}
          post={mockBusinessPost}
          onDelete={mockOnDelete}
          onCancel={mockOnCancel}
        />
      );

      // Confirm deletion
      const confirmButton = getByText('Delete');
      fireEvent.press(confirmButton);

      await waitFor(() => {
        expect(mockDeleteBusinessPost).toHaveBeenCalledWith('business-post-123');
      });

      // Verify error toast is shown
      await waitFor(() => {
        expect(mockToastShow).toHaveBeenCalledWith({
          type: 'error',
          text1: 'Failed to delete post',
          text2: 'Failed to delete business post',
          position: 'bottom',
          visibilityTime: 4000,
        });
      });
    });
  });

  describe('Customer Post Deletion', () => {
    it('should show delete dialog and handle successful deletion', async () => {
      mockDeleteCustomerPost.mockResolvedValue({
        success: true,
      });

      const mockOnDelete = jest.fn();
      const mockOnCancel = jest.fn();

      const { getByText } = render(
        <PostDeleteDialog
          visible={true}
          post={mockCustomerPost}
          onDelete={mockOnDelete}
          onCancel={mockOnCancel}
        />
      );

      // Verify delete dialog is shown
      expect(getByText('Delete Post')).toBeTruthy();

      // Confirm deletion
      const confirmButton = getByText('Delete');
      fireEvent.press(confirmButton);

      await waitFor(() => {
        expect(mockDeleteCustomerPost).toHaveBeenCalledWith('customer-post-456');
      });

      // Verify success toast is shown
      await waitFor(() => {
        expect(mockToastShow).toHaveBeenCalledWith({
          type: 'success',
          text1: 'Post deleted successfully',
          position: 'bottom',
          visibilityTime: 3000,
        });
      });

      // Verify callback is called
      expect(mockOnDelete).toHaveBeenCalled();
    });

    it('should handle customer post deletion failure', async () => {
      mockDeleteCustomerPost.mockResolvedValue({
        success: false,
        error: 'Failed to delete customer post',
      });

      const mockOnDelete = jest.fn();
      const mockOnCancel = jest.fn();

      const { getByText } = render(
        <PostDeleteDialog
          visible={true}
          post={mockCustomerPost}
          onDelete={mockOnDelete}
          onCancel={mockOnCancel}
        />
      );

      // Confirm deletion
      const confirmButton = getByText('Delete');
      fireEvent.press(confirmButton);

      await waitFor(() => {
        expect(mockDeleteCustomerPost).toHaveBeenCalledWith('customer-post-456');
      });

      // Verify error toast is shown
      await waitFor(() => {
        expect(mockToastShow).toHaveBeenCalledWith({
          type: 'error',
          text1: 'Failed to delete post',
          text2: 'Failed to delete customer post',
          position: 'bottom',
          visibilityTime: 4000,
        });
      });
    });
  });

  describe('Dialog Cancellation', () => {
    it('should cancel deletion when cancel button is pressed', async () => {
      const mockOnDelete = jest.fn();
      const mockOnCancel = jest.fn();

      const { getByText } = render(
        <PostDeleteDialog
          visible={true}
          post={mockBusinessPost}
          onDelete={mockOnDelete}
          onCancel={mockOnCancel}
        />
      );

      // Verify delete dialog is shown
      expect(getByText('Delete Post')).toBeTruthy();

      // Cancel deletion
      const cancelButton = getByText('Cancel');
      fireEvent.press(cancelButton);

      // Verify dialog is dismissed and no deletion occurs
      expect(mockOnCancel).toHaveBeenCalled();
      expect(mockDeleteBusinessPost).not.toHaveBeenCalled();
      expect(mockDeleteCustomerPost).not.toHaveBeenCalled();
    });
  });

  describe('Loading States', () => {
    it('should show loading state during deletion', async () => {
      // Mock a delayed response
      mockDeleteBusinessPost.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({ success: true }), 100)
        )
      );

      const mockOnDelete = jest.fn();
      const mockOnCancel = jest.fn();

      const { getByText } = render(
        <PostDeleteDialog
          visible={true}
          post={mockBusinessPost}
          onDelete={mockOnDelete}
          onCancel={mockOnCancel}
        />
      );

      // Confirm deletion
      const confirmButton = getByText('Delete');
      fireEvent.press(confirmButton);

      // Verify loading state is shown
      expect(getByText('Deleting...')).toBeTruthy();

      // Wait for deletion to complete
      await waitFor(() => {
        expect(mockDeleteBusinessPost).toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle unexpected errors during deletion', async () => {
      mockDeleteBusinessPost.mockRejectedValue(new Error('Network error'));

      const mockOnDelete = jest.fn();
      const mockOnCancel = jest.fn();

      const { getByText } = render(
        <PostDeleteDialog
          visible={true}
          post={mockBusinessPost}
          onDelete={mockOnDelete}
          onCancel={mockOnCancel}
        />
      );

      // Confirm deletion
      const confirmButton = getByText('Delete');
      fireEvent.press(confirmButton);

      await waitFor(() => {
        expect(mockDeleteBusinessPost).toHaveBeenCalled();
      });

      // Verify error toast is shown
      await waitFor(() => {
        expect(mockToastShow).toHaveBeenCalledWith({
          type: 'error',
          text1: 'Failed to delete post',
          text2: 'Please try again',
          position: 'bottom',
          visibilityTime: 4000,
        });
      });
    });
  });

  describe('Dialog Visibility', () => {
    it('should not render when visible is false', () => {
      const mockOnDelete = jest.fn();
      const mockOnCancel = jest.fn();

      const { queryByText } = render(
        <PostDeleteDialog
          visible={false}
          post={mockBusinessPost}
          onDelete={mockOnDelete}
          onCancel={mockOnCancel}
        />
      );

      // Verify dialog is not shown
      expect(queryByText('Delete Post')).toBeNull();
    });

    it('should render when visible is true', () => {
      const mockOnDelete = jest.fn();
      const mockOnCancel = jest.fn();

      const { getByText } = render(
        <PostDeleteDialog
          visible={true}
          post={mockBusinessPost}
          onDelete={mockOnDelete}
          onCancel={mockOnCancel}
        />
      );

      // Verify dialog is shown
      expect(getByText('Delete Post')).toBeTruthy();
      expect(getByText('Are you sure you want to delete this post?')).toBeTruthy();
      expect(getByText('Cancel')).toBeTruthy();
      expect(getByText('Delete')).toBeTruthy();
    });
  });
});