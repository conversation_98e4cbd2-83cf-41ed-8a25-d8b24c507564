import { renderHook, waitFor } from "@testing-library/react";
import { createClient } from "@/utils/supabase/client";
import {
  usePostOwnership,
  UsePostOwnershipProps,
} from "@/components/feed/shared/hooks/usePostOwnership";

// Mock the Supabase client
const mockSupabase = {
  auth: {
    getUser: jest.fn(),
  },
};

jest.mock("@/utils/supabase/client", () => ({
  createClient: jest.fn(() => mockSupabase),
}));

describe("usePostOwnership", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("business posts", () => {
    it("should return isOwner true when user owns business post", async () => {
      const mockUser = { id: "business-123" };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const { result } = renderHook(() =>
        usePostOwnership({
          postBusinessId: "business-123",
          postCustomerId: undefined,
          postSource: "business",
        })
      );

      // Initially loading
      expect(result.current.isLoading).toBe(true);
      expect(result.current.isOwner).toBe(false);

      // Wait for the effect to complete
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isOwner).toBe(true);
    });

    it("should return isOwner false when user does not own business post", async () => {
      const mockUser = { id: "business-123" };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const { result } = renderHook(() =>
        usePostOwnership({
          postBusinessId: "different-business-456",
          postCustomerId: undefined,
          postSource: "business",
        })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isOwner).toBe(false);
    });

    it("should return isOwner false when postBusinessId is undefined", async () => {
      const mockUser = { id: "business-123" };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const { result } = renderHook(() =>
        usePostOwnership({
          postBusinessId: undefined,
          postCustomerId: undefined,
          postSource: "business",
        })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isOwner).toBe(false);
    });
  });

  describe("customer posts", () => {
    it("should return isOwner true when user owns customer post", async () => {
      const mockUser = { id: "customer-123" };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const { result } = renderHook(() =>
        usePostOwnership({
          postBusinessId: undefined,
          postCustomerId: "customer-123",
          postSource: "customer",
        })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isOwner).toBe(true);
    });

    it("should return isOwner false when user does not own customer post", async () => {
      const mockUser = { id: "customer-123" };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const { result } = renderHook(() =>
        usePostOwnership({
          postBusinessId: undefined,
          postCustomerId: "different-customer-456",
          postSource: "customer",
        })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isOwner).toBe(false);
    });

    it("should return isOwner false when postCustomerId is undefined", async () => {
      const mockUser = { id: "customer-123" };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const { result } = renderHook(() =>
        usePostOwnership({
          postBusinessId: undefined,
          postCustomerId: undefined,
          postSource: "customer",
        })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isOwner).toBe(false);
    });
  });

  describe("authentication errors", () => {
    it("should return isOwner false when user is not authenticated", async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: "Not authenticated" },
      });

      const { result } = renderHook(() =>
        usePostOwnership({
          postBusinessId: "business-123",
          postCustomerId: undefined,
          postSource: "business",
        })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isOwner).toBe(false);
    });

    it("should return isOwner false when auth.getUser throws error", async () => {
      mockSupabase.auth.getUser.mockRejectedValue(new Error("Auth error"));

      const { result } = renderHook(() =>
        usePostOwnership({
          postBusinessId: "business-123",
          postCustomerId: undefined,
          postSource: "business",
        })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isOwner).toBe(false);
    });
  });

  describe("prop changes", () => {
    it("should update ownership when props change", async () => {
      const mockUser = { id: "user-123" };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const { result, rerender } = renderHook(
        (props: UsePostOwnershipProps) => usePostOwnership(props),
        {
          initialProps: {
            postBusinessId: "user-123",
            postSource: "business" as const,
          } as UsePostOwnershipProps,
        }
      );

      // Wait for initial ownership check
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });
      expect(result.current.isOwner).toBe(true);

      // Change to a different business post
      rerender({
        postBusinessId: "different-user-456",
        postSource: "business",
      } as UsePostOwnershipProps);

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });
      expect(result.current.isOwner).toBe(false);

      // Change to customer post owned by same user
      rerender({
        postCustomerId: "user-123",
        postSource: "customer",
      } as UsePostOwnershipProps);

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });
      expect(result.current.isOwner).toBe(true);
    });
  });

  describe("edge cases", () => {
    it("should handle missing postSource gracefully", async () => {
      const mockUser = { id: "user-123" };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const { result } = renderHook(() =>
        usePostOwnership({
          postBusinessId: "user-123",
          postSource: "business" as "business" | "customer",
        })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isOwner).toBe(false);
    });

    it("should handle both postBusinessId and postCustomerId being provided", async () => {
      const mockUser = { id: "user-123" };
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      // When postSource is business, should check business ID
      const { result: businessResult } = renderHook(() =>
        usePostOwnership({
          postBusinessId: "user-123",
          postCustomerId: "different-user-456",
          postSource: "business",
        })
      );

      await waitFor(() => {
        expect(businessResult.current.isLoading).toBe(false);
      });
      expect(businessResult.current.isOwner).toBe(true);

      // When postSource is customer, should check customer ID
      const { result: customerResult } = renderHook(() =>
        usePostOwnership({
          postBusinessId: "different-user-456",
          postCustomerId: "user-123",
          postSource: "customer",
        })
      );

      await waitFor(() => {
        expect(customerResult.current.isLoading).toBe(false);
      });
      expect(customerResult.current.isOwner).toBe(true);
    });
  });
});
