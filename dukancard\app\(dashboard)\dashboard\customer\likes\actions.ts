import { likesService, LikeWithProfile, LikesResult } from '@/lib/services/socialService';

// Re-export types for compatibility
export type { LikeWithProfile };

/**
 * Fetch customer likes with pagination and search
 */
export async function fetchCustomerLikes(
  userId: string,
  page: number = 1,
  limit: number = 12, // Optimized for 3-column grid
  searchTerm: string = ""
): Promise<LikesResult> {
  try {
    return await likesService.fetchLikes(userId, page, limit, searchTerm);
  } catch (error) {
    console.error('Error in fetchCustomerLikes:', error);
    throw error;
  }
}
