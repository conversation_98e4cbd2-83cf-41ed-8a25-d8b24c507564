{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/addressValidation.ts"], "sourcesContent": ["/**\r\n * Customer address validation utility\r\n * Checks if customer has complete address information\r\n */\r\n\r\nexport interface CustomerAddressData {\r\n  pincode?: string | null;\r\n  state?: string | null;\r\n  city?: string | null;\r\n  locality?: string | null;\r\n  // address is optional as per requirements\r\n  address?: string | null;\r\n}\r\n\r\nexport interface CustomerProfileData extends CustomerAddressData {\r\n  name?: string | null;\r\n}\r\n\r\n/**\r\n * Validates if customer address is complete\r\n * Address field is optional, but pincode, state, city, and locality are required\r\n */\r\nexport function isCustomerAddressComplete(addressData: CustomerAddressData): boolean {\r\n  const { pincode, state, city, locality } = addressData;\r\n\r\n  // Check if required fields are present and not empty\r\n  return !!(\r\n    pincode && pincode.trim() !== '' &&\r\n    state && state.trim() !== '' &&\r\n    city && city.trim() !== '' &&\r\n    locality && locality.trim() !== ''\r\n  );\r\n}\r\n\r\n/**\r\n * Gets missing address fields for customer\r\n */\r\nexport function getMissingAddressFields(addressData: CustomerAddressData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  if (!addressData.pincode || addressData.pincode.trim() === '') {\r\n    missing.push('pincode');\r\n  }\r\n  if (!addressData.state || addressData.state.trim() === '') {\r\n    missing.push('state');\r\n  }\r\n  if (!addressData.city || addressData.city.trim() === '') {\r\n    missing.push('city');\r\n  }\r\n  if (!addressData.locality || addressData.locality.trim() === '') {\r\n    missing.push('locality');\r\n  }\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing address fields\r\n */\r\nexport function getAddressValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n\r\n/**\r\n * Validates if customer name is complete\r\n */\r\nexport function isCustomerNameComplete(name?: string | null): boolean {\r\n  return !!(name && name.trim() !== '');\r\n}\r\n\r\n/**\r\n * Validates if customer profile is complete (both name and address)\r\n */\r\nexport function isCustomerProfileComplete(profileData: CustomerProfileData): boolean {\r\n  return isCustomerNameComplete(profileData.name) && isCustomerAddressComplete(profileData);\r\n}\r\n\r\n/**\r\n * Gets missing profile fields for customer (name + address)\r\n */\r\nexport function getMissingProfileFields(profileData: CustomerProfileData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  // Check name\r\n  if (!isCustomerNameComplete(profileData.name)) {\r\n    missing.push('name');\r\n  }\r\n\r\n  // Check address fields\r\n  const missingAddressFields = getMissingAddressFields(profileData);\r\n  missing.push(...missingAddressFields);\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing profile fields (name + address)\r\n */\r\nexport function getProfileValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'name': return 'Name';\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAmBM,SAAS,0BAA0B,WAAgC;IACxE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;IAE3C,qDAAqD;IACrD,OAAO,CAAC,CAAC,CACP,WAAW,QAAQ,IAAI,OAAO,MAC9B,SAAS,MAAM,IAAI,OAAO,MAC1B,QAAQ,KAAK,IAAI,OAAO,MACxB,YAAY,SAAS,IAAI,OAAO,EAClC;AACF;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,IAAI,CAAC,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,IAAI,OAAO,IAAI;QAC7D,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,KAAK,CAAC,IAAI,OAAO,IAAI;QACzD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,OAAO,IAAI;QACvD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,IAAI,OAAO,IAAI;QAC/D,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF;AAKO,SAAS,uBAAuB,IAAoB;IACzD,OAAO,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,OAAO,EAAE;AACtC;AAKO,SAAS,0BAA0B,WAAgC;IACxE,OAAO,uBAAuB,YAAY,IAAI,KAAK,0BAA0B;AAC/E;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,aAAa;IACb,IAAI,CAAC,uBAAuB,YAAY,IAAI,GAAG;QAC7C,QAAQ,IAAI,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,uBAAuB,wBAAwB;IACrD,QAAQ,IAAI,IAAI;IAEhB,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/customerProfiles/addressValidation.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { isCustomerAddressComplete, getMissingAddressFields, getAddressValidationMessage, type CustomerAddressData } from \"@/lib/utils/addressValidation\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\n/**\r\n * Checks if customer has complete address information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerAddress(userId: string): Promise<{\r\n  isValid: boolean;\r\n  missingFields?: string[];\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    // Fetch customer address data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer profile for address validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your address information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n      };\r\n    }\r\n    \r\n    const addressData: CustomerAddressData = {\r\n      pincode: profile?.pincode,\r\n      state: profile?.state,\r\n      city: profile?.city,\r\n      locality: profile?.locality,\r\n      address: profile?.address\r\n    };\r\n    \r\n    const isValid = isCustomerAddressComplete(addressData);\r\n    \r\n    if (!isValid) {\r\n      const missingFields = getMissingAddressFields(addressData);\r\n      const message = getAddressValidationMessage(missingFields);\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n      \r\n      return {\r\n        isValid: false,\r\n        missingFields,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n    \r\n    return { isValid: true };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error during address validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your address. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check address and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteAddress(userId: string): Promise<void> {\r\n  const validation = await validateCustomerAddress(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Checks if customer has complete name information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerName(userId: string): Promise<{\r\n  isValid: boolean;\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Fetch customer name data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('name')\r\n      .eq('id', userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error('Error fetching customer profile for name validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your profile information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n      };\r\n    }\r\n\r\n    // Check if name is present and not empty\r\n    const isValid = !!(profile?.name && profile.name.trim() !== '');\r\n\r\n    if (!isValid) {\r\n      const message = 'Please complete your name in your profile to access the dashboard.';\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n\r\n      return {\r\n        isValid: false,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n\r\n  } catch (error) {\r\n    console.error('Unexpected error during name validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your profile. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check name and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteName(userId: string): Promise<void> {\r\n  const validation = await validateCustomerName(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check both address and name, redirect if incomplete\r\n * Use this in customer dashboard pages (except settings page)\r\n * Settings page is exempt from address validation\r\n */\r\nexport async function requireCompleteProfile(userId: string, exemptFromAddressValidation: boolean = false): Promise<void> {\r\n  // Always check name (required for all dashboard access)\r\n  await requireCompleteName(userId);\r\n\r\n  // Only check address if not exempt (settings page is exempt)\r\n  if (!exemptFromAddressValidation) {\r\n    await requireCompleteAddress(userId);\r\n  }\r\n}\r\n\r\n/**\r\n * Get customer address data for forms\r\n */\r\nexport async function getCustomerAddressData(userId: string): Promise<{\r\n  data?: CustomerAddressData;\r\n  error?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer address data:', error);\r\n      return { error: 'Failed to fetch address data' };\r\n    }\r\n    \r\n    return {\r\n      data: {\r\n        pincode: profile?.pincode,\r\n        state: profile?.state,\r\n        city: profile?.city,\r\n        locality: profile?.locality,\r\n        address: profile?.address\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error fetching address data:', error);\r\n    return { error: 'An unexpected error occurred' };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;;;;;;;AAMO,eAAe,wBAAwB,MAAc;IAM1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,8BAA8B;QAC9B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2DAA2D;YACzE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,MAAM,cAAmC;YACvC,SAAS,SAAS;YAClB,OAAO,SAAS;YAChB,MAAM,SAAS;YACf,UAAU,SAAS;YACnB,SAAS,SAAS;QACpB;QAEA,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE;QAE1C,IAAI,CAAC,SAAS;YACZ,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE;YAC9C,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD,EAAE;YAC5C,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,uBAAuB,MAAc;IACzD,MAAM,aAAa,MAAM,wBAAwB;IAEjD,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAMO,eAAe,qBAAqB,MAAc;IAKvD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,2BAA2B;QAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wDAAwD;YACtE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,yCAAyC;QACzC,MAAM,UAAU,CAAC,CAAC,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,OAAO,EAAE;QAE9D,IAAI,CAAC,SAAS;YACZ,MAAM,UAAU;YAChB,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,oBAAoB,MAAc;IACtD,MAAM,aAAa,MAAM,qBAAqB;IAE9C,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAOO,eAAe,uBAAuB,MAAc,EAAE,8BAAuC,KAAK;IACvG,wDAAwD;IACxD,MAAM,oBAAoB;IAE1B,6DAA6D;IAC7D,IAAI,CAAC,6BAA6B;QAChC,MAAM,uBAAuB;IAC/B;AACF;AAKO,eAAe,uBAAuB,MAAc;IAIzD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBAAE,OAAO;YAA+B;QACjD;QAEA,OAAO;YACL,MAAM;gBACJ,SAAS,SAAS;gBAClB,OAAO,SAAS;gBAChB,MAAM,SAAS;gBACf,UAAU,SAAS;gBACnB,SAAS,SAAS;YACpB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;;;IA9LsB;IAiEA;IAYA;IAuDA;IAaA;IAaA;;AA9JA,+OAAA;AAiEA,+OAAA;AAYA,+OAAA;AAuDA,+OAAA;AAaA,+OAAA;AAaA,+OAAA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/constants.ts"], "sourcesContent": ["// lib/supabase/constants.ts\r\n\r\nexport const TABLES = {\r\n  BLOGS: \"blogs\",\r\n  BUSINESS_ACTIVITIES: \"business_activities\",\r\n  BUSINESS_POSTS: \"business_posts\",\r\n  BUSINESS_PROFILES: \"business_profiles\",\r\n  CARD_VISITS: \"card_visits\",\r\n  CUSTOMER_POSTS: \"customer_posts\",\r\n  CUSTOMER_PROFILES: \"customer_profiles\",\r\n  CUSTOMER_PROFILES_PUBLIC: \"customer_profiles_public\",\r\n  LIKES: \"likes\",\r\n  PAYMENT_SUBSCRIPTIONS: \"payment_subscriptions\",\r\n  PINCODES: \"pincodes\",\r\n  PRODUCTS_SERVICES: \"products_services\",\r\n  PRODUCT_VARIANTS: \"product_variants\",\r\n  STORAGE_CLEANUP_CONFIG: \"storage_cleanup_config\",\r\n  STORAGE_CLEANUP_PROGRESS: \"storage_cleanup_progress\",\r\n  SUBSCRIPTIONS: \"subscriptions\",\r\n  SYSTEM_ALERTS: \"system_alerts\",\r\n  RATINGS_REVIEWS: \"ratings_reviews\",\r\n} as const;\r\n\r\nexport const BUCKETS = {\r\n  BUSINESS: \"business\",\r\n  CUSTOMERS: \"customers\",\r\n} as const;\r\n\r\nexport const COLUMNS = {\r\n  ID: \"id\",\r\n  CREATED_AT: \"created_at\",\r\n  UPDATED_AT: \"updated_at\",\r\n  NAME: \"name\",\r\n  EMAIL: \"email\",\r\n  PHONE: \"phone\",\r\n  CITY: \"city\",\r\n  STATE: \"state\",\r\n  PINCODE: \"pincode\",\r\n  PLAN_ID: \"plan_id\",\r\n  LOCALITY: \"locality\",\r\n  CITY_SLUG: \"city_slug\",\r\n  STATE_SLUG: \"state_slug\",\r\n  LOCALITY_SLUG: \"locality_slug\",\r\n  LOGO_URL: \"logo_url\",\r\n  IMAGE_URL: \"image_url\",\r\n  IMAGES: \"images\",\r\n  SLUG: \"slug\",\r\n  STATUS: \"status\",\r\n  CONTENT: \"content\",\r\n  GALLERY: \"gallery\",\r\n  DESCRIPTION: \"description\",\r\n  TITLE: \"title\",\r\n  USER_ID: \"user_id\",\r\n  BUSINESS_ID: \"business_id\",\r\n  CUSTOMER_ID: \"customer_id\",\r\n  BUSINESS_NAME: \"business_name\",\r\n  BUSINESS_SLUG: \"business_slug\",\r\n  PRODUCT_ID: \"product_id\",\r\n  PRODUCT_TYPE: \"product_type\",\r\n  BUSINESS_PROFILE_ID: \"business_profile_id\",\r\n  RAZORPAY_SUBSCRIPTION_ID: \"razorpay_subscription_id\",\r\n  SUBSCRIPTION_STATUS: \"subscription_status\",\r\n  RATING: \"rating\",\r\n  REVIEW_TEXT: \"review_text\",\r\n  AVATAR_URL: \"avatar_url\",\r\n  ADDRESS_LINE: \"address_line\",\r\n} as const;\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;AAErB,MAAM,SAAS;IACpB,OAAO;IACP,qBAAqB;IACrB,gBAAgB;IAChB,mBAAmB;IACnB,aAAa;IACb,gBAAgB;IAChB,mBAAmB;IACnB,0BAA0B;IAC1B,OAAO;IACP,uBAAuB;IACvB,UAAU;IACV,mBAAmB;IACnB,kBAAkB;IAClB,wBAAwB;IACxB,0BAA0B;IAC1B,eAAe;IACf,eAAe;IACf,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,UAAU;IACV,WAAW;AACb;AAEO,MAAM,UAAU;IACrB,IAAI;IACJ,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,SAAS;IACT,UAAU;IACV,WAAW;IACX,YAAY;IACZ,eAAe;IACf,UAAU;IACV,WAAW;IACX,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,SAAS;IACT,aAAa;IACb,aAAa;IACb,eAAe;IACf,eAAe;IACf,YAAY;IACZ,cAAc;IACd,qBAAqB;IACrB,0BAA0B;IAC1B,qBAAqB;IACrB,QAAQ;IACR,aAAa;IACb,YAAY;IACZ,cAAc;AAChB", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/secureCustomerProfiles.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\n// Define types for the customer profile data\r\ntype CustomerProfilePublicData = {\r\n  id: string;\r\n  name: string | null;\r\n  email: string | null;\r\n  avatar_url: string | null;\r\n  created_at: string | null;\r\n  updated_at: string | null;\r\n};\r\n\r\n/**\r\n * Securely fetch a customer profile by ID using the service role key\r\n * This bypasses RLS and ensures sensitive data is not exposed to the client\r\n */\r\nexport async function getSecureCustomerProfileById(\r\n  userId: string\r\n): Promise<{\r\n  data?: CustomerProfilePublicData;\r\n  error?: string;\r\n}> {\r\n  if (!userId) {\r\n    return { error: \"User ID is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // Fetch the customer profile from public view\r\n    const { data, error } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"*\")\r\n      .eq(\"id\", userId)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(\"Secure Fetch Error:\", error);\r\n      return { error: `Failed to fetch customer profile: ${error.message}` };\r\n    }\r\n\r\n    if (!data) {\r\n      return { error: \"Profile not found.\" };\r\n    }\r\n\r\n    // Data from public view is already safe\r\n    const safeData: CustomerProfilePublicData = {\r\n      id: data.id!,\r\n      name: data.name,\r\n      email: null, // Not available in public view\r\n      avatar_url: data.avatar_url,\r\n      created_at: data.created_at,\r\n      updated_at: data.updated_at,\r\n    };\r\n\r\n    return { data: safeData };\r\n  } catch (e) {\r\n    console.error(\"Exception in getSecureCustomerProfileById:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely fetch multiple customer profiles by IDs using the service role key\r\n */\r\nexport async function getSecureCustomerProfilesByIds(\r\n  userIds: string[]\r\n): Promise<{\r\n  data?: CustomerProfilePublicData[];\r\n  error?: string;\r\n}> {\r\n  if (!userIds || userIds.length === 0) {\r\n    return { data: [] };\r\n  }\r\n\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // Fetch the customer profiles from public view\r\n    const { data, error } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"id, name, avatar_url, created_at, updated_at\")\r\n      .in(\"id\", userIds);\r\n\r\n    if (error) {\r\n      console.error(\"Secure Fetch Error:\", error);\r\n      return { error: `Failed to fetch customer profiles: ${error.message}` };\r\n    }\r\n\r\n    // Map to expected format (email not available in public view)\r\n    const safeData = data?.map((profile: any) => ({\r\n      ...profile,\r\n      email: null // Not available in public view\r\n    })) || [];\r\n\r\n    return { data: safeData as CustomerProfilePublicData[] };\r\n  } catch (e) {\r\n    console.error(\"Exception in getSecureCustomerProfilesByIds:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely fetch all customer profiles using the service role key\r\n * This is primarily for testing purposes\r\n */\r\nexport async function getAllSecureCustomerProfiles(): Promise<{\r\n  data?: CustomerProfilePublicData[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // Fetch all customer profiles from public view\r\n    const { data, error } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"id, name, avatar_url, created_at, updated_at\");\r\n\r\n    if (error) {\r\n      console.error(\"Secure Fetch Error:\", error);\r\n      return { error: `Failed to fetch customer profiles: ${error.message}` };\r\n    }\r\n\r\n    // Map to expected format (email not available in public view)\r\n    const safeData = data?.map((profile: any) => ({\r\n      ...profile,\r\n      email: null // Not available in public view\r\n    })) || [];\r\n\r\n    return { data: safeData as CustomerProfilePublicData[] };\r\n  } catch (e) {\r\n    console.error(\"Exception in getAllSecureCustomerProfiles:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely check if a user has access to their own customer profile\r\n * This is used for authenticated users to access their own profile\r\n */\r\nexport async function checkUserCustomerProfileAccess(\r\n  userId: string\r\n): Promise<{\r\n  hasAccess: boolean;\r\n  error?: string;\r\n}> {\r\n  if (!userId) {\r\n    return { hasAccess: false, error: \"User ID is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client for authenticated user\r\n    const supabase = await createClient();\r\n\r\n    // Check if the user is authenticated\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return { hasAccess: false, error: \"User not authenticated.\" };\r\n    }\r\n\r\n    // Verify the requested userId matches the authenticated user\r\n    if (user.id !== userId) {\r\n      return { hasAccess: false, error: \"Unauthorized access attempt.\" };\r\n    }\r\n\r\n    // Check if the user has a customer profile\r\n    const { data, error } = await supabase\r\n      .from(\"customer_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", userId)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(\"Profile Access Check Error:\", error);\r\n      return { hasAccess: false, error: \"Database error checking access.\" };\r\n    }\r\n\r\n    return { hasAccess: !!data };\r\n  } catch (e) {\r\n    console.error(\"Exception in checkUserCustomerProfileAccess:\", e);\r\n    return { hasAccess: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Get user profile data (either business or customer) for reviews\r\n * This function will check both tables and return the appropriate profile data\r\n */\r\nexport async function getUserProfileForReview(\r\n  userId: string\r\n): Promise<{\r\n  data?: {\r\n    id: string;\r\n    name: string | null;\r\n    avatar_url: string | null;\r\n    is_business: boolean;\r\n  };\r\n  error?: string;\r\n}> {\r\n  if (!userId) {\r\n    return { error: \"User ID is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // First check customer_profiles_public\r\n    const { data: customerProfile, error: customerError } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"id, name, avatar_url\")\r\n      .eq(\"id\", userId)\r\n      .maybeSingle();\r\n\r\n    if (customerError) {\r\n      console.error(\"Error fetching customer profile:\", customerError);\r\n      return { error: `Failed to fetch customer profile: ${customerError.message}` };\r\n    }\r\n\r\n    if (customerProfile) {\r\n      return {\r\n        data: {\r\n          id: customerProfile.id!,\r\n          name: customerProfile.name,\r\n          avatar_url: customerProfile.avatar_url,\r\n          is_business: false\r\n        }\r\n      };\r\n    }\r\n\r\n    // If not found in customer_profiles, check business_profiles\r\n    const { data: businessProfile, error: businessError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id, business_name, logo_url\")\r\n      .eq(\"id\", userId)\r\n      .maybeSingle();\r\n\r\n    if (businessError) {\r\n      console.error(\"Error fetching business profile:\", businessError);\r\n      return { error: `Failed to fetch business profile: ${businessError.message}` };\r\n    }\r\n\r\n    if (businessProfile) {\r\n      return {\r\n        data: {\r\n          id: businessProfile.id,\r\n          name: businessProfile.business_name,\r\n          avatar_url: businessProfile.logo_url,\r\n          is_business: true\r\n        }\r\n      };\r\n    }\r\n\r\n    return { error: \"User profile not found in either customer or business profiles.\" };\r\n  } catch (e) {\r\n    console.error(\"Exception in getUserProfileForReview:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Get multiple user profiles (either business or customer) for reviews\r\n */\r\nexport async function getUserProfilesForReviews(\r\n  userIds: string[]\r\n): Promise<{\r\n  data?: {\r\n    [key: string]: {\r\n      id: string;\r\n      name: string | null;\r\n      avatar_url: string | null;\r\n      is_business: boolean;\r\n    }\r\n  };\r\n  error?: string;\r\n}> {\r\n  if (!userIds || userIds.length === 0) {\r\n    return { data: {} };\r\n  }\r\n\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // Fetch customer profiles from public view\r\n    const { data: customerProfiles, error: customerError } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"id, name, avatar_url\")\r\n      .in(\"id\", userIds);\r\n\r\n    if (customerError) {\r\n      console.error(\"Error fetching customer profiles:\", customerError);\r\n      return { error: `Failed to fetch customer profiles: ${customerError.message}` };\r\n    }\r\n\r\n    // Fetch business profiles\r\n    const { data: businessProfiles, error: businessError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id, business_name, logo_url\")\r\n      .in(\"id\", userIds);\r\n\r\n    if (businessError) {\r\n      console.error(\"Error fetching business profiles:\", businessError);\r\n      return { error: `Failed to fetch business profiles: ${businessError.message}` };\r\n    }\r\n\r\n    // Combine the results into a map of user ID to profile data\r\n    const profilesMap: {\r\n      [key: string]: {\r\n        id: string;\r\n        name: string | null;\r\n        avatar_url: string | null;\r\n        is_business: boolean;\r\n      }\r\n    } = {};\r\n\r\n    // Add customer profiles to the map\r\n    customerProfiles?.forEach((profile: any) => {\r\n      profilesMap[profile.id] = {\r\n        id: profile.id,\r\n        name: profile.name,\r\n        avatar_url: profile.avatar_url,\r\n        is_business: false\r\n      };\r\n    });\r\n\r\n    // Add business profiles to the map\r\n    businessProfiles?.forEach((profile: any) => {\r\n      // Only add if not already in the map (customer profiles take precedence)\r\n      if (!profilesMap[profile.id]) {\r\n        profilesMap[profile.id] = {\r\n          id: profile.id,\r\n          name: profile.business_name,\r\n          avatar_url: profile.logo_url,\r\n          is_business: true\r\n        };\r\n      }\r\n    });\r\n\r\n    return { data: profilesMap };\r\n  } catch (e) {\r\n    console.error(\"Exception in getUserProfilesForReviews:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;;;AAgBO,eAAe,6BACpB,MAAc;IAKd,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,OAAO;QAAuB;IACzC;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,8CAA8C;QAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4BACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBAAE,OAAO,CAAC,kCAAkC,EAAE,MAAM,OAAO,EAAE;YAAC;QACvE;QAEA,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,wCAAwC;QACxC,MAAM,WAAsC;YAC1C,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,OAAO;YACP,YAAY,KAAK,UAAU;YAC3B,YAAY,KAAK,UAAU;YAC3B,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO;YAAE,MAAM;QAAS;IAC1B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAKO,eAAe,+BACpB,OAAiB;IAKjB,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;YAAE,MAAM,EAAE;QAAC;IACpB;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,+CAA+C;QAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4BACL,MAAM,CAAC,gDACP,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBAAE,OAAO,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;YAAC;QACxE;QAEA,8DAA8D;QAC9D,MAAM,WAAW,MAAM,IAAI,CAAC,UAAiB,CAAC;gBAC5C,GAAG,OAAO;gBACV,OAAO,KAAK,+BAA+B;YAC7C,CAAC,MAAM,EAAE;QAET,OAAO;YAAE,MAAM;QAAwC;IACzD,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAMO,eAAe;IAIpB,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,+CAA+C;QAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4BACL,MAAM,CAAC;QAEV,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBAAE,OAAO,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;YAAC;QACxE;QAEA,8DAA8D;QAC9D,MAAM,WAAW,MAAM,IAAI,CAAC,UAAiB,CAAC;gBAC5C,GAAG,OAAO;gBACV,OAAO,KAAK,+BAA+B;YAC7C,CAAC,MAAM,EAAE;QAET,OAAO;YAAE,MAAM;QAAwC;IACzD,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAMO,eAAe,+BACpB,MAAc;IAKd,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,WAAW;YAAO,OAAO;QAAuB;IAC3D;IAEA,IAAI;QACF,4CAA4C;QAC5C,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,qCAAqC;QACrC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBAAE,WAAW;gBAAO,OAAO;YAA0B;QAC9D;QAEA,6DAA6D;QAC7D,IAAI,KAAK,EAAE,KAAK,QAAQ;YACtB,OAAO;gBAAE,WAAW;gBAAO,OAAO;YAA+B;QACnE;QAEA,2CAA2C;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBAAE,WAAW;gBAAO,OAAO;YAAkC;QACtE;QAEA,OAAO;YAAE,WAAW,CAAC,CAAC;QAAK;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YAAE,WAAW;YAAO,OAAO;QAAgC;IACpE;AACF;AAMO,eAAe,wBACpB,MAAc;IAUd,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,OAAO;QAAuB;IACzC;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,uCAAuC;QACvC,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAC3D,IAAI,CAAC,4BACL,MAAM,CAAC,wBACP,EAAE,CAAC,MAAM,QACT,WAAW;QAEd,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,OAAO,CAAC,kCAAkC,EAAE,cAAc,OAAO,EAAE;YAAC;QAC/E;QAEA,IAAI,iBAAiB;YACnB,OAAO;gBACL,MAAM;oBACJ,IAAI,gBAAgB,EAAE;oBACtB,MAAM,gBAAgB,IAAI;oBAC1B,YAAY,gBAAgB,UAAU;oBACtC,aAAa;gBACf;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAC3D,IAAI,CAAC,qBACL,MAAM,CAAC,+BACP,EAAE,CAAC,MAAM,QACT,WAAW;QAEd,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,OAAO,CAAC,kCAAkC,EAAE,cAAc,OAAO,EAAE;YAAC;QAC/E;QAEA,IAAI,iBAAiB;YACnB,OAAO;gBACL,MAAM;oBACJ,IAAI,gBAAgB,EAAE;oBACtB,MAAM,gBAAgB,aAAa;oBACnC,YAAY,gBAAgB,QAAQ;oBACpC,aAAa;gBACf;YACF;QACF;QAEA,OAAO;YAAE,OAAO;QAAkE;IACpF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAKO,eAAe,0BACpB,OAAiB;IAYjB,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;YAAE,MAAM,CAAC;QAAE;IACpB;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,2CAA2C;QAC3C,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,4BACL,MAAM,CAAC,wBACP,EAAE,CAAC,MAAM;QAEZ,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBAAE,OAAO,CAAC,mCAAmC,EAAE,cAAc,OAAO,EAAE;YAAC;QAChF;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,qBACL,MAAM,CAAC,+BACP,EAAE,CAAC,MAAM;QAEZ,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBAAE,OAAO,CAAC,mCAAmC,EAAE,cAAc,OAAO,EAAE;YAAC;QAChF;QAEA,4DAA4D;QAC5D,MAAM,cAOF,CAAC;QAEL,mCAAmC;QACnC,kBAAkB,QAAQ,CAAC;YACzB,WAAW,CAAC,QAAQ,EAAE,CAAC,GAAG;gBACxB,IAAI,QAAQ,EAAE;gBACd,MAAM,QAAQ,IAAI;gBAClB,YAAY,QAAQ,UAAU;gBAC9B,aAAa;YACf;QACF;QAEA,mCAAmC;QACnC,kBAAkB,QAAQ,CAAC;YACzB,yEAAyE;YACzE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE;gBAC5B,WAAW,CAAC,QAAQ,EAAE,CAAC,GAAG;oBACxB,IAAI,QAAQ,EAAE;oBACd,MAAM,QAAQ,aAAa;oBAC3B,YAAY,QAAQ,QAAQ;oBAC5B,aAAa;gBACf;YACF;QACF;QAEA,OAAO;YAAE,MAAM;QAAY;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IA5UsB;IAkDA;IA0CA;IAmCA;IAiDA;IA2EA;;AA3PA,+OAAA;AAkDA,+OAAA;AA0CA,+OAAA;AAmCA,+OAAA;AAiDA,+OAAA;AA2EA,+OAAA", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/services/socialService.ts"], "sourcesContent": ["/**\r\n * Unified Social Service for Next.js\r\n * Handles subscriptions, likes, and reviews data fetching and management\r\n * Supports both customer and business contexts with identical backend functionality to React Native\r\n */\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { Database } from '@/types/supabase';\r\nimport { TABLES, COLUMNS } from '@/lib/supabase/constants';\r\nimport { getSecureCustomerProfilesByIds } from '@/lib/actions/secureCustomerProfiles';\r\n\r\nexport interface ActivityMetrics {\r\n  likesCount: number;\r\n  reviewCount: number;\r\n  subscriptionCount: number;\r\n  lastUpdated: string;\r\n}\r\n\r\n// Types for subscriptions\r\nexport interface SubscriptionWithProfile {\r\n  id: string;\r\n  business_profiles: Database['public']['Tables']['business_profiles']['Row'] | null;\r\n}\r\n\r\nexport interface SubscriptionsResult {\r\n  items: SubscriptionWithProfile[];\r\n  totalCount: number;\r\n  hasMore: boolean;\r\n  currentPage: number;\r\n}\r\n\r\n// Types for likes\r\nexport interface LikeWithProfile {\r\n  id: string;\r\n  business_profiles: Database['public']['Tables']['business_profiles']['Row'] | null;\r\n}\r\n\r\nexport interface LikesResult {\r\n  items: LikeWithProfile[];\r\n  totalCount: number;\r\n  hasMore: boolean;\r\n  currentPage: number;\r\n}\r\n\r\n// Types for reviews\r\nexport interface ReviewBusinessProfile {\r\n  id: string;\r\n  business_name: string | null;\r\n  business_slug: string | null;\r\n  logo_url: string | null;\r\n}\r\n\r\nexport interface ReviewData {\r\n  id: string;\r\n  rating: number;\r\n  review_text: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  business_profiles: ReviewBusinessProfile | null;\r\n}\r\n\r\nexport interface ReviewsResult {\r\n  items: ReviewData[];\r\n  totalCount: number;\r\n  hasMore: boolean;\r\n  currentPage: number;\r\n}\r\n\r\n// Business-specific types for received interactions\r\nexport interface BusinessLikeReceived {\r\n  id: string;\r\n  user_id: string;\r\n  customer_profiles?: {\r\n    id: string;\r\n    name: string | null;\r\n    email: string | null;\r\n    avatar_url: string | null;\r\n  } | null;\r\n  business_profiles?: {\r\n    id: string;\r\n    business_name: string | null;\r\n    business_slug: string | null;\r\n    logo_url: string | null;\r\n    city: string | null;\r\n    state: string | null;\r\n    pincode: string | null;\r\n    address_line: string | null;\r\n    locality: string | null;\r\n  } | null;\r\n  profile_type: 'customer' | 'business';\r\n}\r\n\r\nexport interface BusinessLikesReceivedResult {\r\n  items: BusinessLikeReceived[];\r\n  totalCount: number;\r\n  hasMore: boolean;\r\n  currentPage: number;\r\n}\r\n\r\n// Follower types for business\r\nexport interface FollowerProfileData {\r\n  id: string;\r\n  name: string | null;\r\n  slug: string | null;\r\n  logo_url?: string | null;\r\n  avatar_url?: string | null;\r\n  city: string | null;\r\n  state: string | null;\r\n  pincode: string | null;\r\n  address_line: string | null;\r\n  type: \"business\" | \"customer\";\r\n}\r\n\r\nexport interface FollowerWithProfile {\r\n  id: string;\r\n  profile: FollowerProfileData | null;\r\n}\r\n\r\nexport interface FollowersResult {\r\n  items: FollowerWithProfile[];\r\n  totalCount: number;\r\n  hasMore: boolean;\r\n  currentPage: number;\r\n}\r\n\r\n/**\r\n * Subscriptions Service\r\n */\r\nexport const subscriptionsService = {\r\n  /**\r\n   * Fetch user subscriptions with pagination and search\r\n   */\r\n  async fetchSubscriptions(\r\n    userId: string,\r\n    page: number = 1,\r\n    limit: number = 20,\r\n    searchTerm: string = \"\"\r\n  ): Promise<SubscriptionsResult> {\r\n    try {\r\n      const supabase = await createClient();\r\n\r\n      // Get total count first with separate query to avoid count issues with joins\r\n      let countQuery = supabase\r\n        .from(TABLES.SUBSCRIPTIONS)\r\n        .select(\r\n          `\r\n          ${COLUMNS.ID},\r\n          ${TABLES.BUSINESS_PROFILES}!inner (\r\n            ${COLUMNS.ID},\r\n            ${COLUMNS.BUSINESS_NAME}\r\n          )\r\n        `,\r\n          { count: \"exact\", head: true }\r\n        )\r\n        .eq(COLUMNS.USER_ID, userId);\r\n\r\n      // Apply search filter to count query if provided\r\n      if (searchTerm && searchTerm.trim()) {\r\n        countQuery = countQuery.ilike(\r\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\r\n          `%${searchTerm.trim()}%`\r\n        );\r\n      }\r\n\r\n      const { count: totalCount, error: countError } = await countQuery;\r\n\r\n      if (countError) {\r\n        throw new Error(`Failed to get subscriptions count: ${countError.message}`);\r\n      }\r\n\r\n      // If no subscriptions, return empty result\r\n      if (!totalCount || totalCount === 0) {\r\n        return {\r\n          items: [],\r\n          totalCount: 0,\r\n          hasMore: false,\r\n          currentPage: page,\r\n        };\r\n      }\r\n\r\n      // Build the main query for fetching data\r\n      let query = supabase\r\n        .from(TABLES.SUBSCRIPTIONS)\r\n        .select(\r\n          `\r\n          ${COLUMNS.ID},\r\n          ${COLUMNS.BUSINESS_PROFILE_ID},\r\n          ${TABLES.BUSINESS_PROFILES}!inner (*)\r\n        `\r\n        )\r\n        .eq(COLUMNS.USER_ID, userId);\r\n\r\n      // Apply search filter if provided\r\n      if (searchTerm && searchTerm.trim()) {\r\n        query = query.ilike(\r\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\r\n          `%${searchTerm.trim()}%`\r\n        );\r\n      }\r\n\r\n      // Apply pagination\r\n      const offset = (page - 1) * limit;\r\n      \r\n      query = query.range(offset, offset + limit - 1);\r\n\r\n      const { data: subscriptionsWithProfiles, error } = await query;\r\n      \r\n\r\n      if (error) {\r\n        throw new Error(`Failed to fetch subscriptions: ${error.message}`);\r\n      }\r\n\r\n      // Transform the data\r\n      const transformedSubscriptions: SubscriptionWithProfile[] = (\r\n        subscriptionsWithProfiles || []\r\n      ).map(\r\n        (sub: {\r\n          id: string;\r\n          business_profiles: Database['public']['Tables']['business_profiles']['Row'] | Database['public']['Tables']['business_profiles']['Row'][];\r\n        }) => ({\r\n          id: sub.id,\r\n          business_profiles: Array.isArray(sub.business_profiles)\r\n            ? sub.business_profiles[0]\r\n            : sub.business_profiles,\r\n        })\r\n      );\r\n\r\n      const hasMore = totalCount > offset + limit;\r\n\r\n      return {\r\n        items: transformedSubscriptions,\r\n        totalCount,\r\n        hasMore,\r\n        currentPage: page,\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in fetchSubscriptions:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Unsubscribe from a business\r\n   */\r\n  async unsubscribe(subscriptionId: string): Promise<void> {\r\n    try {\r\n      const supabase = await createClient();\r\n      const { error } = await supabase\r\n        .from(TABLES.SUBSCRIPTIONS)\r\n        .delete()\r\n        .eq(COLUMNS.ID, subscriptionId);\r\n\r\n      if (error) {\r\n        throw new Error(`Failed to unsubscribe: ${error.message}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in unsubscribe:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Fetch followers to a business (both customers and other businesses)\r\n   */\r\n  async fetchBusinessFollowers(\r\n    businessId: string,\r\n    page: number = 1,\r\n    limit: number = 10\r\n  ): Promise<FollowersResult> {\r\n    try {\r\n      const supabase = await createClient();\r\n      \r\n      // Calculate offset for pagination\r\n      const offset = (page - 1) * limit;\r\n\r\n      // Get total count first\r\n      const { count: totalCount, error: countError } = await supabase\r\n        .from(TABLES.SUBSCRIPTIONS)\r\n        .select(\"*\", { count: \"exact\", head: true })\r\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);\r\n\r\n      if (countError) {\r\n        throw new Error(\"Failed to fetch subscription count\");\r\n      }\r\n\r\n      if (!totalCount || totalCount === 0) {\r\n        return {\r\n          items: [],\r\n          totalCount: 0,\r\n          hasMore: false,\r\n          currentPage: page,\r\n        };\r\n      }\r\n\r\n      // Get paginated subscriptions (database-level pagination)\r\n      const { data: subscriptions, error: subsError } = await supabase\r\n        .from(TABLES.SUBSCRIPTIONS)\r\n        .select(`${COLUMNS.ID}, ${COLUMNS.USER_ID}, ${COLUMNS.CREATED_AT}`)\r\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)\r\n        .order(COLUMNS.CREATED_AT, { ascending: false })\r\n        .range(offset, offset + limit - 1);\r\n\r\n      if (subsError) {\r\n        throw new Error(\"Failed to fetch subscriptions\");\r\n      }\r\n\r\n      if (!subscriptions || subscriptions.length === 0) {\r\n        return {\r\n          items: [],\r\n          totalCount: totalCount || 0,\r\n          hasMore: false,\r\n          currentPage: page,\r\n        };\r\n      }\r\n\r\n      // Get user IDs from the paginated subscriptions only\r\n      const userIds = subscriptions.map((sub) => sub.user_id);\r\n\r\n      // Fetch profiles for only the paginated user IDs (not all users)\r\n      const [customerProfilesResult, businessProfiles] = await Promise.all([\r\n        getSecureCustomerProfilesByIds(userIds),\r\n        supabase\r\n          .from(TABLES.BUSINESS_PROFILES)\r\n          .select(\r\n            `${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}`\r\n          )\r\n          .in(COLUMNS.ID, userIds),\r\n      ]);\r\n\r\n      if (customerProfilesResult.error) {\r\n        throw new Error(\"Failed to fetch customer profiles\");\r\n      }\r\n\r\n      if (businessProfiles.error) {\r\n        throw new Error(\"Failed to fetch business profiles\");\r\n      }\r\n\r\n      // Create profile maps for efficient lookup\r\n      const customerMap = new Map(\r\n        customerProfilesResult.data?.map((p) => [p.id, p]) || []\r\n      );\r\n      const businessMap = new Map(\r\n        businessProfiles.data?.map((p) => [p.id, p]) || []\r\n      );\r\n\r\n      // Transform subscriptions to include profile data\r\n      const allFollowers: FollowerWithProfile[] = subscriptions\r\n        .map((sub) => {\r\n          const customerProfile = customerMap.get(sub.user_id);\r\n          const businessProfile = businessMap.get(sub.user_id);\r\n\r\n          if (customerProfile) {\r\n            return {\r\n              id: sub.id,\r\n              profile: {\r\n                id: customerProfile.id,\r\n                name: customerProfile.name,\r\n                slug: null,\r\n                avatar_url: customerProfile.avatar_url,\r\n                city: null,\r\n                state: null,\r\n                pincode: null,\r\n                address_line: null,\r\n                type: \"customer\" as const,\r\n              },\r\n            };\r\n          } else if (businessProfile) {\r\n            return {\r\n              id: sub.id,\r\n              profile: {\r\n                id: businessProfile.id,\r\n                name: businessProfile.business_name,\r\n                slug: businessProfile.business_slug,\r\n                logo_url: businessProfile.logo_url,\r\n                city: businessProfile.city,\r\n                state: businessProfile.state,\r\n                pincode: businessProfile.pincode,\r\n                address_line: businessProfile.address_line,\r\n                type: \"business\" as const,\r\n              },\r\n            };\r\n          }\r\n          return null;\r\n        })\r\n        .filter((sub) => sub !== null) as FollowerWithProfile[];\r\n\r\n      // Calculate hasMore based on database-level pagination\r\n      const hasMore = totalCount > offset + limit;\r\n\r\n      return {\r\n        items: allFollowers,\r\n        totalCount: totalCount || 0,\r\n        hasMore,\r\n        currentPage: page,\r\n      };\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\n/**\r\n * Likes Service\r\n */\r\nexport const likesService = {\r\n  /**\r\n   * Fetch user likes with pagination and search\r\n   */\r\n  async fetchLikes(\r\n    userId: string,\r\n    page: number = 1,\r\n    limit: number = 20,\r\n    searchTerm: string = \"\"\r\n  ): Promise<LikesResult> {\r\n    try {\r\n      const supabase = await createClient();\r\n\r\n      // Build query with proper joins and filtering\r\n      let query = supabase\r\n        .from(TABLES.LIKES)\r\n        .select(\r\n          `\r\n          ${COLUMNS.ID},\r\n          ${TABLES.BUSINESS_PROFILES}!inner (*)\r\n        `\r\n        )\r\n        .eq(COLUMNS.USER_ID, userId);\r\n\r\n      // Apply search filter if provided\r\n      if (searchTerm && searchTerm.trim()) {\r\n        query = query.ilike(\r\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\r\n          `%${searchTerm.trim()}%`\r\n        );\r\n      }\r\n\r\n      // Get total count for pagination\r\n      let countQuery = supabase\r\n        .from(TABLES.LIKES)\r\n        .select(\r\n          `\r\n          ${COLUMNS.ID},\r\n          ${TABLES.BUSINESS_PROFILES}!inner (\r\n            ${COLUMNS.ID},\r\n            ${COLUMNS.BUSINESS_NAME}\r\n          )\r\n        `,\r\n          { count: \"exact\", head: true }\r\n        )\r\n        .eq(COLUMNS.USER_ID, userId);\r\n\r\n      if (searchTerm && searchTerm.trim()) {\r\n        countQuery = countQuery.ilike(\r\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\r\n          `%${searchTerm.trim()}%`\r\n        );\r\n      }\r\n\r\n      const { count: totalCount, error: countError } = await countQuery;\r\n\r\n      if (countError) {\r\n        throw new Error(`Failed to get likes count: ${countError.message}`);\r\n      }\r\n\r\n      // If no likes, return empty result\r\n      if (!totalCount || totalCount === 0) {\r\n        return {\r\n          items: [],\r\n          totalCount: 0,\r\n          hasMore: false,\r\n          currentPage: page,\r\n        };\r\n      }\r\n\r\n      // Apply pagination to the main query\r\n      const offset = (page - 1) * limit;\r\n      \r\n      query = query.range(offset, offset + limit - 1);\r\n\r\n      const { data: likesWithProfiles, error } = await query;\r\n      \r\n\r\n      if (error) {\r\n        throw new Error(`Failed to fetch likes: ${error.message}`);\r\n      }\r\n\r\n      // Transform the data\r\n      const transformedLikes: LikeWithProfile[] = (likesWithProfiles || []).map(\r\n        (like: {\r\n          id: string;\r\n          business_profiles: Database['public']['Tables']['business_profiles']['Row'] | Database['public']['Tables']['business_profiles']['Row'][];\r\n        }) => ({\r\n          id: like.id,\r\n          business_profiles: Array.isArray(like.business_profiles)\r\n            ? like.business_profiles[0]\r\n            : like.business_profiles,\r\n        })\r\n      );\r\n\r\n      const hasMore = totalCount > offset + limit;\r\n\r\n      return {\r\n        items: transformedLikes,\r\n        totalCount,\r\n        hasMore,\r\n        currentPage: page,\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in fetchLikes:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Unlike a business\r\n   */\r\n  async unlike(likeId: string): Promise<void> {\r\n    try {\r\n      const supabase = await createClient();\r\n      const { error } = await supabase.from(TABLES.LIKES).delete().eq(COLUMNS.ID, likeId);\r\n\r\n      if (error) {\r\n        throw new Error(`Failed to unlike: ${error.message}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in unlike:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Fetch likes received by a business (customers/businesses who liked this business)\r\n   */\r\n  async fetchBusinessLikesReceived(\r\n    businessId: string,\r\n    page: number = 1,\r\n    limit: number = 10\r\n  ): Promise<BusinessLikesReceivedResult> {\r\n    try {\r\n      const supabase = await createClient();\r\n\r\n      // Get total count first\r\n      const { count: totalCount, error: countError } = await supabase\r\n        .from(TABLES.LIKES)\r\n        .select(COLUMNS.ID, { count: \"exact\", head: true })\r\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);\r\n\r\n      if (countError) {\r\n        throw new Error(\"Failed to get total count\");\r\n      }\r\n\r\n      if (!totalCount || totalCount === 0) {\r\n        return {\r\n          items: [],\r\n          totalCount: 0,\r\n          hasMore: false,\r\n          currentPage: page,\r\n        };\r\n      }\r\n\r\n      // Get likes with pagination (database-level pagination)\r\n      const from = (page - 1) * limit;\r\n      \r\n      const { data: likes, error: likesError } = await supabase\r\n        .from(TABLES.LIKES)\r\n        .select(`${COLUMNS.ID}, ${COLUMNS.USER_ID}`)\r\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)\r\n        .order(COLUMNS.CREATED_AT, { ascending: false })\r\n        .range(from, from + limit - 1);\r\n      \r\n\r\n      if (likesError) {\r\n        throw new Error(\"Failed to fetch likes\");\r\n      }\r\n\r\n      if (!likes || likes.length === 0) {\r\n        return {\r\n          items: [],\r\n          totalCount,\r\n          hasMore: false,\r\n          currentPage: page,\r\n        };\r\n      }\r\n\r\n      // Get user IDs for the paginated results only\r\n      const userIds = likes.map((like) => like.user_id);\r\n\r\n      // Fetch customer and business profiles for paginated results only\r\n      \r\n      const [customerProfilesResult, businessProfiles] = await Promise.all([\r\n        getSecureCustomerProfilesByIds(userIds),\r\n        supabase\r\n          .from(TABLES.BUSINESS_PROFILES)\r\n          .select(\r\n            `${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}, ${COLUMNS.LOCALITY}`\r\n          )\r\n          .in(COLUMNS.ID, userIds),\r\n      ]);\r\n\r\n      \r\n      \r\n\r\n      // Create maps for easy lookup\r\n      const customerProfilesMap = new Map(\r\n        customerProfilesResult.data?.map((profile) => [profile.id, profile]) || []\r\n      );\r\n      const businessProfilesMap = new Map(\r\n        businessProfiles.data?.map((profile) => [profile.id, profile]) || []\r\n      );\r\n\r\n      // Combine likes with their corresponding profiles\r\n      \r\n      const processedLikes = likes\r\n        .map((like) => {\r\n          const customerProfile = customerProfilesMap.get(like.user_id);\r\n          const businessProfile = businessProfilesMap.get(like.user_id);\r\n\r\n          if (customerProfile) {\r\n            return {\r\n              id: like.id,\r\n              user_id: like.user_id,\r\n              customer_profiles: customerProfile,\r\n              profile_type: \"customer\" as const,\r\n            };\r\n          } else if (businessProfile) {\r\n            return {\r\n              id: like.id,\r\n              user_id: like.user_id,\r\n              business_profiles: businessProfile,\r\n              profile_type: \"business\" as const,\r\n            };\r\n          }\r\n\r\n          return null;\r\n        })\r\n        .filter((item) => item !== null) as BusinessLikeReceived[];\r\n\r\n      \r\n\r\n      const hasMore = totalCount > from + limit;\r\n\r\n      return {\r\n        items: processedLikes,\r\n        totalCount,\r\n        hasMore,\r\n        currentPage: page,\r\n      };\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\n/**\r\n * Reviews Service\r\n */\r\nexport const reviewsService = {\r\n  /**\r\n   * Fetch user reviews with pagination and sorting\r\n   */\r\n  async fetchReviews(\r\n    userId: string,\r\n    page: number = 1,\r\n    limit: number = 20,\r\n    sortBy: \"newest\" | \"oldest\" | \"rating_high\" | \"rating_low\" = \"newest\",\r\n    searchTerm: string = \"\"\r\n  ): Promise<ReviewsResult> {\r\n    try {\r\n      const supabase = await createClient();\r\n\r\n      // Get total count first with separate query to avoid count issues with joins\r\n      let countResult;\r\n\r\n      if (searchTerm && searchTerm.trim()) {\r\n        // For search, we need to join with business_profiles\r\n        countResult = await supabase\r\n          .from(TABLES.RATINGS_REVIEWS)\r\n          .select(\r\n            `\r\n            ${COLUMNS.ID},\r\n            ${TABLES.BUSINESS_PROFILES}!inner (\r\n              ${COLUMNS.ID},\r\n              ${COLUMNS.BUSINESS_NAME}\r\n            )\r\n          `,\r\n            { count: \"exact\", head: true }\r\n          )\r\n          .eq(COLUMNS.USER_ID, userId)\r\n          .ilike(\r\n            `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\r\n            `%${searchTerm.trim()}%`\r\n          );\r\n      } else {\r\n        // For simple count without search, no need to join\r\n        countResult = await supabase\r\n          .from(TABLES.RATINGS_REVIEWS)\r\n          .select(COLUMNS.ID, { count: \"exact\", head: true })\r\n          .eq(COLUMNS.USER_ID, userId);\r\n      }\r\n\r\n      const { count: totalCount, error: countError } = countResult;\r\n\r\n      if (countError) {\r\n        throw new Error(`Failed to get reviews count: ${countError.message}`);\r\n      }\r\n\r\n      // If no reviews, return empty result\r\n      if (!totalCount || totalCount === 0) {\r\n        return {\r\n          items: [],\r\n          totalCount: 0,\r\n          hasMore: false,\r\n          currentPage: page,\r\n        };\r\n      }\r\n\r\n      // Build the main query for fetching data\r\n      let query = supabase\r\n        .from(TABLES.RATINGS_REVIEWS)\r\n        .select(\r\n          `\r\n          ${COLUMNS.ID},\r\n          ${COLUMNS.RATING},\r\n          ${COLUMNS.REVIEW_TEXT},\r\n          ${COLUMNS.CREATED_AT},\r\n          ${COLUMNS.UPDATED_AT},\r\n          ${COLUMNS.BUSINESS_PROFILE_ID},\r\n          ${COLUMNS.USER_ID},\r\n          ${TABLES.BUSINESS_PROFILES}!inner (\r\n            ${COLUMNS.ID},\r\n            ${COLUMNS.BUSINESS_NAME},\r\n            ${COLUMNS.BUSINESS_SLUG},\r\n            ${COLUMNS.LOGO_URL}\r\n          )\r\n        `\r\n        )\r\n        .eq(COLUMNS.USER_ID, userId);\r\n\r\n      // Apply search filter if provided\r\n      if (searchTerm && searchTerm.trim()) {\r\n        query = query.ilike(\r\n          `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,\r\n          `%${searchTerm.trim()}%`\r\n        );\r\n      }\r\n\r\n      // Apply sorting\r\n      switch (sortBy) {\r\n        case \"oldest\":\r\n          query = query.order(COLUMNS.CREATED_AT, { ascending: true });\r\n          break;\r\n        case \"rating_high\":\r\n          query = query.order(COLUMNS.RATING, { ascending: false });\r\n          break;\r\n        case \"rating_low\":\r\n          query = query.order(COLUMNS.RATING, { ascending: true });\r\n          break;\r\n        case \"newest\":\r\n        default:\r\n          query = query.order(COLUMNS.CREATED_AT, { ascending: false });\r\n          break;\r\n      }\r\n\r\n      // Apply pagination\r\n      const offset = (page - 1) * limit;\r\n      query = query.range(offset, offset + limit - 1);\r\n\r\n      const { data: reviews, error } = await query;\r\n\r\n      if (error) {\r\n        throw new Error(`Failed to fetch reviews: ${error.message}`);\r\n      }\r\n\r\n      // Transform the data (business profiles are already joined)\r\n      const transformedReviews: ReviewData[] = (reviews || []).map((review: {\r\n        id: string;\r\n        rating: number;\r\n        review_text: string | null;\r\n        created_at: string;\r\n        updated_at: string;\r\n        business_profile_id: string;\r\n        user_id: string;\r\n        business_profiles: ReviewBusinessProfile | ReviewBusinessProfile[];\r\n      }) => ({\r\n        id: review.id,\r\n        rating: review.rating,\r\n        review_text: review.review_text,\r\n        created_at: review.created_at,\r\n        updated_at: review.updated_at,\r\n        business_profile_id: review.business_profile_id,\r\n        user_id: review.user_id,\r\n        business_profiles: Array.isArray(review.business_profiles)\r\n          ? review.business_profiles[0]\r\n          : review.business_profiles,\r\n      }));\r\n\r\n      const hasMore = totalCount > offset + limit;\r\n\r\n      return {\r\n        items: transformedReviews,\r\n        totalCount,\r\n        hasMore,\r\n        currentPage: page,\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in fetchReviews:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a review\r\n   */\r\n  async deleteReview(reviewId: string): Promise<void> {\r\n    try {\r\n      const supabase = await createClient();\r\n      const { error } = await supabase\r\n        .from(TABLES.RATINGS_REVIEWS)\r\n        .delete()\r\n        .eq(COLUMNS.ID, reviewId);\r\n\r\n      if (error) {\r\n        throw new Error(`Failed to delete review: ${error.message}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in deleteReview:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update a review\r\n   */\r\n  async updateReview(\r\n    reviewId: string,\r\n    rating: number,\r\n    reviewText: string\r\n  ): Promise<void> {\r\n    try {\r\n      const supabase = await createClient();\r\n      const { error } = await supabase\r\n        .from(TABLES.RATINGS_REVIEWS)\r\n        .update({\r\n          [COLUMNS.RATING]: rating,\r\n          [COLUMNS.REVIEW_TEXT]: reviewText || null,\r\n          [COLUMNS.UPDATED_AT]: new Date().toISOString(),\r\n        })\r\n        .eq(COLUMNS.ID, reviewId);\r\n\r\n      if (error) {\r\n        throw new Error(`Failed to update review: ${error.message}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in updateReview:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Fetch reviews received by a business (customers/businesses who reviewed this business)\r\n   */\r\n  async fetchBusinessReviewsReceived(\r\n    businessId: string,\r\n    page: number = 1,\r\n    limit: number = 10,\r\n    sortBy: \"newest\" | \"oldest\" | \"rating_high\" | \"rating_low\" = \"newest\"\r\n  ): Promise<{\r\n    items: {\r\n      id: string;\r\n      rating: number;\r\n      review_text: string | null;\r\n      created_at: string;\r\n      updated_at: string;\r\n      business_profile_id: string;\r\n      user_id: string;\r\n      customer_profiles?: {\r\n        id: string;\r\n        name: string | null;\r\n        email: string | null;\r\n        avatar_url: string | null;\r\n      } | null;\r\n      business_profiles?: {\r\n        id: string;\r\n        business_name: string | null;\r\n        business_slug: string | null;\r\n        logo_url: string | null;\r\n        city: string | null;\r\n        state: string | null;\r\n        pincode: string | null;\r\n        address_line: string | null;\r\n      } | null;\r\n      profile_type: \"customer\" | \"business\";\r\n    }[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    currentPage: number;\r\n  }> {\r\n    try {\r\n      const supabase = await createClient();\r\n\r\n      // Get total count first\r\n      const { count: totalCount, error: countError } = await supabase\r\n        .from(TABLES.RATINGS_REVIEWS)\r\n        .select(COLUMNS.ID, { count: \"exact\", head: true })\r\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);\r\n\r\n      if (countError) {\r\n        throw new Error(\"Failed to get total count\");\r\n      }\r\n\r\n      if (!totalCount || totalCount === 0) {\r\n        return {\r\n          items: [],\r\n          totalCount: 0,\r\n          hasMore: false,\r\n          currentPage: page,\r\n        };\r\n      }\r\n\r\n      // Get reviews with pagination and sorting\r\n      const from = (page - 1) * limit;\r\n      let query = supabase\r\n        .from(TABLES.RATINGS_REVIEWS)\r\n        .select(`${COLUMNS.ID}, ${COLUMNS.RATING}, ${COLUMNS.REVIEW_TEXT}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.BUSINESS_PROFILE_ID}, ${COLUMNS.USER_ID}`)\r\n        .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);\r\n\r\n      // Apply sorting\r\n      switch (sortBy) {\r\n        case \"oldest\":\r\n          query = query.order(COLUMNS.CREATED_AT, { ascending: true });\r\n          break;\r\n        case \"rating_high\":\r\n          query = query.order(COLUMNS.RATING, { ascending: false });\r\n          break;\r\n        case \"rating_low\":\r\n          query = query.order(COLUMNS.RATING, { ascending: true });\r\n          break;\r\n        case \"newest\":\r\n        default:\r\n          query = query.order(COLUMNS.CREATED_AT, { ascending: false });\r\n          break;\r\n      }\r\n\r\n      query = query.range(from, from + limit - 1);\r\n\r\n      const { data: reviews, error: reviewsError } = await query;\r\n\r\n      if (reviewsError) {\r\n        throw new Error(\"Failed to fetch reviews\");\r\n      }\r\n\r\n      if (!reviews || reviews.length === 0) {\r\n        return {\r\n          items: [],\r\n          totalCount,\r\n          hasMore: false,\r\n          currentPage: page,\r\n        };\r\n      }\r\n\r\n      // Get user IDs for the paginated results only\r\n      const userIds = reviews.map((review) => review.user_id);\r\n\r\n      // Fetch customer and business profiles for paginated results only\r\n      const [customerProfilesResult, businessProfiles] = await Promise.all([\r\n        getSecureCustomerProfilesByIds(userIds),\r\n        supabase\r\n          .from(TABLES.BUSINESS_PROFILES)\r\n          .select(\r\n            `${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}`\r\n          )\r\n          .in(COLUMNS.ID, userIds),\r\n      ]);\r\n\r\n      // Create maps for easy lookup\r\n      const customerProfilesMap = new Map(\r\n        customerProfilesResult.data?.map((profile) => [profile.id, profile]) || []\r\n      );\r\n      const businessProfilesMap = new Map(\r\n        businessProfiles.data?.map((profile) => [profile.id, profile]) || []\r\n      );\r\n\r\n      // Combine reviews with their corresponding profiles\r\n      const processedReviews = reviews\r\n        .map((review) => {\r\n          const customerProfile = customerProfilesMap.get(review.user_id);\r\n          const businessProfile = businessProfilesMap.get(review.user_id);\r\n\r\n          if (customerProfile) {\r\n            return {\r\n              id: review.id,\r\n              rating: review.rating,\r\n              review_text: review.review_text,\r\n              created_at: review.created_at,\r\n              updated_at: review.updated_at,\r\n              business_profile_id: review.business_profile_id,\r\n              user_id: review.user_id,\r\n              customer_profiles: customerProfile,\r\n              profile_type: \"customer\" as const,\r\n            };\r\n          } else if (businessProfile) {\r\n            return {\r\n              id: review.id,\r\n              rating: review.rating,\r\n              review_text: review.review_text,\r\n              created_at: review.created_at,\r\n              updated_at: review.updated_at,\r\n              business_profile_id: review.business_profile_id,\r\n              user_id: review.user_id,\r\n              business_profiles: businessProfile,\r\n              profile_type: \"business\" as const,\r\n            };\r\n          }\r\n          return null;\r\n        })\r\n        .filter((item) => item !== null);\r\n\r\n      const hasMore = totalCount > from + limit;\r\n\r\n      return {\r\n        items: processedReviews,\r\n        totalCount,\r\n        hasMore,\r\n        currentPage: page,\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in fetchBusinessReviewsReceived:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\n/**\r\n * Get customer activity metrics\r\n */\r\nexport async function getActivityMetrics(\r\n  userId: string\r\n): Promise<ActivityMetrics | null> {\r\n  try {\r\n    const supabase = await createClient();\r\n\r\n    // Fetch fresh metrics from database\r\n    const [likesResult, reviewsResult, subscriptionsResult] = await Promise.all(\r\n      [\r\n        supabase\r\n          .from(TABLES.LIKES)\r\n          .select(COLUMNS.ID, { count: \"exact\" })\r\n          .eq(COLUMNS.USER_ID, userId),\r\n        supabase\r\n          .from(TABLES.RATINGS_REVIEWS)\r\n          .select(COLUMNS.ID, { count: \"exact\" })\r\n          .eq(COLUMNS.USER_ID, userId),\r\n        supabase\r\n          .from(TABLES.SUBSCRIPTIONS)\r\n          .select(COLUMNS.ID, { count: \"exact\" })\r\n          .eq(COLUMNS.USER_ID, userId),\r\n      ]\r\n    );\r\n\r\n    const metrics: ActivityMetrics = {\r\n      likesCount: likesResult.count || 0,\r\n      reviewCount: reviewsResult.count || 0,\r\n      subscriptionCount: subscriptionsResult.count || 0,\r\n      lastUpdated: new Date().toISOString(),\r\n    };\r\n\r\n    return metrics;\r\n  } catch (error) {\r\n    console.error(\"Error fetching activity metrics:\", error);\r\n    return null;\r\n  }\r\n}\r\n\r\n// Export a service object for compatibility with existing imports\r\nexport const socialService = {\r\n  likesService,\r\n  reviewsService,\r\n  subscriptionsService,\r\n  fetchActivityMetrics: getActivityMetrics,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;AAED;AAEA;AACA;;;;AAyHO,MAAM,uBAAuB;IAClC;;GAEC,GACD,MAAM,oBACJ,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,aAAqB,EAAE;QAEvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,6EAA6E;YAC7E,IAAI,aAAa,SACd,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;YACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;YACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;;QAE5B,CAAC,EACC;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAE9B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,iDAAiD;YACjD,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,aAAa,WAAW,KAAK,CAC3B,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;YAEvD,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,WAAW,OAAO,EAAE;YAC5E;YAEA,2CAA2C;YAC3C,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,yCAAyC;YACzC,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;UAC9B,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;QAC7B,CAAC,EAEA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,kCAAkC;YAClC,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,QAAQ,MAAM,KAAK,CACjB,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,mBAAmB;YACnB,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAE5B,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAE7C,MAAM,EAAE,MAAM,yBAAyB,EAAE,KAAK,EAAE,GAAG,MAAM;YAGzD,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,MAAM,OAAO,EAAE;YACnE;YAEA,qBAAqB;YACrB,MAAM,2BAAsD,CAC1D,6BAA6B,EAAE,AACjC,EAAE,GAAG,CACH,CAAC,MAGK,CAAC;oBACL,IAAI,IAAI,EAAE;oBACV,mBAAmB,MAAM,OAAO,CAAC,IAAI,iBAAiB,IAClD,IAAI,iBAAiB,CAAC,EAAE,GACxB,IAAI,iBAAiB;gBAC3B,CAAC;YAGH,MAAM,UAAU,aAAa,SAAS;YAEtC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAY,cAAsB;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,GACN,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAElB,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YAC3D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,wBACJ,UAAkB,EAClB,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,kCAAkC;YAClC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAE5B,wBAAwB;YACxB,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE;YAEnC,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,0DAA0D;YAC1D,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EACjE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,YAChC,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAM,GAC7C,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAElC,IAAI,WAAW;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;gBAChD,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY,cAAc;oBAC1B,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,qDAAqD;YACrD,MAAM,UAAU,cAAc,GAAG,CAAC,CAAC,MAAQ,IAAI,OAAO;YAEtD,iEAAiE;YACjE,MAAM,CAAC,wBAAwB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnE,CAAA,GAAA,wIAAA,CAAA,iCAA8B,AAAD,EAAE;gBAC/B,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE,EAEzK,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;aACnB;YAED,IAAI,uBAAuB,KAAK,EAAE;gBAChC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,iBAAiB,KAAK,EAAE;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,2CAA2C;YAC3C,MAAM,cAAc,IAAI,IACtB,uBAAuB,IAAI,EAAE,IAAI,CAAC,IAAM;oBAAC,EAAE,EAAE;oBAAE;iBAAE,KAAK,EAAE;YAE1D,MAAM,cAAc,IAAI,IACtB,iBAAiB,IAAI,EAAE,IAAI,CAAC,IAAM;oBAAC,EAAE,EAAE;oBAAE;iBAAE,KAAK,EAAE;YAGpD,kDAAkD;YAClD,MAAM,eAAsC,cACzC,GAAG,CAAC,CAAC;gBACJ,MAAM,kBAAkB,YAAY,GAAG,CAAC,IAAI,OAAO;gBACnD,MAAM,kBAAkB,YAAY,GAAG,CAAC,IAAI,OAAO;gBAEnD,IAAI,iBAAiB;oBACnB,OAAO;wBACL,IAAI,IAAI,EAAE;wBACV,SAAS;4BACP,IAAI,gBAAgB,EAAE;4BACtB,MAAM,gBAAgB,IAAI;4BAC1B,MAAM;4BACN,YAAY,gBAAgB,UAAU;4BACtC,MAAM;4BACN,OAAO;4BACP,SAAS;4BACT,cAAc;4BACd,MAAM;wBACR;oBACF;gBACF,OAAO,IAAI,iBAAiB;oBAC1B,OAAO;wBACL,IAAI,IAAI,EAAE;wBACV,SAAS;4BACP,IAAI,gBAAgB,EAAE;4BACtB,MAAM,gBAAgB,aAAa;4BACnC,MAAM,gBAAgB,aAAa;4BACnC,UAAU,gBAAgB,QAAQ;4BAClC,MAAM,gBAAgB,IAAI;4BAC1B,OAAO,gBAAgB,KAAK;4BAC5B,SAAS,gBAAgB,OAAO;4BAChC,cAAc,gBAAgB,YAAY;4BAC1C,MAAM;wBACR;oBACF;gBACF;gBACA,OAAO;YACT,GACC,MAAM,CAAC,CAAC,MAAQ,QAAQ;YAE3B,uDAAuD;YACvD,MAAM,UAAU,aAAa,SAAS;YAEtC,OAAO;gBACL,OAAO;gBACP,YAAY,cAAc;gBAC1B;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;AAKO,MAAM,eAAe;IAC1B;;GAEC,GACD,MAAM,YACJ,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,aAAqB,EAAE;QAEvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,8CAA8C;YAC9C,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;QAC7B,CAAC,EAEA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,kCAAkC;YAClC,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,QAAQ,MAAM,KAAK,CACjB,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,iCAAiC;YACjC,IAAI,aAAa,SACd,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;YACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;YACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;;QAE5B,CAAC,EACC;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAE9B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,aAAa,WAAW,KAAK,CAC3B,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;YAEvD,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,WAAW,OAAO,EAAE;YACpE;YAEA,mCAAmC;YACnC,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,qCAAqC;YACrC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAE5B,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAE7C,MAAM,EAAE,MAAM,iBAAiB,EAAE,KAAK,EAAE,GAAG,MAAM;YAGjD,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YAC3D;YAEA,qBAAqB;YACrB,MAAM,mBAAsC,CAAC,qBAAqB,EAAE,EAAE,GAAG,CACvE,CAAC,OAGK,CAAC;oBACL,IAAI,KAAK,EAAE;oBACX,mBAAmB,MAAM,OAAO,CAAC,KAAK,iBAAiB,IACnD,KAAK,iBAAiB,CAAC,EAAE,GACzB,KAAK,iBAAiB;gBAC5B,CAAC;YAGH,MAAM,UAAU,aAAa,SAAS;YAEtC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,QAAO,MAAc;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAE5E,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM,OAAO,EAAE;YACtD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,4BACJ,UAAkB,EAClB,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,wBAAwB;YACxB,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAChD,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE;YAEnC,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,wDAAwD;YACxD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YAE1B,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAC9C,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,EAC1C,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE,YAChC,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;gBAAE,WAAW;YAAM,GAC7C,KAAK,CAAC,MAAM,OAAO,QAAQ;YAG9B,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;gBAChC,OAAO;oBACL,OAAO,EAAE;oBACT;oBACA,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,8CAA8C;YAC9C,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO;YAEhD,kEAAkE;YAElE,MAAM,CAAC,wBAAwB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnE,CAAA,GAAA,wIAAA,CAAA,iCAA8B,AAAD,EAAE;gBAC/B,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,EAAE,EAE9L,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;aACnB;YAKD,8BAA8B;YAC9B,MAAM,sBAAsB,IAAI,IAC9B,uBAAuB,IAAI,EAAE,IAAI,CAAC,UAAY;oBAAC,QAAQ,EAAE;oBAAE;iBAAQ,KAAK,EAAE;YAE5E,MAAM,sBAAsB,IAAI,IAC9B,iBAAiB,IAAI,EAAE,IAAI,CAAC,UAAY;oBAAC,QAAQ,EAAE;oBAAE;iBAAQ,KAAK,EAAE;YAGtE,kDAAkD;YAElD,MAAM,iBAAiB,MACpB,GAAG,CAAC,CAAC;gBACJ,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,KAAK,OAAO;gBAC5D,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,KAAK,OAAO;gBAE5D,IAAI,iBAAiB;oBACnB,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,SAAS,KAAK,OAAO;wBACrB,mBAAmB;wBACnB,cAAc;oBAChB;gBACF,OAAO,IAAI,iBAAiB;oBAC1B,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,SAAS,KAAK,OAAO;wBACrB,mBAAmB;wBACnB,cAAc;oBAChB;gBACF;gBAEA,OAAO;YACT,GACC,MAAM,CAAC,CAAC,OAAS,SAAS;YAI7B,MAAM,UAAU,aAAa,OAAO;YAEpC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;AAKO,MAAM,iBAAiB;IAC5B;;GAEC,GACD,MAAM,cACJ,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAA6D,QAAQ,EACrE,aAAqB,EAAE;QAEvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,6EAA6E;YAC7E,IAAI;YAEJ,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,qDAAqD;gBACrD,cAAc,MAAM,SACjB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CACL,CAAC;YACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;YACb,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;cACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;cACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;;UAE5B,CAAC,EACC;oBAAE,OAAO;oBAAS,MAAM;gBAAK,GAE9B,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,QACpB,KAAK,CACJ,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE9B,OAAO;gBACL,mDAAmD;gBACnD,cAAc,MAAM,SACjB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;oBAAE,OAAO;oBAAS,MAAM;gBAAK,GAChD,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YACzB;YAEA,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG;YAEjD,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,WAAW,OAAO,EAAE;YACtE;YAEA,qCAAqC;YACrC,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,yCAAyC;YACzC,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CACL,CAAC;UACD,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;UACb,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC;UACjB,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC;UACtB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;UACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC;UACrB,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;UAC9B,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC;UAClB,EAAE,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC;YACzB,EAAE,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC;YACb,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;YACxB,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC;YACxB,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;;QAEvB,CAAC,EAEA,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YAEvB,kCAAkC;YAClC,IAAI,cAAc,WAAW,IAAI,IAAI;gBACnC,QAAQ,MAAM,KAAK,CACjB,GAAG,4HAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EACtD,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;YAE5B;YAEA,gBAAgB;YAChB,OAAQ;gBACN,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;wBAAE,WAAW;oBAAK;oBAC1D;gBACF,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;wBAAE,WAAW;oBAAM;oBACvD;gBACF,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;wBAAE,WAAW;oBAAK;oBACtD;gBACF,KAAK;gBACL;oBACE,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;wBAAE,WAAW;oBAAM;oBAC3D;YACJ;YAEA,mBAAmB;YACnB,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAC5B,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAE7C,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM;YAEvC,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;YAC7D;YAEA,4DAA4D;YAC5D,MAAM,qBAAmC,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,CAAC,SASxD,CAAC;oBACL,IAAI,OAAO,EAAE;oBACb,QAAQ,OAAO,MAAM;oBACrB,aAAa,OAAO,WAAW;oBAC/B,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,qBAAqB,OAAO,mBAAmB;oBAC/C,SAAS,OAAO,OAAO;oBACvB,mBAAmB,MAAM,OAAO,CAAC,OAAO,iBAAiB,IACrD,OAAO,iBAAiB,CAAC,EAAE,GAC3B,OAAO,iBAAiB;gBAC9B,CAAC;YAED,MAAM,UAAU,aAAa,SAAS;YAEtC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cAAa,QAAgB;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,GACN,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAElB,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cACJ,QAAgB,EAChB,MAAc,EACd,UAAkB;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAClC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC;gBACN,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE;gBAClB,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,cAAc;gBACrC,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,IAAI,OAAO,WAAW;YAC9C,GACC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;YAElB,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,8BACJ,UAAkB,EAClB,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAA6D,QAAQ;QAgCrE,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAElC,wBAAwB;YACxB,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAChD,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE;YAEnC,IAAI,YAAY;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,cAAc,eAAe,GAAG;gBACnC,OAAO;oBACL,OAAO,EAAE;oBACT,YAAY;oBACZ,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,0CAA0C;YAC1C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YAC1B,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,MAAM,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,EACnK,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,mBAAmB,EAAE;YAEnC,gBAAgB;YAChB,OAAQ;gBACN,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;wBAAE,WAAW;oBAAK;oBAC1D;gBACF,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;wBAAE,WAAW;oBAAM;oBACvD;gBACF,KAAK;oBACH,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,MAAM,EAAE;wBAAE,WAAW;oBAAK;oBACtD;gBACF,KAAK;gBACL;oBACE,QAAQ,MAAM,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE;wBAAE,WAAW;oBAAM;oBAC3D;YACJ;YAEA,QAAQ,MAAM,KAAK,CAAC,MAAM,OAAO,QAAQ;YAEzC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM;YAErD,IAAI,cAAc;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;gBACpC,OAAO;oBACL,OAAO,EAAE;oBACT;oBACA,SAAS;oBACT,aAAa;gBACf;YACF;YAEA,8CAA8C;YAC9C,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAC,SAAW,OAAO,OAAO;YAEtD,kEAAkE;YAClE,MAAM,CAAC,wBAAwB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnE,CAAA,GAAA,wIAAA,CAAA,iCAA8B,AAAD,EAAE;gBAC/B,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CACL,GAAG,4HAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,KAAK,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE,EAEzK,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;aACnB;YAED,8BAA8B;YAC9B,MAAM,sBAAsB,IAAI,IAC9B,uBAAuB,IAAI,EAAE,IAAI,CAAC,UAAY;oBAAC,QAAQ,EAAE;oBAAE;iBAAQ,KAAK,EAAE;YAE5E,MAAM,sBAAsB,IAAI,IAC9B,iBAAiB,IAAI,EAAE,IAAI,CAAC,UAAY;oBAAC,QAAQ,EAAE;oBAAE;iBAAQ,KAAK,EAAE;YAGtE,oDAAoD;YACpD,MAAM,mBAAmB,QACtB,GAAG,CAAC,CAAC;gBACJ,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,OAAO,OAAO;gBAC9D,MAAM,kBAAkB,oBAAoB,GAAG,CAAC,OAAO,OAAO;gBAE9D,IAAI,iBAAiB;oBACnB,OAAO;wBACL,IAAI,OAAO,EAAE;wBACb,QAAQ,OAAO,MAAM;wBACrB,aAAa,OAAO,WAAW;wBAC/B,YAAY,OAAO,UAAU;wBAC7B,YAAY,OAAO,UAAU;wBAC7B,qBAAqB,OAAO,mBAAmB;wBAC/C,SAAS,OAAO,OAAO;wBACvB,mBAAmB;wBACnB,cAAc;oBAChB;gBACF,OAAO,IAAI,iBAAiB;oBAC1B,OAAO;wBACL,IAAI,OAAO,EAAE;wBACb,QAAQ,OAAO,MAAM;wBACrB,aAAa,OAAO,WAAW;wBAC/B,YAAY,OAAO,UAAU;wBAC7B,YAAY,OAAO,UAAU;wBAC7B,qBAAqB,OAAO,mBAAmB;wBAC/C,SAAS,OAAO,OAAO;wBACvB,mBAAmB;wBACnB,cAAc;oBAChB;gBACF;gBACA,OAAO;YACT,GACC,MAAM,CAAC,CAAC,OAAS,SAAS;YAE7B,MAAM,UAAU,aAAa,OAAO;YAEpC,OAAO;gBACL,OAAO;gBACP;gBACA;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;AACF;AAKO,eAAe,mBACpB,MAAc;IAEd,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,oCAAoC;QACpC,MAAM,CAAC,aAAa,eAAe,oBAAoB,GAAG,MAAM,QAAQ,GAAG,CACzE;YACE,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;YAAQ,GACpC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YACvB,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,eAAe,EAC3B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;YAAQ,GACpC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YACvB,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;gBAAE,OAAO;YAAQ,GACpC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE;SACxB;QAGH,MAAM,UAA2B;YAC/B,YAAY,YAAY,KAAK,IAAI;YACjC,aAAa,cAAc,KAAK,IAAI;YACpC,mBAAmB,oBAAoB,KAAK,IAAI;YAChD,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAGO,MAAM,gBAAgB;IAC3B;IACA;IACA;IACA,sBAAsB;AACxB", "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/reviews/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { reviewsService } from '@/lib/services/socialService';\r\n\r\nexport async function fetchCustomerReviews(\r\n  userId: string,\r\n  page: number = 1,\r\n  limit: number = 10,\r\n  sortBy: \"newest\" | \"oldest\" | \"rating_high\" | \"rating_low\" = \"newest\"\r\n) {\r\n  try {\r\n    const result = await reviewsService.fetchReviews(userId, page, limit, sortBy);\r\n    return { success: true, data: result };\r\n  } catch (error) {\r\n    console.error(\"Error in fetchCustomerReviews server action:\", error);\r\n    return { success: false, error: (error as Error).message };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;;;AAEO,eAAe,qBACpB,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAA6D,QAAQ;IAErE,IAAI;QACF,MAAM,SAAS,MAAM,gIAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,QAAQ,MAAM,OAAO;QACtE,OAAO;YAAE,SAAS;YAAM,MAAM;QAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YAAE,SAAS;YAAO,OAAO,AAAC,MAAgB,OAAO;QAAC;IAC3D;AACF;;;IAbsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;;;;;;AAKO,eAAe,oBACpB,iBAAyB;IAEzB,0DAA0D;IAC1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,iEAAiE;IACjE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,8DAA8D;QAC9D,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,+EAA+E;YAC/E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,gCAAgC,EAAE,kBAAkB,CAAC,CAAC;gBAExE,qEAAqE;gBACrE,OAAO;oBAAE,SAAS;gBAAK;YACzB;YACA,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,yEAAyE;QACzE,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;QAEvE,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAIO,eAAe,wBACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,qEAAqE;IACrE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,8DAA8D;QAC9D,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB,EACzB,MAAc,EACd,UAA0B,AAAC,6BAA6B;;IAExD,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,4DAA4D;IAC5D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;IAEA,IAAI,SAAS,KAAK,SAAS,GAAG;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAkC;IACpE;IAEA,IAAI;QACF,iGAAiG;QACjG,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,mBACL,MAAM,CACL;YACE,SAAS,KAAK,EAAE;YAChB,qBAAqB;YACrB,QAAQ;YACR,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACA;YACE,YAAY;QACd;QAGJ,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,6DAA6D;QAEpG,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,IAAI;QACF,qCAAqC;QACrC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,mBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,yDAAyD;IACzD,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,IAAI;QACF,sDAAsD;QACtD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,0EAA0E;YAC1E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,CAAC;gBAEhE,OAAO;oBAAE,SAAS;gBAAK,GAAG,uCAAuC;YACnE;YACA,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,eACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,2DAA2D;IAC3D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI;QACF,sDAAsD;QACtD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,qBAAqB,iBAAyB;IAOlE,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAClE,IAAI,SAAwB;IAE5B,mEAAmE;IACnE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,IAAI,MAAM;QACR,SAAS,KAAK,EAAE;IAClB;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,cAAc;QACd,UAAU;QACV,YAAY;QACZ,YAAY;IACd;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO,eAAe,yCAAyC;IACjE;IAEA,IAAI;QACF,gEAAgE;QAChE,iCAAiC;QACjC,MAAM,CAAC,iBAAiB,SAAS,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC9D,SACG,IAAI,CAAC,iBACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,SACG,IAAI,CAAC,SACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,SACG,IAAI,CAAC,mBACL,MAAM,CAAC,uBACP,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB,GAChE,WAAW;SACf;QAED,uCAAuC;QACvC,IAAI,gBAAgB,KAAK,EACvB,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,gBAAgB,KAAK,CAAC,OAAO,EAAE;QAEhE,IAAI,QAAQ,KAAK,EACf,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ,KAAK,CAAC,OAAO,EAAE;QAC9D,IAAI,UAAU,KAAK,EACjB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,UAAU,KAAK,CAAC,OAAO,EAAE;QAElE,MAAM,aAAa,UAAU,IAAI;QAEjC,OAAO;YACL,cAAc,CAAC,gBAAgB,KAAK,IAAI,CAAC,IAAI;YAC7C,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI;YACjC,YAAY,YAAY,UAAU;YAClC,YAAY,YAAY,eAAe;QACzC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,sDAAsD;QACtD,OAAO;YAAE,GAAG,aAAa;YAAE,OAAO;QAAa;IACjD;AACF;;;IAvgBsB;IAuFA;IA4EA;IA2EA;IAsDA;IA8EA;IA0EA;;AA5bA,+OAAA;AAuFA,+OAAA;AA4EA,+OAAA;AA2EA,+OAAA;AAsDA,+OAAA;AA8EA,+OAAA;AA0EA,+OAAA", "debugId": null}}, {"offset": {"line": 1879, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/customer/reviews/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '00717e70cd790befefd26ed70b7132877c74908f23'} from 'ACTIONS_MODULE0'\nexport {requireCompleteName as '40160bb260c662906af3bb9a7066a411b7708da678'} from 'ACTIONS_MODULE1'\nexport {validateCustomerAddress as '40779b81e49cd52b3b027ee98034e2d303cc0ff9fc'} from 'ACTIONS_MODULE1'\nexport {validateCustomerName as '40bebad49fe3b2a7a7d211d615b87699a945cdc8d3'} from 'ACTIONS_MODULE1'\nexport {requireCompleteAddress as '40e4b24a2ecdf469f0758d5350d74a57b85811e8ed'} from 'ACTIONS_MODULE1'\nexport {getCustomerAddressData as '40e5261a3e0c8b9ab32b0ef011062acc83d26ad112'} from 'ACTIONS_MODULE1'\nexport {requireCompleteProfile as '60d95347f55f6976253cb5b8b9c38207ec7f210432'} from 'ACTIONS_MODULE1'\nexport {fetchCustomerReviews as '7801d0575193fd72376b4c784fe7edb938baf4dc33'} from 'ACTIONS_MODULE2'\nexport {deleteReview as '402fb4e645ec2ec8469bcbc22e49d6a1e15fdb3dec'} from 'ACTIONS_MODULE3'\n"], "names": [], "mappings": ";AAAA;AACA;AAMA;AACA", "debugId": null}}, {"offset": {"line": 1976, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/reviews/components/ReviewsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+U,GAC5W,6GACA", "debugId": null}}, {"offset": {"line": 1990, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/reviews/components/ReviewsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/customer/reviews/components/ReviewsPageClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2T,GACxV,yFACA", "debugId": null}}, {"offset": {"line": 2004, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2014, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/reviews/page.tsx"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { redirect } from 'next/navigation';\r\nimport { Metadata } from 'next';\r\nimport ReviewsPageClient from './components/ReviewsPageClient';\r\nimport { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';\r\n\r\n// Define interfaces for the expected data structure\r\n// interface BusinessProfileDataForReview {\r\n//   id: string;\r\n//   business_name: string | null;\r\n//   business_slug: string | null;\r\n//   logo_url: string | null;\r\n// }\r\n\r\n// Unused interface - keeping for potential future use\r\n// interface ReviewWithProfile {\r\n//   id: string;\r\n//   rating: number;\r\n//   review_text: string | null;\r\n//   created_at: string;\r\n//   updated_at: string;\r\n//   business_profile_id: string;\r\n//   user_id: string;\r\n//   business_profiles: BusinessProfileDataForReview | null;\r\n// }\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"My Reviews - Dukancard\",\r\n  robots: \"noindex, nofollow\",\r\n};\r\n\r\nexport default async function CustomerReviewsPage() {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    redirect('/login?message=Please log in to view your reviews.');\r\n  }\r\n\r\n  // Check if customer has complete address\r\n  await requireCompleteProfile(user.id);\r\n\r\n  try {\r\n    // Get count of reviews written by the customer\r\n    const { count: reviewsCount } = await supabase\r\n      .from('ratings_reviews')\r\n      .select('*', { count: 'exact', head: true })\r\n      .eq('user_id', user.id);\r\n\r\n    return <ReviewsPageClient reviewsCount={reviewsCount || 0} />;\r\n  } catch (_error) {\r\n    // If there's an error fetching count, still render the page with 0 count\r\n    return <ReviewsPageClient reviewsCount={0} />;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAEA;AACA;;;;;;AAsBO,MAAM,WAAqB;IAChC,OAAO;IACP,QAAQ;AACV;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,yCAAyC;IACzC,MAAM,CAAA,GAAA,uJAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,EAAE;IAEpC,IAAI;QACF,+CAA+C;QAC/C,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,mBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,WAAW,KAAK,EAAE;QAExB,qBAAO,8OAAC,4LAAA,CAAA,UAAiB;YAAC,cAAc,gBAAgB;;;;;;IAC1D,EAAE,OAAO,QAAQ;QACf,yEAAyE;QACzE,qBAAO,8OAAC,4LAAA,CAAA,UAAiB;YAAC,cAAc;;;;;;IAC1C;AACF", "debugId": null}}]}