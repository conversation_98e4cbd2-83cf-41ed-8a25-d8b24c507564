"use server";

import { unstable_noStore as noStore } from "next/cache";
import { createClient } from "@/utils/supabase/server";
import { TABLES, COLUMNS } from "@/lib/supabase/constants";
import { Tables } from "@/types/supabase";

type BusinessProfileAnalyticsRow = Pick<Tables<'business_profiles'>, 'total_visits' | 'today_visits' | 'yesterday_visits' | 'visits_7_days' | 'visits_30_days'>;

// getAuthenticatedUser
async function getAuthenticatedUser() {
  const supabase = await createClient();
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error("Error fetching authenticated user:", error.message);
      return { user: null, error: "User not found or authentication error." };
    }
    return { user, error: null };
  } catch (err) {
    console.error("Unexpected error fetching authenticated user:", err);
    return { user: null, error: "An unexpected error occurred." };
  }
}

// getBusinessProfileAnalyticsData
async function getBusinessProfileAnalyticsData(businessProfileId: string): Promise<{ data: BusinessProfileAnalyticsRow | null; error: string | null }> {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select("total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days")
      .eq(COLUMNS.ID, businessProfileId)
      .single();

    if (error) {
      console.error("Error fetching business profile analytics data:", error.message);
      return { data: null, error: error.message };
    }
    return { data: data as BusinessProfileAnalyticsRow, error: null };
  } catch (err) {
    console.error("Unexpected error fetching business profile analytics data:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

// getDailyUniqueVisitTrend
async function getDailyUniqueVisitTrend(businessId: string, startDate: string, endDate: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_daily_unique_visit_trend", {
      business_id: businessId,
      start_date: startDate,
      end_date: endDate,
    });
    if (error) {
      console.error("Error fetching daily unique visit trend:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching daily unique visit trend:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

// getHourlyUniqueVisitTrend
async function getHourlyUniqueVisitTrend(businessId: string, targetDate: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_hourly_unique_visit_trend", {
      business_id: businessId,
      target_date: targetDate,
    });
    if (error) {
      console.error("Error fetching hourly unique visit trend:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching hourly unique visit trend:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

// getMonthlyUniqueVisits
async function getMonthlyUniqueVisits(businessId: string, targetYear: number, targetMonth: number) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_monthly_unique_visits", {
      business_id: businessId,
      target_year: targetYear,
      target_month: targetMonth,
    });
    if (error) {
      console.error("Error fetching monthly unique visits:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching monthly unique visits:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

// getMonthlyUniqueVisitTrend
async function getMonthlyUniqueVisitTrend(businessId: string, startYear: number, startMonth: number, endYear: number, endMonth: number) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_monthly_unique_visit_trend", {
      business_id: businessId,
      start_year: startYear,
      start_month: startMonth,
      end_year: endYear,
      end_month: endMonth,
    });
    if (error) {
      console.error("Error fetching monthly unique visit trend:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching monthly unique visit trend:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

// getAvailableYearsForMonthlyMetrics
async function getAvailableYearsForMonthlyMetrics(businessId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_available_years_for_monthly_metrics", {
      business_id: businessId,
    });
    if (error) {
      console.error("Error fetching available years for monthly metrics:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching available years for monthly metrics:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

// getTotalUniqueVisits
async function getTotalUniqueVisits(businessId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_total_unique_visits", {
      business_id: businessId,
    });
    if (error) {
      console.error("Error fetching total unique visits:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching total unique visits:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}


/**
 * Structure for the analytics data returned by getVisitAnalytics
 *
 * This data is used to display visitor metrics on the analytics page.
 * The data comes from three sources:
 * 1. Pre-aggregated metrics in the business_profiles table
 * 2. Monthly metrics in the monthly_visit_metrics table
 * 3. Trend data calculated by database functions
 *
 * Database Implementation:
 * - total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days
 *   are stored in the business_profiles table and updated by triggers
 * - currentMonthUniqueVisits, previousMonthUniqueVisits are stored in the monthly_visit_metrics table
 *   and updated by the update_monthly_visit_counts() function via the handle_new_visit_monthly trigger
 * - dailyTrend7Days, dailyTrend30Days, hourlyTrendToday, monthlyTrend are calculated
 *   by database functions when requested
 *
 * Timezone Handling:
 * - All visit metrics are calculated based on IST (Indian Standard Time, UTC+5:30)
 * - The visited_at column in card_visits stores timestamps in UTC
 * - All database functions convert these timestamps to IST using 'visited_at AT TIME ZONE 'Asia/Kolkata''
 * - The update_visit_counts() function uses the most recent visit date in IST as "today"
 * - This ensures accurate metrics regardless of when the functions run
 *
 * Monthly Metrics Implementation:
 * - Monthly metrics are stored in the monthly_visit_metrics table with year and month columns
 * - The update_monthly_visit_counts() function updates the monthly metrics when a new visit is recorded
 * - The update_all_monthly_visit_metrics() function updates monthly metrics for all businesses
 *   and is scheduled to run on the 1st day of each month
 * - The populate_historical_monthly_data() function populates historical monthly data
 *   from existing card_visits records
 * - The get_monthly_unique_visits() function gets monthly unique visits for a specific month and year
 * - The get_monthly_unique_visit_trend() function gets monthly trend data for a date range
 * - The get_available_years_for_monthly_metrics() function gets available years from monthly_visit_metrics table
 *
 * Real-time Updates:
 * - The component uses realtime database for all metrics:
 *   - Total unique visits, today's visits, yesterday's visits, 7-day visits, and 30-day visits from business_profiles table
 *   - Total likes, total subscriptions, and average rating from business_profiles table
 *   - Monthly unique visits from monthly_visit_metrics table
 * - When changes occur in any of these tables, the UI updates automatically with animations
 * - Note: card_visits table does not have realtime enabled; instead, we rely on the business_profiles table
 *   which has pre-aggregated metrics that are updated by triggers when new visits are recorded
 */
export interface VisitAnalyticsData {
  totalUniqueVisits: number;  // Total lifetime unique visitors
  todayUniqueVisits: number;  // Unique visitors today (based on IST timezone)
  yesterdayUniqueVisits: number;  // Unique visitors yesterday (based on IST timezone)
  visits7Days: number;  // Unique visitors in the last 7 days
  visits30Days: number;  // Unique visitors in the last 30 days
  currentMonthUniqueVisits: number;  // Unique visitors in the current month
  previousMonthUniqueVisits: number;  // Unique visitors in the previous month
  currentYear: number;  // Current year (for UI display)
  currentMonth: number;  // Current month (for UI display)
  dailyTrend7Days: { date: string; visits: number }[];  // Daily trend for the last 7 days
  dailyTrend30Days: { date: string; visits: number }[];  // Daily trend for the last 30 days
  hourlyTrendToday: { hour: number; visits: number }[];  // Hourly trend for today
  monthlyTrend: { year: number; month: number; visits: number }[];  // Monthly trend data
  availableYears: number[];  // Available years for monthly trend selection
}

export interface DailyTrendData {
  dailyTrend7Days: { date: string; visits: number }[];
  dailyTrend30Days: { date: string; visits: number }[];
}

export interface HourlyTrendData {
  hourlyTrendToday: { hour: number; visits: number }[];
}

// Helper function to get date strings in 'Asia/Kolkata' timezone (YYYY-MM-DD)
const getKolkataDateString = (offsetDays: number = 0): string => {
  // Get current time in UTC
  const now = new Date();
  // Calculate the offset for IST (UTC+5:30) in milliseconds
  const istOffset = 5.5 * 60 * 60 * 1000;
  // Create a new Date object representing the time in IST
  const istDate = new Date(now.getTime() + istOffset);
  // Apply the day offset in UTC to avoid issues around midnight IST
  istDate.setUTCDate(istDate.getUTCDate() - offsetDays);
  // Format as YYYY-MM-DD based on the IST date parts
  const year = istDate.getUTCFullYear();
  const month = String(istDate.getUTCMonth() + 1).padStart(2, "0");
  const day = String(istDate.getUTCDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// Removed unused getStartOfDayUTC function

/**
 * Gets visit analytics data for the current user's business profile
 *
 * This function:
 * 1. Gets the current user's business profile ID
 * 2. Fetches pre-aggregated metrics from the business_profiles table
 * 3. Fetches monthly metrics from the monthly_visit_metrics table
 * 4. Fetches trend data using database functions
 * 5. Falls back to calculating metrics if pre-aggregated data is not available
 *
 * Database Functions Used:
 * - get_daily_unique_visit_trend: Gets daily trend data for a date range based on IST dates
 * - get_hourly_unique_visit_trend: Gets hourly trend data for a specific date based on IST hours
 * - get_monthly_unique_visits: Gets monthly unique visits for a specific month and year
 * - get_monthly_unique_visit_trend: Gets monthly trend data for a date range
 * - get_total_unique_visits: Gets total unique visits (fallback)
 * - get_daily_unique_visits: Gets daily unique visits (fallback)
 * - get_period_unique_visits: Gets period unique visits (fallback)
 *
 * Pre-aggregated Metrics:
 * - total_visits: Updated by update_visit_counts() trigger function
 * - today_visits: Updated by update_visit_counts() trigger function based on the most recent visit date in IST
 * - yesterday_visits: Updated by update_visit_counts() trigger function based on the day before the most recent visit date
 * - visits_7_days: Updated by update_visit_counts() trigger function
 * - visits_30_days: Updated by update_visit_counts() trigger function
 *
 * Monthly Metrics:
 * - currentMonthUniqueVisits: Stored in monthly_visit_metrics table
 * - previousMonthUniqueVisits: Stored in monthly_visit_metrics table
 * - monthlyTrend: Calculated by get_monthly_unique_visit_trend() function
 *
 * These metrics are managed by scheduled cron jobs:
 * - reset-visit-counts: Runs at midnight IST (6:30 PM UTC) to reset daily counts
 * - clean-card-visits: Runs at 1 AM IST (7:30 PM UTC) to delete records older than 31 days
 * - update-monthly-visit-metrics: Runs at midnight IST on the 1st day of each month
 *
 * @returns Object containing analytics data or error
 */
export async function getVisitAnalytics(userPlan?: string | null): Promise<{
  data?: VisitAnalyticsData;
  error?: string;
}> {
  // Check if user has access to premium analytics features
  const isPremiumUser = userPlan === "growth" || userPlan === "pro" || userPlan === "enterprise";
  noStore(); // Ensure data is fetched dynamically
  const { user, error: userError } = await getAuthenticatedUser();
  if (userError || !user) {
    return { error: "Authentication required." };
  }

  // 2. Get business profile ID (assuming it's the same as user ID for business users)
  // Adjust if business_profile_id is stored differently or needs a separate lookup
  const businessProfileId = user.id;

  try {
    // First, get the pre-aggregated visit counts from business_profiles
    const { data: profile, error: profileError } = await getBusinessProfileAnalyticsData(businessProfileId);

    if (profileError || !profile) {
      throw new Error(`Failed to fetch business profile analytics data: ${profileError || 'Profile data is null'}`);
    }

    // --- Fetch Aggregated Data using NEW RPC Functions ---
    const todayStr = getKolkataDateString(0);
    const yesterdayStr = getKolkataDateString(1);
    const sevenDaysAgoStr = getKolkataDateString(7);
    const thirtyDaysAgoStr = getKolkataDateString(30);

    // Get current date in IST for monthly metrics
    const now = new Date();
    const istOffset = 5.5 * 60 * 60 * 1000;
    const istDate = new Date(now.getTime() + istOffset);
    const currentYear = istDate.getUTCFullYear();
    const currentMonth = istDate.getUTCMonth() + 1; // 1-12

    // Calculate previous month and year
    let previousMonth = currentMonth - 1;
    let previousYear = currentYear;
    if (previousMonth === 0) {
      previousMonth = 12;
      previousYear = currentYear - 1;
    }

    // Initialize premium data with empty arrays for basic plan users
    let trend7Data: { date: string; visits: number }[] = [];
    let trend30Data: { date: string; visits: number }[] = [];
    let hourlyTrendData: { hour: number; visits: number }[] = [];

    // Only fetch premium analytics data for premium users
    if (isPremiumUser) {
      // Daily Trend (Last 7 Days)
      const { data: trend7Result, error: trend7Error } = await getDailyUniqueVisitTrend(
        businessProfileId,
        sevenDaysAgoStr,
        todayStr
      );
      if (trend7Error) throw new Error(`Failed to fetch 7-day trend: ${trend7Error || 'Unknown error'}`);
      trend7Data = trend7Result ?? [];

      // Daily Trend (Last 30 Days)
      const { data: trend30Result, error: trend30Error } = await getDailyUniqueVisitTrend(
        businessProfileId,
        thirtyDaysAgoStr,
        todayStr
      );
      if (trend30Error) throw new Error(`Failed to fetch 30-day trend: ${trend30Error || 'Unknown error'}`);
      trend30Data = trend30Result ?? [];

      // Hourly Trend (Today)
      const { data: hourlyTrendResult, error: hourlyTrendError } = await getHourlyUniqueVisitTrend(
        businessProfileId,
        todayStr
      );
      if (hourlyTrendError) throw new Error(`Failed to fetch hourly trend: ${hourlyTrendError || 'Unknown error'}`);
      hourlyTrendData = hourlyTrendResult ?? [];
    }

    // Get current month's unique visits
    const { data: currentMonthData, error: currentMonthError } = await getMonthlyUniqueVisits(
      businessProfileId,
      currentYear,
      currentMonth
    );
    if (currentMonthError) throw new Error(`Failed to fetch current month visits: ${currentMonthError || 'Unknown error'}`);

    // Get previous month's unique visits
    const { data: previousMonthData, error: previousMonthError } = await getMonthlyUniqueVisits(
      businessProfileId,
      previousYear,
      previousMonth
    );
    if (previousMonthError) throw new Error(`Failed to fetch previous month visits: ${previousMonthError || 'Unknown error'}`);

    // Initialize monthly trend data with empty arrays for basic plan users
    let availableYears: number[] = [currentYear];
    let monthlyTrendData: { year: number; month: number; visits: number }[] = [];

    // Only fetch monthly trend data for premium users
    if (isPremiumUser) {
      // Get available years from monthly_visit_metrics
      const { data: availableYearsData, error: availableYearsError } = await getAvailableYearsForMonthlyMetrics(
        businessProfileId
      );
      if (availableYearsError) throw new Error(`Failed to fetch available years: ${availableYearsError || 'Unknown error'}`);

      // Create array of available years, with current year as fallback
      if (availableYearsData && Array.isArray(availableYearsData) && availableYearsData.length > 0) {
        availableYears = availableYearsData.map(item => item.year);
      } else {
        // If no years available, use current year as fallback
        availableYears = [currentYear];
      }

      // Get monthly trend data for all available years
      const { data: monthlyTrendResult, error: monthlyTrendError } = await getMonthlyUniqueVisitTrend(
        businessProfileId,
        Math.min(...availableYears),
        1,
        currentYear,
        currentMonth
      );
      if (monthlyTrendError) throw new Error(`Failed to fetch monthly trend: ${monthlyTrendError || 'Unknown error'}`);
      monthlyTrendData = monthlyTrendResult ?? [];
    }

    let totalVisits: number;
    let todayVisits: number;
    let yesterdayVisits: number;
    let visits7Days: number;
    let visits30Days: number;

    if (profile) {
      totalVisits = profile.total_visits ?? 0;
      todayVisits = profile.today_visits ?? 0;
      yesterdayVisits = profile.yesterday_visits ?? 0;
      visits7Days = profile.visits_7_days ?? 0;
      visits30Days = profile.visits_30_days ?? 0;
    } else {
      console.warn("Using calculated visit metrics due to missing pre-aggregated data");

      // Calculate Today's and Yesterday's visits from the 7-day trend data
      todayVisits = trend7Data?.find((d: { date: string; visits: number }) => d.date === todayStr)?.visits ?? 0;
      yesterdayVisits = trend7Data?.find((d: { date: string; visits: number }) => d.date === yesterdayStr)?.visits ?? 0;

      // Calculate total visits (fallback)
      const { data: totalCount, error: totalError } = await getTotalUniqueVisits(
        businessProfileId
      );
      if (totalError) throw new Error(`Failed to fetch total visits: ${totalError || 'Unknown error'}`);
      totalVisits = totalCount ?? 0;

      // Calculate 7-day and 30-day visits
      visits7Days = trend7Data?.reduce((sum: number, day: { visits: number }) => sum + day.visits, 0) ?? 0;
      visits30Days = trend30Data?.reduce((sum: number, day: { visits: number }) => sum + day.visits, 0) ?? 0;
    }

    // --- Clicks Data Removed ---

    const analyticsData: VisitAnalyticsData = {
      totalUniqueVisits: Number(totalVisits),
      todayUniqueVisits: Number(todayVisits),
      yesterdayUniqueVisits: Number(yesterdayVisits),
      visits7Days: Number(visits7Days),
      visits30Days: Number(visits30Days),
      currentMonthUniqueVisits: Number(currentMonthData || 0),
      previousMonthUniqueVisits: Number(previousMonthData || 0),
      currentYear: currentYear,
      currentMonth: currentMonth,
      dailyTrend7Days: trend7Data ?? [],
      dailyTrend30Days: trend30Data ?? [],
      hourlyTrendToday: hourlyTrendData ?? [],
      monthlyTrend: monthlyTrendData ?? [],
      availableYears: availableYears,
    };

    return { data: analyticsData };

  } catch (error) {
    console.error("getVisitAnalytics Error:", error);
    // Ensure error message is always a string
    return {
      error:
        error instanceof Error ? error.message : "An unknown error occurred.",
    };
  }
}
