/* Custom styles for html5-qrcode scanner to match Dukancard theme */

/* Scanner container */
#qr-scanner-region {
  border-radius: 12px;
  overflow: hidden;
  background: #000;
}

/* Scanner video element */
#qr-scanner-region video {
  border-radius: 12px;
  width: 100% !important;
  height: auto !important;
  object-fit: cover;
}

/* Scanner overlay */
#qr-scanner-region canvas {
  border-radius: 12px;
}

/* Control buttons styling */
#qr-scanner-region button {
  background: rgba(212, 175, 55, 0.9) !important; /* Gold theme */
  border: none !important;
  border-radius: 8px !important;
  color: white !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  margin: 4px !important;
  transition: all 0.2s ease !important;
}

#qr-scanner-region button:hover {
  background: rgba(212, 175, 55, 1) !important;
  transform: translateY(-1px);
}

/* Torch/flashlight button */
#qr-scanner-region button[title*="torch"],
#qr-scanner-region button[title*="Torch"],
#qr-scanner-region button[title*="flash"],
#qr-scanner-region button[title*="Flash"] {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Camera selection dropdown */
#qr-scanner-region select {
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  border: 1px solid rgba(212, 175, 55, 0.5) !important;
  border-radius: 6px !important;
  padding: 6px 12px !important;
  font-size: 14px !important;
}

/* Scanner region text */
#qr-scanner-region span,
#qr-scanner-region div {
  color: white !important;
  font-family: inherit !important;
}

/* Hide default file input styling */
#qr-scanner-region input[type="file"] {
  background: rgba(212, 175, 55, 0.9) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 8px 16px !important;
  cursor: pointer !important;
}

/* Scanner status text */
#qr-scanner-region .qr-scanner-status {
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(10px);
  border-radius: 8px !important;
  padding: 8px 12px !important;
  margin: 8px !important;
}

/* Permission request styling */
#qr-scanner-region .qr-scanner-permission {
  background: rgba(212, 175, 55, 0.1) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  border-radius: 12px !important;
  padding: 20px !important;
  text-align: center !important;
}

/* Error messages */
#qr-scanner-region .qr-scanner-error {
  background: rgba(239, 68, 68, 0.1) !important;
  border: 1px solid rgba(239, 68, 68, 0.3) !important;
  border-radius: 8px !important;
  padding: 12px !important;
  color: #ef4444 !important;
}

/* Success messages */
#qr-scanner-region .qr-scanner-success {
  background: rgba(34, 197, 94, 0.1) !important;
  border: 1px solid rgba(34, 197, 94, 0.3) !important;
  border-radius: 8px !important;
  padding: 12px !important;
  color: #22c55e !important;
}

/* Loading spinner */
#qr-scanner-region .qr-scanner-loading {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 20px !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  #qr-scanner-region {
    border-radius: 8px;
  }
  
  #qr-scanner-region button {
    padding: 6px 12px !important;
    font-size: 14px !important;
  }
  
  #qr-scanner-region select {
    font-size: 12px !important;
    padding: 4px 8px !important;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  #qr-scanner-region {
    background: #0a0a0a;
  }
  
  #qr-scanner-region span,
  #qr-scanner-region div {
    color: #f5f5f5 !important;
  }
}

/* Animation for scanner frame */
@keyframes qr-scanner-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(212, 175, 55, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);
  }
}

#qr-scanner-region .qr-scanner-active {
  animation: qr-scanner-pulse 2s infinite;
}
