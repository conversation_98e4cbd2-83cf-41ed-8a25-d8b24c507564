import {
  likesService,
  BusinessLikeReceived,
  BusinessLikesReceivedResult,
  LikesResult
} from '@/lib/services/socialService';

// Re-export types for compatibility
export type { BusinessLikeReceived };
export interface BusinessMyLike {
  id: string;
  business_profiles: {
    id: string;
    business_name: string;
    business_slug: string | null;
    logo_url: string | null;
    city: string | null;
    state: string | null;
    pincode: string | null;
    address_line: string | null;
    locality: string | null;
  } | null;
}

/**
 * Fetch likes received by a business (customers/businesses who liked this business)
 */
export async function fetchBusinessLikesReceived(
  businessId: string,
  page: number = 1,
  limit: number = 10
): Promise<BusinessLikesReceivedResult> {
  try {
    console.log(`[fetchBusinessLikesReceived] Fetching for businessId: ${businessId}, page: ${page}, limit: ${limit}`);
    const result = await likesService.fetchBusinessLikesReceived(businessId, page, limit);
    console.log(`[fetchBusinessLikesReceived] Received ${result.items.length} items, totalCount: ${result.totalCount}`);
    return result;
  } catch (error) {
    console.error('Error in fetchBusinessLikesReceived:', error);
    throw error;
  }
}

/**
 * Fetch businesses that this business has liked
 */
export async function fetchBusinessMyLikes(
  businessId: string,
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ""
): Promise<LikesResult> {
  try {
    console.log(`[fetchBusinessMyLikes] Fetching for businessId: ${businessId}, page: ${page}, limit: ${limit}, searchTerm: ${searchTerm}`);
    const result = await likesService.fetchLikes(businessId, page, limit, searchTerm);
    console.log(`[fetchBusinessMyLikes] Received ${result.items.length} items, totalCount: ${result.totalCount}`);
    return result;
  } catch (error) {
    console.error('Error in fetchBusinessMyLikes:', error);
    throw error;
  }
}
