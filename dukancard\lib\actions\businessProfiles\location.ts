"use server";

import { createClient } from "@/utils/supabase/server";

import { BusinessProfilePublicData, BusinessSortBy } from "./types";
import { getCurrentISOTimestamp } from "./utils";

/**
 * Securely fetch business profiles by location using the service role key
 */
export async function getSecureBusinessProfilesByLocation(
  location: {
    pincode?: string;
    city?: string;
    state?: string;
    locality?: string;
  },
  page: number = 1,
  limit: number = 20,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  try {
    const supabase = await createClient();
    const _nowISO = getCurrentISOTimestamp();
    const offset = (page - 1) * limit;

    // Define fields to select
    const businessFields = `
      id,
      business_name,
      logo_url,
      member_name,
      title,
      address_line,
      city,
      state,
      pincode,
      locality,
      phone,
      instagram_url,
      facebook_url,
      whatsapp_number,
      about_bio,
      status,
      business_slug,
      theme_color,
      delivery_info,
      business_hours,
      business_category,
      total_likes,
      total_subscriptions,
      average_rating,
      created_at,
      updated_at,
      trial_end_date,
      contact_email,
      established_year,
      custom_branding,
      custom_ads,
      has_active_subscription,
      total_visits,
      today_visits,
      yesterday_visits
    `;

    // Start building the query
    let query = supabase
      .from("business_profiles")
      .select(businessFields, { count: "exact" })
      .eq("status", "online");

    // Add location filters if provided
    if (location.pincode) {
      query = query.eq("pincode", location.pincode);
    } else if (location.city) {
      query = query.eq("city", location.city); // Use exact matching for city
    } else if (location.state) {
      // State names are now standardized, so we can use exact matching
      query = query.eq("state", location.state);
    }

    // Add locality filter if provided
    if (location.locality) {
      query = query.ilike("locality", `%${location.locality}%`);
    }

    // Add sorting
    if (sortBy === "created_desc") {
      query = query.order("created_at", { ascending: false });
    } else if (sortBy === "created_asc") {
      query = query.order("created_at", { ascending: true });
    } else if (sortBy === "name_asc") {
      query = query.order("business_name", { ascending: true });
    } else if (sortBy === "name_desc") {
      query = query.order("business_name", { ascending: false });
    } else if (sortBy === "rating_desc") {
      query = query.order("average_rating", { ascending: false });
    } else if (sortBy === "likes_desc") {
      query = query.order("total_likes", { ascending: false });
    } else {
      // Default to created_desc
      query = query.order("created_at", { ascending: false });
    }

    // Add pagination
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    const { data, count, error } = await query;

    if (error) {
      console.error("Error fetching businesses by location:", error);
      return { error: "Database error fetching businesses." };
    }

    return {
      data: data as BusinessProfilePublicData[],
      count: count || 0,
    };
  } catch (error) {
    console.error(
      "Unexpected error in getSecureBusinessProfilesByLocation:",
      error
    );
    return { error: "An unexpected error occurred." };
  }
}
