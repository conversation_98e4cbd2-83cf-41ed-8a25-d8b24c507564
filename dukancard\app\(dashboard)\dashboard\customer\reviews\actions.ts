"use server";

import { reviewsService } from '@/lib/services/socialService';

export async function fetchCustomerReviews(
  userId: string,
  page: number = 1,
  limit: number = 10,
  sortBy: "newest" | "oldest" | "rating_high" | "rating_low" = "newest"
) {
  try {
    const result = await reviewsService.fetchReviews(userId, page, limit, sortBy);
    return { success: true, data: result };
  } catch (error) {
    console.error("Error in fetchCustomerReviews server action:", error);
    return { success: false, error: (error as Error).message };
  }
}
