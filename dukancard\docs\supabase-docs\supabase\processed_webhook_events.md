# Processed Webhook Events Table Documentation

## Table Overview

The `processed_webhook_events` table in the Dukancard application tracks webhook events that have been received and processed by the system. It serves as both a record of webhook processing and a mechanism to prevent duplicate processing of the same event, particularly for payment-related webhooks from Razorpay.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| event_id | text | NO | | Primary key, unique Razorpay event ID from x-razorpay-event-id header |
| processed_at | timestamptz | NO | now() | Timestamp when the event was processed |
| event_type | text | YES | | Type of webhook event (e.g., 'payment.authorized', 'subscription.charged') |
| entity_type | text | YES | | Type of entity (subscription, payment, refund, invoice) |
| entity_id | text | YES | | ID of the related entity (subscription_id, payment_id, etc.) |
| payload | jsonb | YES | | Complete JSON payload of the webhook event with metadata |
| status | text | YES | 'processed' | Status of the event processing ('processed', 'failed', 'retrying') |
| error_message | text | YES | | Error message if processing failed |
| retry_count | integer | YES | 0 | Number of times processing has been retried |
| notes | text | YES | | Additional notes or messages about the event processing |
| created_at | timestamptz | YES | now() | Timestamp when the record was created |

## Constraints

### Primary Key
- `processed_webhook_events_pkey` - Primary key constraint on the `event_id` column

### Not Null Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| processed_webhook_events_pkey | UNIQUE | event_id | Primary key index |
| idx_processed_webhook_events_entity | INDEX | entity_type, entity_id | Entity lookup index |
| idx_processed_webhook_events_created_at | INDEX | created_at | Cleanup operations index |

## Row Level Security (RLS) Policies

| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Enable read access for all users | SELECT | true | |

This policy ensures that all users can view webhook event records, but there are no policies for modifying the data (likely handled through server-side logic).

## Related Tables

### payment_subscriptions
While not directly linked via foreign key, the `processed_webhook_events` table is closely related to the `payment_subscriptions` table, as many webhook events will be related to subscription payments and status changes.

## Event ID Strategy (Updated)

**IMPORTANT**: The `event_id` field now uses the unique `x-razorpay-event-id` header from Razorpay webhooks for true idempotency.

### Current Implementation (Post-Fix)

- **All Events**: Uses the unique Razorpay event ID from the `x-razorpay-event-id` header (e.g., `evt_QaidkzzWhvADW9`)
- **Entity Tracking**: Separate `entity_type` and `entity_id` columns track the related entity
- **True Idempotency**: Each webhook event has a globally unique identifier

### Benefits of This Approach

1. **True Idempotency**: Uses Razorpay's unique event IDs, preventing any duplicate processing
2. **Multiple Events**: Same entity can have multiple different events processed correctly
3. **Better Tracking**: Separate entity fields for debugging and monitoring
4. **Race Condition Prevention**: Eliminates issues where events for same entity overwrite each other

### Example Event Processing

**For subscription `sub_QajNdvDsksZk1w`:**
- Event 1: `evt_001` → `subscription.authenticated` → Processed ✅
- Event 2: `evt_002` → `subscription.cancelled` → Processed ✅ (different event)
- Event 3: `evt_003` → `subscription.authenticated` → Processed ✅ (different event)
- Event 1 Retry: `evt_001` → `subscription.authenticated` → Skipped ✅ (duplicate)

### Legacy Implementation (Pre-Fix)

The previous implementation used entity IDs with event type prefixes, which caused issues:
- `subscription.activated_sub_QajNdvDsksZk1w`
- `subscription.charged_sub_QajNdvDsksZk1w`

This approach had problems with race conditions and duplicate processing.

## Race Condition Handling

### The Problem

When subscriptions reach terminal states in Razorpay, webhooks may arrive out of order or be sent unexpectedly:

**Immediate Cancellation Scenario:**
1. `subscription.cancelled` (processes cancellation, downgrades to free plan)
2. `subscription.updated` (arrives later, could overwrite cancellation state)

**Subscription Expiration Scenario:**
1. `subscription.expired` (processes expiration, downgrades to free plan)
2. `subscription.activated` (arrives later due to delayed processing, could reactivate expired subscription)

**Subscription Completion Scenario:**
1. `subscription.completed` (processes completion, downgrades to free plan)
2. `subscription.charged` (arrives later for final billing cycle, could reactivate completed subscription)

**General Race Condition:**
Any webhook event that would change subscription status could potentially overwrite a terminal state if not properly guarded.

### Protection Mechanisms

1. **Terminal State Check**: Before processing any subscription event, the system checks if the subscription is in a terminal state (cancelled, expired, or completed):
   ```typescript
   const { data: existingSubscription } = await adminClient
     .from("payment_subscriptions")
     .select("cancelled_at, plan_id, subscription_status")
     .eq("razorpay_subscription_id", subscriptionId)
     .maybeSingle();

   const isTerminalState = existingSubscription?.cancelled_at ||
                          existingSubscription?.plan_id === 'free' ||
                          existingSubscription?.subscription_status === "cancelled" ||
                          existingSubscription?.subscription_status === "expired" ||
                          existingSubscription?.subscription_status === "completed";

   if (isTerminalState) {
     return { success: true, message: "Subscription is in terminal state, ignoring event" };
   }
   ```

2. **Event Type Validation**: Subscription events that would reactivate a terminated subscription are ignored

3. **Database-Level Consistency**: Multiple fields serve as authoritative terminal state indicators:
   - `cancelled_at` timestamp (subscription was cancelled)
   - `plan_id = 'free'` (subscription was downgraded to free plan)
   - `subscription_status` in `['cancelled', 'expired', 'completed']` (subscription reached terminal state)

### Affected Event Handlers

The following subscription event handlers include terminal state checks:
- `handleSubscriptionAuthenticated` - Prevents authentication of terminated subscriptions
- `handleSubscriptionActivated` - Prevents activation of terminated subscriptions
- `handleSubscriptionCharged` - Prevents charge processing for terminated subscriptions
- `handleSubscriptionPending` - Prevents pending status updates for terminated subscriptions
- `handleSubscriptionUpdated` - Prevents any updates to terminated subscriptions

**Note**: The terminal state handlers (`handleSubscriptionCancelled`, `handleSubscriptionExpired`, `handleSubscriptionCompleted`) do not include these checks as they are responsible for setting the terminal state.

## Usage Notes

1. **Idempotency Mechanism**:
   - The table serves as an idempotency mechanism for webhook processing
   - By recording processed event IDs, the system can detect and skip duplicate webhook events
   - This is critical for payment systems where duplicate processing could lead to financial errors

2. **Event Tracking**:
   - The table stores the complete webhook payload for audit and debugging purposes
   - The `event_type` field categorizes events for easier filtering and analysis
   - The `processed_at` timestamp provides a chronological record of webhook activity
   - The `notes` field stores additional processing information and success messages

3. **Error Handling**:
   - The `status` field tracks the processing outcome ('processed', 'failed', 'retrying')
   - Failed events can be identified and investigated using the `error_message` field
   - The `retry_count` field supports a retry mechanism for transient failures

4. **Webhook Sources**:
   - Based on project memory, the primary webhook source is Razorpay payment gateway
   - Webhook events likely include payment authorizations, subscription charges, and subscription status changes
   - These events trigger updates to the `payment_subscriptions` table and business subscription status

5. **Subscription Cancellation Logic** (Updated):
   - **Authenticated Subscriptions**: When cancelled, revert to trial status with:
     - Selected plan preserved (plan_id)
     - Plan cycle enforced to 'monthly'
     - Razorpay IDs cleared (razorpay_subscription_id, razorpay_customer_id)
     - All other columns nulled except plan_id, plan_cycle, subscription_status, created_at, updated_at
   - **Active Subscriptions**: When cancelled, downgrade to free plan (existing behavior)
   - **Race Condition Protection**: Late authenticated webhooks are rejected if they arrive after cancellation
   - This ensures trial users can continue with their selected plan even after cancelling authenticated subscriptions

6. **Security**:
   - RLS policies allow read access to all users, but modification is likely restricted to server-side functions
   - This separation ensures that webhook processing is handled securely by the application logic

7. **Subscription Management**:
   - Based on project memory, subscription status changes are handled exclusively through Razorpay webhooks
   - When a webhook indicates a subscription has been cancelled:
     - **Authenticated subscriptions**: Revert to trial status preserving the selected plan with monthly cycle enforced
     - **Active subscriptions**: Downgrade to free plan
   - When expired or completed: Always downgrade to free plan
   - Razorpay subscription and customer IDs are cleared after webhook confirmation of cancellation

8. **Data Retention**:
   - The table may grow large over time as it stores all processed webhook events
   - A scheduled cron job (`cleanup-webhook-events`) runs weekly on Sundays at midnight to automatically clean up old events
   - The cleanup function (`cleanup_webhook_events()`) deletes:
     - Successfully processed events older than 30 days
     - Failed or retrying events older than 90 days
   - The function counts and logs the number of deleted records for monitoring purposes
   - This automated cleanup prevents excessive database growth while maintaining sufficient history for troubleshooting

9. **Race Condition Protection** (Enhanced):
   - **Webhook Sequence Validation**: Uses `last_webhook_timestamp` to prevent out-of-order processing
   - **Authenticated vs Cancelled Protection**: Special handling for late authenticated webhooks
   - **Tolerance Settings**:
     - Default: 5 seconds for normal webhooks
     - Strict: 30 seconds for authenticated webhooks on trial subscriptions
   - **Rejection Logic**: Late webhooks are rejected with detailed logging for debugging

10. **Debugging and Monitoring**:
    - The stored webhook payloads and processing status provide valuable data for debugging issues
    - Monitoring the table can help identify patterns of webhook failures or delays
    - Race condition rejections are logged with detailed timestamp comparisons

## Cleanup Function Implementation

The following SQL function is used by the cron job to clean up old webhook events:

```sql
CREATE OR REPLACE FUNCTION public.cleanup_webhook_events()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
  success_cutoff TIMESTAMP WITH TIME ZONE := NOW() - INTERVAL '30 days';
  failed_cutoff TIMESTAMP WITH TIME ZONE := NOW() - INTERVAL '90 days';
  deleted_success INTEGER;
  deleted_failed INTEGER;
BEGIN
  -- Delete successfully processed events older than 30 days
  WITH deleted AS (
    DELETE FROM processed_webhook_events
    WHERE status = 'processed' AND processed_at < success_cutoff
    RETURNING *
  )
  SELECT COUNT(*) INTO deleted_success FROM deleted;

  -- Delete failed events older than 90 days
  WITH deleted AS (
    DELETE FROM processed_webhook_events
    WHERE status IN ('failed', 'retrying') AND processed_at < failed_cutoff
    RETURNING *
  )
  SELECT COUNT(*) INTO deleted_failed FROM deleted;

  RAISE NOTICE 'Deleted % processed events and % failed events',
    deleted_success, deleted_failed;
END;
$function$
```

This function is scheduled to run weekly on Sundays at midnight via the following cron job:

```sql
SELECT cron.schedule(
  'cleanup-webhook-events',
  '0 0 * * 0',  -- Run at midnight every Sunday
  'SELECT cleanup_webhook_events();'
);
```
