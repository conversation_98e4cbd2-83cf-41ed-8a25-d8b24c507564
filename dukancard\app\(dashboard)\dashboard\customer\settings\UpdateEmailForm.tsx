'use client';

import React, { useEffect } from 'react';
import { useActionState } from 'react';  // Updated from useFormState
import { useFormStatus } from 'react-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { updateCustomerEmail, type LinkEmailFormState } from './actions';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

// Client-side schema
const UpdateEmailFormSchema = z.object({
  newEmail: z.string().email('Invalid email address.'),
});

type UpdateEmailFormData = z.infer<typeof UpdateEmailFormSchema>;

interface UpdateEmailFormProps {
  currentEmail: string | undefined; // Pass current email for display
}

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <Button type="submit" disabled={pending} aria-disabled={pending}>
      {pending ? 'Sending Confirmation...' : 'Update Email'}
    </Button>
  );
}

export function UpdateEmailForm({ currentEmail }: UpdateEmailFormProps) {
  const initialState: LinkEmailFormState = { message: null, errors: {}, success: false };
  const [state, dispatch] = useActionState(updateCustomerEmail, initialState);

  const form = useForm<UpdateEmailFormData>({
    resolver: zodResolver(UpdateEmailFormSchema),
    defaultValues: {
      newEmail: '', // Start with empty
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (state.success) {
      toast.success(state.message || 'Confirmation email sent!');
      form.reset(); // Reset form on success
    } else if (state.message && !state.success) {
      // Show general errors
      if (!state.errors || Object.keys(state.errors).length === 0) {
         toast.error(state.message);
      }
    }
  }, [state, form]);

  return (
    <form action={dispatch} className="space-y-4">
      {/* Display non-field specific errors */}
      {state.message && !state.success && (!state.errors || Object.keys(state.errors).length === 0) && (
         <p className="text-sm font-medium text-destructive">{state.message}</p>
       )}

      <p className="text-sm text-muted-foreground">
        Current Email: {currentEmail || 'Loading...'}
      </p>

      <div className="space-y-2">
        <Label htmlFor="newEmail">New Email Address</Label>
        <Input
          id="newEmail"
          type="email"
          {...form.register('newEmail')}
          aria-invalid={!!form.formState.errors.newEmail || !!state.errors?.email}
          aria-describedby="newEmail-error"
        />
        {/* Client-side validation error */}
        {form.formState.errors.newEmail && (
          <p id="newEmail-error" className="text-sm font-medium text-destructive">
            {form.formState.errors.newEmail.message}
          </p>
        )}
         {/* Server-side validation error */}
        {state.errors?.email && (
           <p id="newEmail-error-server" className="text-sm font-medium text-destructive">
             {state.errors.email.join(', ')}
           </p>
         )}
      </div>

      <SubmitButton />
       <p className="text-xs text-muted-foreground">
         You will receive confirmation emails at both your old and new addresses to complete the change.
       </p>
    </form>
  );
}
