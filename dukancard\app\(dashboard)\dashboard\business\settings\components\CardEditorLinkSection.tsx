"use client";

import { motion } from "framer-motion";
import { CreditCard, ExternalLink } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function CardEditorLinkSection() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.2 }}
      className="space-y-6"
    >
      {/* Section Header */}
      <div className="pb-6 border-b border-neutral-200/60 dark:border-neutral-700/60">
        <div className="flex items-center gap-3 mb-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
            <CreditCard className="w-4 h-4 text-primary" />
          </div>
          <h2 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
            Edit Digital Card Profile
          </h2>
        </div>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed">
          Update your business details, contact information, social links, and appearance on your public digital card.
        </p>
      </div>

      {/* Section Content */}
      <div className="flex justify-end">
        <Button
          asChild
          variant="outline"
          size="sm"
          className="px-4 py-2 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200"
        >
          <Link href="/dashboard/business/card" className="flex items-center">
            <CreditCard className="w-4 h-4 mr-2" />
            Go to Card Editor
            <ExternalLink className="w-4 h-4 ml-2" />
          </Link>
        </Button>
      </div>
    </motion.div>
  );
}
