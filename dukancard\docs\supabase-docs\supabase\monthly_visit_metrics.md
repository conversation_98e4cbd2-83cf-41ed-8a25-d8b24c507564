# Monthly Visit Metrics Table Documentation

## Table Overview

The `monthly_visit_metrics` table in the Dukancard application stores aggregated monthly visit data for business profiles. It tracks unique visitor counts on a monthly basis, providing businesses with historical analytics about their profile's performance over time.

## Table Schema

| Column Name | Data Type | Nullable | Default | Description |
|-------------|-----------|----------|---------|-------------|
| id | uuid | NO | gen_random_uuid() | Primary key for the monthly visit metric record |
| business_profile_id | uuid | NO | | Foreign key to business_profiles.id |
| year | integer | NO | | Year of the visit metrics (e.g., 2025) |
| month | integer | NO | | Month of the visit metrics (1-12) |
| unique_visits | integer | NO | 0 | Count of unique visitors for the month |
| created_at | timestamptz | NO | now() | Timestamp when the record was created |
| updated_at | timestamptz | NO | now() | Timestamp when the record was last updated |

## Constraints

### Primary Key
- `monthly_visit_metrics_pkey` - Primary key constraint on the `id` column

### Foreign Keys
- `monthly_visit_metrics_business_profile_id_fkey` - Foreign key constraint linking `business_profile_id` to `business_profiles.id`

### Unique Constraints
- `monthly_visit_metrics_business_profile_id_year_month_key` - Ensures only one record per business per month
  - Columns: `business_profile_id`, `year`, `month`

### Check Constraints
- `monthly_visit_metrics_month_check` - Ensures month values are between 1 and 12
  ```sql
  CHECK ((month >= 1) AND (month <= 12))
  ```

### Not Null Constraints
Multiple check constraints ensure that required fields are not null.

## Indexes

| Index Name | Type | Columns | Description |
|------------|------|---------|-------------|
| monthly_visit_metrics_pkey | UNIQUE | id | Primary key index |
| monthly_visit_metrics_business_profile_id_year_month_key | UNIQUE | business_profile_id, year, month | Ensures unique combination of business, year, and month |
| idx_monthly_visit_metrics_business_id | BTREE | business_profile_id | Index for faster business profile lookups |
| idx_monthly_visit_metrics_year_month | BTREE | year, month | Index for faster time-based queries |

## Row Level Security (RLS) Policies
 
| Policy Name | Command | Using Expression | With Check Expression |
|-------------|---------|------------------|------------------------|
| Users can view their own monthly visit metrics | SELECT | (auth.uid() = business_profile_id) | |
| Users can insert their own monthly visit metrics | INSERT | | |
| Users can update their own monthly visit metrics | UPDATE | (auth.uid() = business_profile_id) | |
| Users can delete their own monthly visit metrics | DELETE | (auth.uid() = business_profile_id) | |

These policies ensure that business owners can only access and modify visit metrics for their own business profiles.

## Related Tables

### business_profiles
The `business_profiles` table is referenced by the `business_profile_id` foreign key and contains information about the business that the visit metrics are for.

### card_visits
The `card_visits` table is related to `monthly_visit_metrics` through the `update_monthly_visit_counts()` trigger function, which populates this table with aggregated monthly data when new visits occur.

## Usage Notes

1. **Analytics Purpose**:
   - This table provides monthly aggregated visit data for business analytics
   - It enables businesses to track visitor trends over time
   - The data is used for generating monthly visit reports and charts

2. **Data Population**:
   - Records are primarily created and updated by the `update_monthly_visit_counts()` trigger on the `card_visits` table
   - When a new visit is recorded, the corresponding monthly metric is either created or updated
   - The unique constraint ensures there's only one record per business per month

3. **Time-Based Structure**:
   - Data is organized by year and month for efficient time-series analysis
   - The check constraint ensures month values are valid (1-12)
   - This structure allows for easy querying of specific time periods

4. **Security**:
   - RLS policies ensure that business owners can only access their own visit metrics
   - This protects sensitive analytics data from unauthorized access

5. **Performance Considerations**:
   - The table uses a composite index on business_profile_id, year, and month for efficient lookups
   - The separate index on year and month supports time-based queries across businesses
   - Pre-aggregating data in this table improves performance for analytics queries compared to calculating on-the-fly

6. **Business Intelligence**:
   - This table supports trend analysis and performance tracking
   - Businesses can compare visitor numbers across different months
   - The data can be used to measure the effectiveness of marketing campaigns or seasonal trends

7. **Subscription Tier Relevance**:
   - Based on the project's memory, access to monthly visit trends is restricted to Growth plan and above
   - This table stores the data regardless of subscription tier, but access is controlled at the application level

8. **Data Relationships**:
   - Each record represents one month of visit data for one business profile
   - The relationship to business_profiles is many-to-one (many months to one business)
   - The data is derived from the many individual visit records in the card_visits table
