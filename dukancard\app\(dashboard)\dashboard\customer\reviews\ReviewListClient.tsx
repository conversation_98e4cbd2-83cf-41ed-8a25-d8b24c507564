'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Star, Compass } from 'lucide-react';
import ReviewCard from '@/app/components/shared/reviews/ReviewCard';

// Re-define interfaces needed for props
interface BusinessProfileDataForReview {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: BusinessProfileDataForReview | null;
}

interface ReviewListClientProps {
  initialReviews: ReviewData[];
}

export default function ReviewListClient({ initialReviews }: ReviewListClientProps) {
  const [reviews, setReviews] = useState(initialReviews);

  const handleDeleteSuccess = (reviewIdToRemove: string) => {
    setReviews(currentReviews =>
      currentReviews.filter(review => review.id !== reviewIdToRemove)
    );
  };

  // Enhanced empty state for customer reviews (matching business component style)
  if (reviews.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-20 text-center">
        <div className="relative mb-8">
          <div className="absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"></div>
          <div className="relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg">
            <Star className="w-10 h-10 text-primary" />
          </div>
        </div>
        <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3">
          You haven&apos;t written any reviews yet
        </h3>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2">
          Share your experiences by reviewing businesses you&apos;ve visited.
        </p>
        <p className="text-sm text-neutral-500 dark:text-neutral-500 max-w-md leading-relaxed mb-8">
          Your reviews help other customers make informed decisions and support great businesses.
        </p>
        <Button asChild variant="outline" className="gap-2 px-6 py-2.5 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200">
          <Link href="/businesses" target="_blank" rel="noopener noreferrer">
            <Compass className="w-4 h-4" />
            Discover Businesses
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {reviews.map((review) => (
        <ReviewCard
          key={review.id}
          review={review}
          onDeleteSuccess={handleDeleteSuccess}
        />
      ))}
    </div>
  );
}
