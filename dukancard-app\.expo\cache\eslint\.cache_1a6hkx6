[{"C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx": "1", "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx": "2", "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx": "3", "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx": "4", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx": "5", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx": "6", "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx": "7", "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx": "8", "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts": "9", "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx": "10", "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx": "11", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx": "12", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx": "13", "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx": "14", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx": "15", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx": "16", "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx": "17", "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx": "18", "C:\\web-app\\dukancard-app\\src\\components\\common\\LoadingOverlay.tsx": "19", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\BusinessCard.tsx": "20", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CategorySelector.tsx": "21", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CompactLocationPicker.tsx": "22", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\DiscoverySkeletons.tsx": "23", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ErrorComponents.tsx": "24", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\FullScreenLocationSelector.tsx": "25", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\NavigationHandlers.tsx": "26", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ResultsList.tsx": "27", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SearchSection.tsx": "28", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SortBottomSheet.tsx": "29", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\CompactLocationPickerStyles.ts": "30", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\DiscoverScreenStyles.ts": "31", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\LocationSelectorScreenStyles.ts": "32", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\ResultsListStyles.ts": "33", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ViewToggle.tsx": "34", "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx": "35", "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx": "36", "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts": "37", "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts": "38", "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts": "39", "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts": "40", "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts": "41", "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts": "42", "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts": "43", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx": "44", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx": "45", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx": "46", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx": "47", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx": "48", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx": "49", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx": "50", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx": "51", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx": "52", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx": "53", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx": "54", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx": "55", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx": "56", "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx": "57", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx": "58", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx": "59", "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx": "60", "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx": "61", "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx": "62", "C:\\web-app\\dukancard-app\\src\\components\\index.ts": "63", "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx": "64", "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx": "65", "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts": "66", "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx": "67", "C:\\web-app\\dukancard-app\\src\\components\\layout\\SafeAreaWrapper.tsx": "68", "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx": "69", "C:\\web-app\\dukancard-app\\src\\components\\layout\\StatusBarManager.tsx": "70", "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx": "71", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessDeleteAccountModal.tsx": "72", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessFollowersModal.tsx": "73", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessLikesModal.tsx": "74", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessReviewsModal.tsx": "75", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessStatusSettingsModal.tsx": "76", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowersList.tsx": "77", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowingList.tsx": "78", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesGivenList.tsx": "79", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesList.tsx": "80", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsGivenList.tsx": "81", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsList.tsx": "82", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductForm.tsx": "83", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsList.tsx": "84", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsSortBottomSheet.tsx": "85", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantForm.tsx": "86", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantList.tsx": "87", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\GalleryModal.tsx": "88", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageCardModal.tsx": "89", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageProductsModal.tsx": "90", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AdvancedFeaturesSection.tsx": "91", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AppearanceSection.tsx": "92", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BasicInfoSection.tsx": "93", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BusinessDetailsSection.tsx": "94", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\ContactLocationSection.tsx": "95", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\SocialLinksSection.tsx": "96", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\StatusSettingsSection.tsx": "97", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ShareBusinessCardModal.tsx": "98", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\VariantModal.tsx": "99", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\FollowingList.tsx": "100", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\LikesList.tsx": "101", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsList.tsx": "102", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsSortBottomSheet.tsx": "103", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\EditProfileModal.tsx": "104", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\FollowingModal.tsx": "105", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\LikesModal.tsx": "106", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\ReviewsModal.tsx": "107", "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx": "108", "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx": "109", "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx": "110", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx": "111", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ColorPickerBottomSheet.tsx": "112", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx": "113", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx": "114", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\VariantTypeBottomSheet.tsx": "115", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx": "116", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx": "117", "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx": "118", "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx": "119", "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx": "120", "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx": "121", "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx": "122", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx": "123", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx": "124", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressInformationSection.tsx": "125", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx": "126", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadSection.tsx": "127", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx": "128", "C:\\web-app\\dukancard-app\\src\\components\\profile\\PersonalInformationSection.tsx": "129", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx": "130", "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx": "131", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx": "132", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx": "133", "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx": "134", "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx": "135", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx": "136", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx": "137", "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx": "138", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx": "139", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx": "140", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx": "141", "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx": "142", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.styles.ts": "143", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.tsx": "144", "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx": "145", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx": "146", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx": "147", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts": "148", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx": "149", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx": "150", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\SocialEmptyState.tsx": "151", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx": "152", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessFollowersModalSkeleton.tsx": "153", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessLikesModalSkeleton.tsx": "154", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessReviewsModalSkeleton.tsx": "155", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\FollowingModalSkeleton.tsx": "156", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\LikesModalSkeleton.tsx": "157", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ProductsModalSkeleton.tsx": "158", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ReviewsModalSkeleton.tsx": "159", "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx": "160", "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx": "161", "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx": "162", "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx": "163", "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx": "164", "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx": "165", "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx": "166", "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx": "167", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx": "168", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx": "169", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx": "170", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx": "171", "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts": "172", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx": "173", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx": "174", "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx": "175", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx": "176", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx": "177", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx": "178", "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts": "179", "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx": "180", "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts": "181", "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx": "182", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx": "183", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx": "184", "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts": "185", "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx": "186", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx": "187", "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts": "188", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx": "189", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx": "190", "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts": "191", "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts": "192", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx": "193", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx": "194", "C:\\web-app\\dukancard-app\\src\\components\\ui\\PostDeleteDialog.tsx": "195", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx": "196", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx": "197", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx": "198", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx": "199", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx": "200", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx": "201", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx": "202", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx": "203", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx": "204", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx": "205", "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts": "206", "C:\\web-app\\dukancard-app\\src\\config\\supabase\\constants.ts": "207", "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts": "208", "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts": "209", "C:\\web-app\\dukancard-app\\src\\constants\\predefinedVariants.ts": "210", "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx": "211", "C:\\web-app\\dukancard-app\\src\\contexts\\DiscoveryContext.tsx": "212", "C:\\web-app\\dukancard-app\\src\\contexts\\LocationContext.tsx": "213", "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx": "214", "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx": "215", "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx": "216", "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts": "217", "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts": "218", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts": "219", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts": "220", "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts": "221", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts": "222", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts": "223", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts": "224", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts": "225", "C:\\web-app\\dukancard-app\\src\\hooks\\useDebounce.ts": "226", "C:\\web-app\\dukancard-app\\src\\hooks\\useDynamicSafeArea.ts": "227", "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts": "228", "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts": "229", "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts": "230", "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts": "231", "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts": "232", "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts": "233", "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts": "234", "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts": "235", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\businessActions.ts": "236", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\DiscoveryService.ts": "237", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\index.ts": "238", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\locationActions.ts": "239", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\productActions.ts": "240", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\types.ts": "241", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\locationUtils.ts": "242", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\secureBusinessProfiles.ts": "243", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\sortMappings.ts": "244", "C:\\web-app\\dukancard-app\\src\\services\\locationStorageService.ts": "245", "C:\\web-app\\dukancard-app\\src\\types\\ad.ts": "246", "C:\\web-app\\dukancard-app\\src\\types\\auth.ts": "247", "C:\\web-app\\dukancard-app\\src\\types\\business\\analytics.ts": "248", "C:\\web-app\\dukancard-app\\src\\types\\business.ts": "249", "C:\\web-app\\dukancard-app\\src\\types\\components.ts": "250", "C:\\web-app\\dukancard-app\\src\\types\\discovery.ts": "251", "C:\\web-app\\dukancard-app\\src\\types\\gallery.ts": "252", "C:\\web-app\\dukancard-app\\src\\types\\index.ts": "253", "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts": "254", "C:\\web-app\\dukancard-app\\src\\types\\profile.ts": "255", "C:\\web-app\\dukancard-app\\src\\types\\screens.ts": "256", "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts": "257", "C:\\web-app\\dukancard-app\\src\\types\\ui.ts": "258", "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts": "259", "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts": "260", "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts": "261", "C:\\web-app\\dukancard-app\\src\\utils\\distanceCalculation.ts": "262", "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts": "263", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts": "264", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts": "265", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "266", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts": "267", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts": "268", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts": "269", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts": "270", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts": "271", "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts": "272", "C:\\web-app\\dukancard-app\\src\\utils\\imageCompression.ts": "273", "C:\\web-app\\dukancard-app\\src\\utils\\index.ts": "274", "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts": "275", "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts": "276", "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts": "277", "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts": "278", "C:\\web-app\\dukancard-app\\src\\utils\\sortMappings.ts": "279", "C:\\web-app\\dukancard-app\\src\\utils\\storage-paths.ts": "280", "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts": "281", "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts": "282", "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts": "283", "C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx": "284", "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx": "285", "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step1AvatarName.tsx": "286", "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step2AddressLocation.tsx": "287", "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx": "288", "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx": "289", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx": "290", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx": "291", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx": "292", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx": "293", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx": "294", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx": "295", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx": "296", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx": "297", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx": "298", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx": "299", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx": "300", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx": "301", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx": "302", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "303", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx": "304", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx": "305", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx": "306", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx": "307", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx": "308", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx": "309", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx": "310", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx": "311", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx": "312", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx": "313", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx": "314", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx": "315", "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx": "316", "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx": "317", "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx": "318", "C:\\web-app\\dukancard-app\\app\\+not-found.tsx": "319", "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx": "320", "C:\\web-app\\dukancard-app\\app\\index.tsx": "321", "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx": "322", "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx": "323", "C:\\web-app\\dukancard-app\\app\\_layout.tsx": "324"}, {"size": 6700, "mtime": 1753769441476, "results": "325", "hashOfConfig": "326"}, {"size": 2348, "mtime": 1753769441469, "results": "327", "hashOfConfig": "326"}, {"size": 11448, "mtime": 1753769441477, "results": "328", "hashOfConfig": "326"}, {"size": 7406, "mtime": 1753769441477, "results": "329", "hashOfConfig": "326"}, {"size": 3642, "mtime": 1753769441478, "results": "330", "hashOfConfig": "326"}, {"size": 9032, "mtime": 1753769441479, "results": "331", "hashOfConfig": "326"}, {"size": 9871, "mtime": 1753769441480, "results": "332", "hashOfConfig": "326"}, {"size": 1271, "mtime": 1753769441481, "results": "333", "hashOfConfig": "326"}, {"size": 966, "mtime": 1753769441487, "results": "334", "hashOfConfig": "326"}, {"size": 11914, "mtime": 1753769441481, "results": "335", "hashOfConfig": "326"}, {"size": 9940, "mtime": 1753769441482, "results": "336", "hashOfConfig": "326"}, {"size": 6948, "mtime": 1753769441483, "results": "337", "hashOfConfig": "326"}, {"size": 10670, "mtime": 1753769441484, "results": "338", "hashOfConfig": "326"}, {"size": 6195, "mtime": 1753769441484, "results": "339", "hashOfConfig": "326"}, {"size": 8770, "mtime": 1753769441485, "results": "340", "hashOfConfig": "326"}, {"size": 17174, "mtime": 1753769441486, "results": "341", "hashOfConfig": "326"}, {"size": 2174, "mtime": 1753769441486, "results": "342", "hashOfConfig": "326"}, {"size": 1283, "mtime": 1753769441470, "results": "343", "hashOfConfig": "326"}, {"size": 1425, "mtime": 1753769441487, "results": "344", "hashOfConfig": "326"}, {"size": 8520, "mtime": 1753769441488, "results": "345", "hashOfConfig": "326"}, {"size": 4430, "mtime": 1753769441489, "results": "346", "hashOfConfig": "326"}, {"size": 4617, "mtime": 1753769441490, "results": "347", "hashOfConfig": "326"}, {"size": 11891, "mtime": 1753769441491, "results": "348", "hashOfConfig": "326"}, {"size": 10501, "mtime": 1753769441492, "results": "349", "hashOfConfig": "326"}, {"size": 24989, "mtime": 1753769441493, "results": "350", "hashOfConfig": "326"}, {"size": 6509, "mtime": 1753769441493, "results": "351", "hashOfConfig": "326"}, {"size": 9389, "mtime": 1753769441494, "results": "352", "hashOfConfig": "326"}, {"size": 4869, "mtime": 1753769441495, "results": "353", "hashOfConfig": "326"}, {"size": 8673, "mtime": 1753769441496, "results": "354", "hashOfConfig": "326"}, {"size": 1130, "mtime": 1753769441497, "results": "355", "hashOfConfig": "326"}, {"size": 942, "mtime": 1753769441498, "results": "356", "hashOfConfig": "326"}, {"size": 3399, "mtime": 1753769441498, "results": "357", "hashOfConfig": "326"}, {"size": 919, "mtime": 1753769441499, "results": "358", "hashOfConfig": "326"}, {"size": 4801, "mtime": 1753769441496, "results": "359", "hashOfConfig": "326"}, {"size": 6175, "mtime": 1753769441471, "results": "360", "hashOfConfig": "326"}, {"size": 737, "mtime": 1753769441472, "results": "361", "hashOfConfig": "326"}, {"size": 203, "mtime": 1753769441500, "results": "362", "hashOfConfig": "326"}, {"size": 170, "mtime": 1753769441501, "results": "363", "hashOfConfig": "326"}, {"size": 203, "mtime": 1753769441502, "results": "364", "hashOfConfig": "326"}, {"size": 416, "mtime": 1753769441503, "results": "365", "hashOfConfig": "326"}, {"size": 246, "mtime": 1753769441503, "results": "366", "hashOfConfig": "326"}, {"size": 226, "mtime": 1753769441504, "results": "367", "hashOfConfig": "326"}, {"size": 219, "mtime": 1753769441505, "results": "368", "hashOfConfig": "326"}, {"size": 3552, "mtime": 1753769441506, "results": "369", "hashOfConfig": "326"}, {"size": 15793, "mtime": 1753769441507, "results": "370", "hashOfConfig": "326"}, {"size": 18465, "mtime": 1753769441508, "results": "371", "hashOfConfig": "326"}, {"size": 3413, "mtime": 1753769441509, "results": "372", "hashOfConfig": "326"}, {"size": 10739, "mtime": 1753769441510, "results": "373", "hashOfConfig": "326"}, {"size": 13902, "mtime": 1753769441510, "results": "374", "hashOfConfig": "326"}, {"size": 4745, "mtime": 1753769441511, "results": "375", "hashOfConfig": "326"}, {"size": 853, "mtime": 1753769441512, "results": "376", "hashOfConfig": "326"}, {"size": 26108, "mtime": 1753776515353, "results": "377", "hashOfConfig": "326"}, {"size": 10697, "mtime": 1753776809632, "results": "378", "hashOfConfig": "326"}, {"size": 8134, "mtime": 1753769441515, "results": "379", "hashOfConfig": "326"}, {"size": 17474, "mtime": 1753769441516, "results": "380", "hashOfConfig": "326"}, {"size": 11033, "mtime": 1753769441516, "results": "381", "hashOfConfig": "326"}, {"size": 16755, "mtime": 1753778944106, "results": "382", "hashOfConfig": "326"}, {"size": 2781, "mtime": 1753769441518, "results": "383", "hashOfConfig": "326"}, {"size": 3905, "mtime": 1753769441519, "results": "384", "hashOfConfig": "326"}, {"size": 582, "mtime": 1753769441472, "results": "385", "hashOfConfig": "326"}, {"size": 859, "mtime": 1753769441473, "results": "386", "hashOfConfig": "326"}, {"size": 1509, "mtime": 1753769441520, "results": "387", "hashOfConfig": "326"}, {"size": 1485, "mtime": 1753769441521, "results": "388", "hashOfConfig": "326"}, {"size": 5700, "mtime": 1753769441521, "results": "389", "hashOfConfig": "326"}, {"size": 9064, "mtime": 1753769441522, "results": "390", "hashOfConfig": "326"}, {"size": 622, "mtime": 1753769441526, "results": "391", "hashOfConfig": "326"}, {"size": 8493, "mtime": 1753769441523, "results": "392", "hashOfConfig": "326"}, {"size": 2168, "mtime": 1753769441524, "results": "393", "hashOfConfig": "326"}, {"size": 5487, "mtime": 1753769441524, "results": "394", "hashOfConfig": "326"}, {"size": 1159, "mtime": 1753769441525, "results": "395", "hashOfConfig": "326"}, {"size": 8171, "mtime": 1753769441526, "results": "396", "hashOfConfig": "326"}, {"size": 6109, "mtime": 1753769441528, "results": "397", "hashOfConfig": "326"}, {"size": 5648, "mtime": 1753769441528, "results": "398", "hashOfConfig": "326"}, {"size": 5564, "mtime": 1753769441529, "results": "399", "hashOfConfig": "326"}, {"size": 8387, "mtime": 1753769441530, "results": "400", "hashOfConfig": "326"}, {"size": 2192, "mtime": 1753769441531, "results": "401", "hashOfConfig": "326"}, {"size": 7308, "mtime": 1753769441535, "results": "402", "hashOfConfig": "326"}, {"size": 5929, "mtime": 1753769441536, "results": "403", "hashOfConfig": "326"}, {"size": 4758, "mtime": 1753769441537, "results": "404", "hashOfConfig": "326"}, {"size": 7604, "mtime": 1753769441538, "results": "405", "hashOfConfig": "326"}, {"size": 5547, "mtime": 1753769441538, "results": "406", "hashOfConfig": "326"}, {"size": 6986, "mtime": 1753769441539, "results": "407", "hashOfConfig": "326"}, {"size": 34339, "mtime": 1753769441540, "results": "408", "hashOfConfig": "326"}, {"size": 12556, "mtime": 1753769441541, "results": "409", "hashOfConfig": "326"}, {"size": 7800, "mtime": 1753769441542, "results": "410", "hashOfConfig": "326"}, {"size": 29492, "mtime": 1753769441543, "results": "411", "hashOfConfig": "326"}, {"size": 8103, "mtime": 1753769441543, "results": "412", "hashOfConfig": "326"}, {"size": 5906, "mtime": 1753769441531, "results": "413", "hashOfConfig": "326"}, {"size": 8217, "mtime": 1753769441532, "results": "414", "hashOfConfig": "326"}, {"size": 10266, "mtime": 1753769441533, "results": "415", "hashOfConfig": "326"}, {"size": 9194, "mtime": 1753769441544, "results": "416", "hashOfConfig": "326"}, {"size": 9143, "mtime": 1753769441545, "results": "417", "hashOfConfig": "326"}, {"size": 11142, "mtime": 1753769441546, "results": "418", "hashOfConfig": "326"}, {"size": 11553, "mtime": 1753769441547, "results": "419", "hashOfConfig": "326"}, {"size": 13954, "mtime": 1753769441548, "results": "420", "hashOfConfig": "326"}, {"size": 9759, "mtime": 1753769441548, "results": "421", "hashOfConfig": "326"}, {"size": 8299, "mtime": 1753769441549, "results": "422", "hashOfConfig": "326"}, {"size": 7233, "mtime": 1753769441534, "results": "423", "hashOfConfig": "326"}, {"size": 1368, "mtime": 1753769441534, "results": "424", "hashOfConfig": "326"}, {"size": 5068, "mtime": 1753769441553, "results": "425", "hashOfConfig": "326"}, {"size": 3956, "mtime": 1753769441554, "results": "426", "hashOfConfig": "326"}, {"size": 5312, "mtime": 1753769441555, "results": "427", "hashOfConfig": "326"}, {"size": 5891, "mtime": 1753769441555, "results": "428", "hashOfConfig": "326"}, {"size": 25452, "mtime": 1753769441550, "results": "429", "hashOfConfig": "326"}, {"size": 4008, "mtime": 1753769441551, "results": "430", "hashOfConfig": "326"}, {"size": 3663, "mtime": 1753769441552, "results": "431", "hashOfConfig": "326"}, {"size": 5533, "mtime": 1753769441553, "results": "432", "hashOfConfig": "326"}, {"size": 10941, "mtime": 1753769441556, "results": "433", "hashOfConfig": "326"}, {"size": 2793, "mtime": 1753769441557, "results": "434", "hashOfConfig": "326"}, {"size": 2106, "mtime": 1753769441473, "results": "435", "hashOfConfig": "326"}, {"size": 11262, "mtime": 1753769441558, "results": "436", "hashOfConfig": "326"}, {"size": 6314, "mtime": 1753769441560, "results": "437", "hashOfConfig": "326"}, {"size": 6366, "mtime": 1753769441560, "results": "438", "hashOfConfig": "326"}, {"size": 9679, "mtime": 1753769441561, "results": "439", "hashOfConfig": "326"}, {"size": 5984, "mtime": 1753769441562, "results": "440", "hashOfConfig": "326"}, {"size": 4230, "mtime": 1753769441563, "results": "441", "hashOfConfig": "326"}, {"size": 2684, "mtime": 1753769441564, "results": "442", "hashOfConfig": "326"}, {"size": 9968, "mtime": 1753776927252, "results": "443", "hashOfConfig": "326"}, {"size": 1575, "mtime": 1753769441566, "results": "444", "hashOfConfig": "326"}, {"size": 9527, "mtime": 1753769441567, "results": "445", "hashOfConfig": "326"}, {"size": 5586, "mtime": 1753769441568, "results": "446", "hashOfConfig": "326"}, {"size": 11655, "mtime": 1753769441569, "results": "447", "hashOfConfig": "326"}, {"size": 11518, "mtime": 1753769441570, "results": "448", "hashOfConfig": "326"}, {"size": 13102, "mtime": 1753769441571, "results": "449", "hashOfConfig": "326"}, {"size": 11196, "mtime": 1753769441571, "results": "450", "hashOfConfig": "326"}, {"size": 8857, "mtime": 1753769441572, "results": "451", "hashOfConfig": "326"}, {"size": 6441, "mtime": 1753769441573, "results": "452", "hashOfConfig": "326"}, {"size": 7340, "mtime": 1753769441574, "results": "453", "hashOfConfig": "326"}, {"size": 1991, "mtime": 1753769441574, "results": "454", "hashOfConfig": "326"}, {"size": 5037, "mtime": 1753769441575, "results": "455", "hashOfConfig": "326"}, {"size": 2500, "mtime": 1753769441576, "results": "456", "hashOfConfig": "326"}, {"size": 9974, "mtime": 1753769441577, "results": "457", "hashOfConfig": "326"}, {"size": 16789, "mtime": 1753769441578, "results": "458", "hashOfConfig": "326"}, {"size": 6697, "mtime": 1753769441579, "results": "459", "hashOfConfig": "326"}, {"size": 12415, "mtime": 1753769441580, "results": "460", "hashOfConfig": "326"}, {"size": 12074, "mtime": 1753769441581, "results": "461", "hashOfConfig": "326"}, {"size": 14058, "mtime": 1753769441581, "results": "462", "hashOfConfig": "326"}, {"size": 6400, "mtime": 1753769441585, "results": "463", "hashOfConfig": "326"}, {"size": 4276, "mtime": 1753769441586, "results": "464", "hashOfConfig": "326"}, {"size": 3025, "mtime": 1753769441587, "results": "465", "hashOfConfig": "326"}, {"size": 4287, "mtime": 1753769441588, "results": "466", "hashOfConfig": "326"}, {"size": 2417, "mtime": 1753769441582, "results": "467", "hashOfConfig": "326"}, {"size": 1634, "mtime": 1753769441583, "results": "468", "hashOfConfig": "326"}, {"size": 3452, "mtime": 1753769441584, "results": "469", "hashOfConfig": "326"}, {"size": 17959, "mtime": 1753769441589, "results": "470", "hashOfConfig": "326"}, {"size": 2325, "mtime": 1753769441590, "results": "471", "hashOfConfig": "326"}, {"size": 3529, "mtime": 1753769441591, "results": "472", "hashOfConfig": "326"}, {"size": 135, "mtime": 1753769441594, "results": "473", "hashOfConfig": "326"}, {"size": 2115, "mtime": 1753769441591, "results": "474", "hashOfConfig": "326"}, {"size": 10219, "mtime": 1753769441593, "results": "475", "hashOfConfig": "326"}, {"size": 6914, "mtime": 1753769441593, "results": "476", "hashOfConfig": "326"}, {"size": 1300, "mtime": 1753769441594, "results": "477", "hashOfConfig": "326"}, {"size": 2107, "mtime": 1753769441596, "results": "478", "hashOfConfig": "326"}, {"size": 2098, "mtime": 1753769441596, "results": "479", "hashOfConfig": "326"}, {"size": 2953, "mtime": 1753769441597, "results": "480", "hashOfConfig": "326"}, {"size": 2137, "mtime": 1753769441598, "results": "481", "hashOfConfig": "326"}, {"size": 2044, "mtime": 1753769441599, "results": "482", "hashOfConfig": "326"}, {"size": 4252, "mtime": 1753769441599, "results": "483", "hashOfConfig": "326"}, {"size": 2897, "mtime": 1753769441600, "results": "484", "hashOfConfig": "326"}, {"size": 4752, "mtime": 1753769441601, "results": "485", "hashOfConfig": "326"}, {"size": 8720, "mtime": 1753769441601, "results": "486", "hashOfConfig": "326"}, {"size": 2505, "mtime": 1753769441602, "results": "487", "hashOfConfig": "326"}, {"size": 6483, "mtime": 1753769441603, "results": "488", "hashOfConfig": "326"}, {"size": 4310, "mtime": 1753769441604, "results": "489", "hashOfConfig": "326"}, {"size": 4919, "mtime": 1753769441604, "results": "490", "hashOfConfig": "326"}, {"size": 803, "mtime": 1753769441474, "results": "491", "hashOfConfig": "326"}, {"size": 486, "mtime": 1753769441474, "results": "492", "hashOfConfig": "326"}, {"size": 8385, "mtime": 1753769441606, "results": "493", "hashOfConfig": "326"}, {"size": 1165, "mtime": 1753769441606, "results": "494", "hashOfConfig": "326"}, {"size": 8019, "mtime": 1753769441607, "results": "495", "hashOfConfig": "326"}, {"size": 2790, "mtime": 1753769441607, "results": "496", "hashOfConfig": "326"}, {"size": 146, "mtime": 1753769441625, "results": "497", "hashOfConfig": "326"}, {"size": 3070, "mtime": 1753769441608, "results": "498", "hashOfConfig": "326"}, {"size": 6869, "mtime": 1753769441609, "results": "499", "hashOfConfig": "326"}, {"size": 2437, "mtime": 1753769441610, "results": "500", "hashOfConfig": "326"}, {"size": 6521, "mtime": 1753769441610, "results": "501", "hashOfConfig": "326"}, {"size": 8512, "mtime": 1753769441611, "results": "502", "hashOfConfig": "326"}, {"size": 6960, "mtime": 1753769441612, "results": "503", "hashOfConfig": "326"}, {"size": 191, "mtime": 1753769441626, "results": "504", "hashOfConfig": "326"}, {"size": 8704, "mtime": 1753769441613, "results": "505", "hashOfConfig": "326"}, {"size": 154, "mtime": 1753769441627, "results": "506", "hashOfConfig": "326"}, {"size": 1212, "mtime": 1753769441613, "results": "507", "hashOfConfig": "326"}, {"size": 630, "mtime": 1753769441614, "results": "508", "hashOfConfig": "326"}, {"size": 1463, "mtime": 1753769441614, "results": "509", "hashOfConfig": "326"}, {"size": 164, "mtime": 1753769441627, "results": "510", "hashOfConfig": "326"}, {"size": 5488, "mtime": 1753769441615, "results": "511", "hashOfConfig": "326"}, {"size": 5792, "mtime": 1753769441615, "results": "512", "hashOfConfig": "326"}, {"size": 182, "mtime": 1753769441628, "results": "513", "hashOfConfig": "326"}, {"size": 4297, "mtime": 1753769441616, "results": "514", "hashOfConfig": "326"}, {"size": 4954, "mtime": 1753769441617, "results": "515", "hashOfConfig": "326"}, {"size": 103, "mtime": 1753769441629, "results": "516", "hashOfConfig": "326"}, {"size": 118, "mtime": 1753769441630, "results": "517", "hashOfConfig": "326"}, {"size": 2218, "mtime": 1753769441618, "results": "518", "hashOfConfig": "326"}, {"size": 5855, "mtime": 1753769441617, "results": "519", "hashOfConfig": "326"}, {"size": 1061, "mtime": 1753769441619, "results": "520", "hashOfConfig": "326"}, {"size": 6418, "mtime": 1753769441620, "results": "521", "hashOfConfig": "326"}, {"size": 4561, "mtime": 1753769441620, "results": "522", "hashOfConfig": "326"}, {"size": 3124, "mtime": 1753769441621, "results": "523", "hashOfConfig": "326"}, {"size": 3747, "mtime": 1753769441621, "results": "524", "hashOfConfig": "326"}, {"size": 17543, "mtime": 1753769441622, "results": "525", "hashOfConfig": "326"}, {"size": 2219, "mtime": 1753769441623, "results": "526", "hashOfConfig": "326"}, {"size": 566, "mtime": 1753769441623, "results": "527", "hashOfConfig": "326"}, {"size": 165, "mtime": 1753769441624, "results": "528", "hashOfConfig": "326"}, {"size": 5248, "mtime": 1753769441624, "results": "529", "hashOfConfig": "326"}, {"size": 8082, "mtime": 1753769441625, "results": "530", "hashOfConfig": "326"}, {"size": 2617, "mtime": 1753769441631, "results": "531", "hashOfConfig": "326"}, {"size": 2841, "mtime": 1753774315403, "results": "532", "hashOfConfig": "326"}, {"size": 962, "mtime": 1753769441632, "results": "533", "hashOfConfig": "326"}, {"size": 3021, "mtime": 1753769441633, "results": "534", "hashOfConfig": "326"}, {"size": 17737, "mtime": 1753769441634, "results": "535", "hashOfConfig": "326"}, {"size": 11758, "mtime": 1753769441635, "results": "536", "hashOfConfig": "326"}, {"size": 20619, "mtime": 1753769441636, "results": "537", "hashOfConfig": "326"}, {"size": 5249, "mtime": 1753769441637, "results": "538", "hashOfConfig": "326"}, {"size": 7415, "mtime": 1753769441638, "results": "539", "hashOfConfig": "326"}, {"size": 8309, "mtime": 1753769441639, "results": "540", "hashOfConfig": "326"}, {"size": 3053, "mtime": 1753769441639, "results": "541", "hashOfConfig": "326"}, {"size": 1882, "mtime": 1753769441640, "results": "542", "hashOfConfig": "326"}, {"size": 4833, "mtime": 1753769441641, "results": "543", "hashOfConfig": "326"}, {"size": 8177, "mtime": 1753769441642, "results": "544", "hashOfConfig": "326"}, {"size": 2162, "mtime": 1753769441643, "results": "545", "hashOfConfig": "326"}, {"size": 4524, "mtime": 1753769441644, "results": "546", "hashOfConfig": "326"}, {"size": 8324, "mtime": 1753769441644, "results": "547", "hashOfConfig": "326"}, {"size": 4309, "mtime": 1753769441645, "results": "548", "hashOfConfig": "326"}, {"size": 224, "mtime": 1753769441646, "results": "549", "hashOfConfig": "326"}, {"size": 501, "mtime": 1753769441646, "results": "550", "hashOfConfig": "326"}, {"size": 1272, "mtime": 1753769441647, "results": "551", "hashOfConfig": "326"}, {"size": 1703, "mtime": 1753769441647, "results": "552", "hashOfConfig": "326"}, {"size": 4891, "mtime": 1753769441648, "results": "553", "hashOfConfig": "326"}, {"size": 1849, "mtime": 1753769441649, "results": "554", "hashOfConfig": "326"}, {"size": 4438, "mtime": 1753769441649, "results": "555", "hashOfConfig": "326"}, {"size": 2447, "mtime": 1753769441650, "results": "556", "hashOfConfig": "326"}, {"size": 2412, "mtime": 1753769441650, "results": "557", "hashOfConfig": "326"}, {"size": 3949, "mtime": 1753769441651, "results": "558", "hashOfConfig": "326"}, {"size": 2356, "mtime": 1753769441652, "results": "559", "hashOfConfig": "326"}, {"size": 565, "mtime": 1753769441653, "results": "560", "hashOfConfig": "326"}, {"size": 6189, "mtime": 1753769441655, "results": "561", "hashOfConfig": "326"}, {"size": 18260, "mtime": 1753769441655, "results": "562", "hashOfConfig": "326"}, {"size": 170, "mtime": 1753769441656, "results": "563", "hashOfConfig": "326"}, {"size": 23023, "mtime": 1753769441657, "results": "564", "hashOfConfig": "326"}, {"size": 15441, "mtime": 1753769441657, "results": "565", "hashOfConfig": "326"}, {"size": 2980, "mtime": 1753769441658, "results": "566", "hashOfConfig": "326"}, {"size": 1671, "mtime": 1753769441659, "results": "567", "hashOfConfig": "326"}, {"size": 13486, "mtime": 1753769441660, "results": "568", "hashOfConfig": "326"}, {"size": 0, "mtime": 1753769441660, "results": "569", "hashOfConfig": "326"}, {"size": 12996, "mtime": 1753769441661, "results": "570", "hashOfConfig": "326"}, {"size": 410, "mtime": 1753769441662, "results": "571", "hashOfConfig": "326"}, {"size": 5821, "mtime": 1753769441662, "results": "572", "hashOfConfig": "326"}, {"size": 902, "mtime": 1753769441664, "results": "573", "hashOfConfig": "326"}, {"size": 785, "mtime": 1753769441663, "results": "574", "hashOfConfig": "326"}, {"size": 393, "mtime": 1753769441665, "results": "575", "hashOfConfig": "326"}, {"size": 8154, "mtime": 1753769441666, "results": "576", "hashOfConfig": "326"}, {"size": 149, "mtime": 1753769441666, "results": "577", "hashOfConfig": "326"}, {"size": 315, "mtime": 1753769441667, "results": "578", "hashOfConfig": "326"}, {"size": 351, "mtime": 1753769441667, "results": "579", "hashOfConfig": "326"}, {"size": 781, "mtime": 1753769441668, "results": "580", "hashOfConfig": "326"}, {"size": 303, "mtime": 1753769441669, "results": "581", "hashOfConfig": "326"}, {"size": 57483, "mtime": 1753769441670, "results": "582", "hashOfConfig": "326"}, {"size": 422, "mtime": 1753769441670, "results": "583", "hashOfConfig": "326"}, {"size": 4903, "mtime": 1753769441671, "results": "584", "hashOfConfig": "326"}, {"size": 8418, "mtime": 1753769441672, "results": "585", "hashOfConfig": "326"}, {"size": 4141, "mtime": 1753769441672, "results": "586", "hashOfConfig": "326"}, {"size": 4768, "mtime": 1753769441673, "results": "587", "hashOfConfig": "326"}, {"size": 12465, "mtime": 1753769441674, "results": "588", "hashOfConfig": "326"}, {"size": 3715, "mtime": 1753769441675, "results": "589", "hashOfConfig": "326"}, {"size": 5950, "mtime": 1753769441676, "results": "590", "hashOfConfig": "326"}, {"size": 9417, "mtime": 1753769441677, "results": "591", "hashOfConfig": "326"}, {"size": 141, "mtime": 1753769441678, "results": "592", "hashOfConfig": "326"}, {"size": 10403, "mtime": 1753769441678, "results": "593", "hashOfConfig": "326"}, {"size": 4149, "mtime": 1753769441679, "results": "594", "hashOfConfig": "326"}, {"size": 7483, "mtime": 1753769441680, "results": "595", "hashOfConfig": "326"}, {"size": 5633, "mtime": 1753769441680, "results": "596", "hashOfConfig": "326"}, {"size": 2746, "mtime": 1753769441681, "results": "597", "hashOfConfig": "326"}, {"size": 8387, "mtime": 1753769441682, "results": "598", "hashOfConfig": "326"}, {"size": 732, "mtime": 1753769441682, "results": "599", "hashOfConfig": "326"}, {"size": 6262, "mtime": 1753769441683, "results": "600", "hashOfConfig": "326"}, {"size": 1808, "mtime": 1753769441684, "results": "601", "hashOfConfig": "326"}, {"size": 3162, "mtime": 1753769441684, "results": "602", "hashOfConfig": "326"}, {"size": 4293, "mtime": 1753769441685, "results": "603", "hashOfConfig": "326"}, {"size": 1829, "mtime": 1753769441685, "results": "604", "hashOfConfig": "326"}, {"size": 6965, "mtime": 1753769441686, "results": "605", "hashOfConfig": "326"}, {"size": 1339, "mtime": 1753769441686, "results": "606", "hashOfConfig": "326"}, {"size": 2467, "mtime": 1753769441687, "results": "607", "hashOfConfig": "326"}, {"size": 8533, "mtime": 1753769441688, "results": "608", "hashOfConfig": "326"}, {"size": 10723, "mtime": 1753769441280, "results": "609", "hashOfConfig": "326"}, {"size": 27805, "mtime": 1753769441282, "results": "610", "hashOfConfig": "326"}, {"size": 4491, "mtime": 1753769441283, "results": "611", "hashOfConfig": "326"}, {"size": 7930, "mtime": 1753769441284, "results": "612", "hashOfConfig": "326"}, {"size": 27094, "mtime": 1753769441285, "results": "613", "hashOfConfig": "326"}, {"size": 2617, "mtime": 1753769441280, "results": "614", "hashOfConfig": "326"}, {"size": 2092, "mtime": 1753769441288, "results": "615", "hashOfConfig": "326"}, {"size": 3430, "mtime": 1753769441289, "results": "616", "hashOfConfig": "326"}, {"size": 7332, "mtime": 1753769441290, "results": "617", "hashOfConfig": "326"}, {"size": 2476, "mtime": 1753769441291, "results": "618", "hashOfConfig": "326"}, {"size": 16234, "mtime": 1753769441292, "results": "619", "hashOfConfig": "326"}, {"size": 2523, "mtime": 1753769441287, "results": "620", "hashOfConfig": "326"}, {"size": 5217, "mtime": 1753769441294, "results": "621", "hashOfConfig": "326"}, {"size": 7075, "mtime": 1753769441295, "results": "622", "hashOfConfig": "326"}, {"size": 7555, "mtime": 1753769441296, "results": "623", "hashOfConfig": "326"}, {"size": 6825, "mtime": 1753769441297, "results": "624", "hashOfConfig": "326"}, {"size": 7792, "mtime": 1753769441300, "results": "625", "hashOfConfig": "326"}, {"size": 6973, "mtime": 1753769441301, "results": "626", "hashOfConfig": "326"}, {"size": 2892, "mtime": 1753769441302, "results": "627", "hashOfConfig": "326"}, {"size": 6477, "mtime": 1753769441303, "results": "628", "hashOfConfig": "326"}, {"size": 15800, "mtime": 1753769441298, "results": "629", "hashOfConfig": "326"}, {"size": 7803, "mtime": 1753769441305, "results": "630", "hashOfConfig": "326"}, {"size": 2313, "mtime": 1753769441305, "results": "631", "hashOfConfig": "326"}, {"size": 5468, "mtime": 1753769441306, "results": "632", "hashOfConfig": "326"}, {"size": 2609, "mtime": 1753769441307, "results": "633", "hashOfConfig": "326"}, {"size": 4654, "mtime": 1753769441293, "results": "634", "hashOfConfig": "326"}, {"size": 760, "mtime": 1753769441286, "results": "635", "hashOfConfig": "326"}, {"size": 21125, "mtime": 1753769441310, "results": "636", "hashOfConfig": "326"}, {"size": 6062, "mtime": 1753769441311, "results": "637", "hashOfConfig": "326"}, {"size": 17024, "mtime": 1753769441312, "results": "638", "hashOfConfig": "326"}, {"size": 16748, "mtime": 1753769441313, "results": "639", "hashOfConfig": "326"}, {"size": 6083, "mtime": 1753769441309, "results": "640", "hashOfConfig": "326"}, {"size": 4878, "mtime": 1753769441315, "results": "641", "hashOfConfig": "326"}, {"size": 4770, "mtime": 1753769441316, "results": "642", "hashOfConfig": "326"}, {"size": 1410, "mtime": 1753769441314, "results": "643", "hashOfConfig": "326"}, {"size": 816, "mtime": 1753769441317, "results": "644", "hashOfConfig": "326"}, {"size": 5491, "mtime": 1753769441319, "results": "645", "hashOfConfig": "326"}, {"size": 1022, "mtime": 1753769441320, "results": "646", "hashOfConfig": "326"}, {"size": 2650, "mtime": 1753769441321, "results": "647", "hashOfConfig": "326"}, {"size": 26953, "mtime": 1753769441322, "results": "648", "hashOfConfig": "326"}, {"size": 6236, "mtime": 1753769441318, "results": "649", "hashOfConfig": "326"}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "151pwhz", {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1337", "messages": "1338", "suppressedMessages": "1339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1340", "messages": "1341", "suppressedMessages": "1342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1343", "messages": "1344", "suppressedMessages": "1345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1346", "messages": "1347", "suppressedMessages": "1348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1349", "messages": "1350", "suppressedMessages": "1351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1352", "messages": "1353", "suppressedMessages": "1354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1355", "messages": "1356", "suppressedMessages": "1357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1358", "messages": "1359", "suppressedMessages": "1360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1361", "messages": "1362", "suppressedMessages": "1363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1364", "messages": "1365", "suppressedMessages": "1366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1367", "messages": "1368", "suppressedMessages": "1369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1370", "messages": "1371", "suppressedMessages": "1372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1373", "messages": "1374", "suppressedMessages": "1375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1376", "messages": "1377", "suppressedMessages": "1378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1379", "messages": "1380", "suppressedMessages": "1381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1382", "messages": "1383", "suppressedMessages": "1384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1385", "messages": "1386", "suppressedMessages": "1387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1388", "messages": "1389", "suppressedMessages": "1390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1391", "messages": "1392", "suppressedMessages": "1393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1394", "messages": "1395", "suppressedMessages": "1396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1397", "messages": "1398", "suppressedMessages": "1399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1400", "messages": "1401", "suppressedMessages": "1402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1403", "messages": "1404", "suppressedMessages": "1405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1406", "messages": "1407", "suppressedMessages": "1408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1409", "messages": "1410", "suppressedMessages": "1411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1412", "messages": "1413", "suppressedMessages": "1414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1415", "messages": "1416", "suppressedMessages": "1417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1418", "messages": "1419", "suppressedMessages": "1420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1421", "messages": "1422", "suppressedMessages": "1423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1427", "messages": "1428", "suppressedMessages": "1429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1430", "messages": "1431", "suppressedMessages": "1432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1433", "messages": "1434", "suppressedMessages": "1435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1436", "messages": "1437", "suppressedMessages": "1438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1439", "messages": "1440", "suppressedMessages": "1441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1442", "messages": "1443", "suppressedMessages": "1444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1445", "messages": "1446", "suppressedMessages": "1447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1448", "messages": "1449", "suppressedMessages": "1450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1451", "messages": "1452", "suppressedMessages": "1453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1454", "messages": "1455", "suppressedMessages": "1456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1457", "messages": "1458", "suppressedMessages": "1459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1460", "messages": "1461", "suppressedMessages": "1462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1463", "messages": "1464", "suppressedMessages": "1465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1484", "messages": "1485", "suppressedMessages": "1486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1487", "messages": "1488", "suppressedMessages": "1489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1490", "messages": "1491", "suppressedMessages": "1492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1493", "messages": "1494", "suppressedMessages": "1495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1496", "messages": "1497", "suppressedMessages": "1498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1499", "messages": "1500", "suppressedMessages": "1501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1502", "messages": "1503", "suppressedMessages": "1504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1505", "messages": "1506", "suppressedMessages": "1507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1508", "messages": "1509", "suppressedMessages": "1510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1511", "messages": "1512", "suppressedMessages": "1513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1514", "messages": "1515", "suppressedMessages": "1516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1517", "messages": "1518", "suppressedMessages": "1519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1520", "messages": "1521", "suppressedMessages": "1522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1523", "messages": "1524", "suppressedMessages": "1525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1526", "messages": "1527", "suppressedMessages": "1528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1529", "messages": "1530", "suppressedMessages": "1531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1532", "messages": "1533", "suppressedMessages": "1534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1535", "messages": "1536", "suppressedMessages": "1537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1538", "messages": "1539", "suppressedMessages": "1540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1541", "messages": "1542", "suppressedMessages": "1543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1544", "messages": "1545", "suppressedMessages": "1546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1547", "messages": "1548", "suppressedMessages": "1549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1550", "messages": "1551", "suppressedMessages": "1552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1553", "messages": "1554", "suppressedMessages": "1555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1556", "messages": "1557", "suppressedMessages": "1558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1559", "messages": "1560", "suppressedMessages": "1561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1562", "messages": "1563", "suppressedMessages": "1564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1565", "messages": "1566", "suppressedMessages": "1567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1568", "messages": "1569", "suppressedMessages": "1570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1571", "messages": "1572", "suppressedMessages": "1573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1574", "messages": "1575", "suppressedMessages": "1576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1577", "messages": "1578", "suppressedMessages": "1579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1580", "messages": "1581", "suppressedMessages": "1582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1583", "messages": "1584", "suppressedMessages": "1585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1586", "messages": "1587", "suppressedMessages": "1588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1589", "messages": "1590", "suppressedMessages": "1591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1592", "messages": "1593", "suppressedMessages": "1594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1595", "messages": "1596", "suppressedMessages": "1597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1598", "messages": "1599", "suppressedMessages": "1600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1601", "messages": "1602", "suppressedMessages": "1603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1604", "messages": "1605", "suppressedMessages": "1606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1607", "messages": "1608", "suppressedMessages": "1609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1610", "messages": "1611", "suppressedMessages": "1612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1613", "messages": "1614", "suppressedMessages": "1615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1616", "messages": "1617", "suppressedMessages": "1618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1619", "messages": "1620", "suppressedMessages": "1621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\common\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\BusinessCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CategorySelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CompactLocationPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\DiscoverySkeletons.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ErrorComponents.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\FullScreenLocationSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\NavigationHandlers.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ResultsList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SearchSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SortBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\CompactLocationPickerStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\DiscoverScreenStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\LocationSelectorScreenStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\ResultsListStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ViewToggle.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx", [], ["1622", "1623"], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\SafeAreaWrapper.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\StatusBarManager.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessDeleteAccountModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessFollowersModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessLikesModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessReviewsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessStatusSettingsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowersList.tsx", [], ["1624", "1625"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowingList.tsx", [], ["1626", "1627"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesGivenList.tsx", [], ["1628", "1629"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesList.tsx", [], ["1630", "1631"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsGivenList.tsx", [], ["1632", "1633"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsList.tsx", [], ["1634"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsList.tsx", [], ["1635", "1636"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsSortBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\GalleryModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageCardModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageProductsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AdvancedFeaturesSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AppearanceSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BasicInfoSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BusinessDetailsSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\ContactLocationSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\SocialLinksSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\StatusSettingsSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ShareBusinessCardModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\VariantModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\FollowingList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\LikesList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsSortBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\EditProfileModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\FollowingModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\LikesModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\ReviewsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ColorPickerBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\VariantTypeBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressInformationSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\PersonalInformationSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.styles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\SocialEmptyState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessFollowersModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessLikesModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessReviewsModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\FollowingModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\LikesModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ProductsModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ReviewsModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\PostDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts", [], [], "C:\\web-app\\dukancard-app\\src\\config\\supabase\\constants.ts", [], [], "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts", [], [], "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts", [], [], "C:\\web-app\\dukancard-app\\src\\constants\\predefinedVariants.ts", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\DiscoveryContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\LocationContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useDebounce.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useDynamicSafeArea.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\businessActions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\DiscoveryService.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\locationActions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\productActions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\types.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\locationUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\secureBusinessProfiles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\locationStorageService.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ad.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\auth.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\business\\analytics.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\business.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\components.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\discovery.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\gallery.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\profile.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\screens.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ui.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\distanceCalculation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\imageCompression.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step1AvatarName.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step2AddressLocation.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\+not-found.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\_layout.tsx", [], [], {"ruleId": "1637", "severity": 1, "message": "1638", "line": 404, "column": 6, "nodeType": "1639", "endLine": 404, "endColumn": 56, "suggestions": "1640", "suppressions": "1641"}, {"ruleId": "1637", "severity": 1, "message": "1642", "line": 424, "column": 6, "nodeType": "1639", "endLine": 424, "endColumn": 69, "suggestions": "1643", "suppressions": "1644"}, {"ruleId": "1637", "severity": 1, "message": "1645", "line": 89, "column": 6, "nodeType": "1639", "endLine": 89, "endColumn": 30, "suggestions": "1646", "suppressions": "1647"}, {"ruleId": "1637", "severity": 1, "message": "1645", "line": 96, "column": 6, "nodeType": "1639", "endLine": 96, "endColumn": 12, "suggestions": "1648", "suppressions": "1649"}, {"ruleId": "1637", "severity": 1, "message": "1650", "line": 93, "column": 6, "nodeType": "1639", "endLine": 93, "endColumn": 24, "suggestions": "1651", "suppressions": "1652"}, {"ruleId": "1637", "severity": 1, "message": "1650", "line": 100, "column": 6, "nodeType": "1639", "endLine": 100, "endColumn": 12, "suggestions": "1653", "suppressions": "1654"}, {"ruleId": "1637", "severity": 1, "message": "1655", "line": 93, "column": 6, "nodeType": "1639", "endLine": 93, "endColumn": 24, "suggestions": "1656", "suppressions": "1657"}, {"ruleId": "1637", "severity": 1, "message": "1655", "line": 100, "column": 6, "nodeType": "1639", "endLine": 100, "endColumn": 12, "suggestions": "1658", "suppressions": "1659"}, {"ruleId": "1637", "severity": 1, "message": "1655", "line": 116, "column": 6, "nodeType": "1639", "endLine": 116, "endColumn": 30, "suggestions": "1660", "suppressions": "1661"}, {"ruleId": "1637", "severity": 1, "message": "1655", "line": 123, "column": 6, "nodeType": "1639", "endLine": 123, "endColumn": 12, "suggestions": "1662", "suppressions": "1663"}, {"ruleId": "1637", "severity": 1, "message": "1664", "line": 96, "column": 6, "nodeType": "1639", "endLine": 96, "endColumn": 32, "suggestions": "1665", "suppressions": "1666"}, {"ruleId": "1637", "severity": 1, "message": "1664", "line": 103, "column": 6, "nodeType": "1639", "endLine": 103, "endColumn": 12, "suggestions": "1667", "suppressions": "1668"}, {"ruleId": "1637", "severity": 1, "message": "1664", "line": 173, "column": 6, "nodeType": "1639", "endLine": 173, "endColumn": 38, "suggestions": "1669", "suppressions": "1670"}, {"ruleId": "1637", "severity": 1, "message": "1671", "line": 237, "column": 6, "nodeType": "1639", "endLine": 237, "endColumn": 12, "suggestions": "1672", "suppressions": "1673"}, {"ruleId": "1637", "severity": 1, "message": "1674", "line": 248, "column": 6, "nodeType": "1639", "endLine": 248, "endColumn": 32, "suggestions": "1675", "suppressions": "1676"}, "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'styles.loadingFooter' and 'styles.loadingText'. Either include them or remove the dependency array.", "ArrayExpression", ["1677"], ["1678"], "React Hook useCallback has missing dependencies: 'isLoading', 'styles.emptyContainer', 'styles.emptySubtitle', and 'styles.emptyTitle'. Either include them or remove the dependency array.", ["1679"], ["1680"], "React Hook useEffect has a missing dependency: 'fetchFollowers'. Either include it or remove the dependency array.", ["1681"], ["1682"], ["1683"], ["1684"], "React Hook useEffect has a missing dependency: 'fetchSubscriptions'. Either include it or remove the dependency array.", ["1685"], ["1686"], ["1687"], ["1688"], "React Hook useEffect has a missing dependency: 'fetchLikes'. Either include it or remove the dependency array.", ["1689"], ["1690"], ["1691"], ["1692"], ["1693"], ["1694"], ["1695"], ["1696"], "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["1697"], ["1698"], ["1699"], ["1700"], ["1701"], ["1702"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["1703"], ["1704"], "React Hook useEffect has missing dependencies: 'fetchProducts' and 'user'. Either include them or remove the dependency array.", ["1705"], ["1706"], {"desc": "1707", "fix": "1708"}, {"kind": "1709", "justification": "1710"}, {"desc": "1711", "fix": "1712"}, {"kind": "1709", "justification": "1710"}, {"desc": "1713", "fix": "1714"}, {"kind": "1709", "justification": "1710"}, {"desc": "1715", "fix": "1716"}, {"kind": "1709", "justification": "1710"}, {"desc": "1717", "fix": "1718"}, {"kind": "1709", "justification": "1710"}, {"desc": "1719", "fix": "1720"}, {"kind": "1709", "justification": "1710"}, {"desc": "1721", "fix": "1722"}, {"kind": "1709", "justification": "1710"}, {"desc": "1723", "fix": "1724"}, {"kind": "1709", "justification": "1710"}, {"desc": "1725", "fix": "1726"}, {"kind": "1709", "justification": "1710"}, {"desc": "1723", "fix": "1727"}, {"kind": "1709", "justification": "1710"}, {"desc": "1728", "fix": "1729"}, {"kind": "1709", "justification": "1710"}, {"desc": "1730", "fix": "1731"}, {"kind": "1709", "justification": "1710"}, {"desc": "1732", "fix": "1733"}, {"kind": "1709", "justification": "1710"}, {"desc": "1734", "fix": "1735"}, {"kind": "1709", "justification": "1710"}, {"desc": "1736", "fix": "1737"}, {"kind": "1709", "justification": "1710"}, "Update the dependencies array to be: [isLoading, hasMore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", {"range": "1738", "text": "1739"}, "directive", "", "Update the dependencies array to be: [isLoading, posts.length, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]", {"range": "1740", "text": "1741"}, "Update the dependencies array to be: [businessId, fetchFollowers, searchTerm]", {"range": "1742", "text": "1743"}, "Update the dependencies array to be: [fetchFollowers, page]", {"range": "1744", "text": "1745"}, "Update the dependencies array to be: [user, searchTerm, fetchSubscriptions]", {"range": "1746", "text": "1747"}, "Update the dependencies array to be: [fetchSubscriptions, page]", {"range": "1748", "text": "1749"}, "Update the dependencies array to be: [user, searchTerm, fetchLikes]", {"range": "1750", "text": "1751"}, "Update the dependencies array to be: [fetchLikes, page]", {"range": "1752", "text": "1753"}, "Update the dependencies array to be: [businessId, fetchLikes, searchTerm]", {"range": "1754", "text": "1755"}, {"range": "1756", "text": "1753"}, "Update the dependencies array to be: [user, sortBy, searchTerm, fetchReviews]", {"range": "1757", "text": "1758"}, "Update the dependencies array to be: [fetchReviews, page]", {"range": "1759", "text": "1760"}, "Update the dependencies array to be: [businessId, sortBy, searchTerm, fetchReviews]", {"range": "1761", "text": "1762"}, "Update the dependencies array to be: [fetchProducts, user]", {"range": "1763", "text": "1764"}, "Update the dependencies array to be: [activeSearchTerm, fetchProducts, sortBy, user]", {"range": "1765", "text": "1766"}, [12349, 12399], "[isLoading, has<PERSON>ore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", [13003, 13066], "[isLoading, posts.length, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]", [2852, 2876], "[businessId, fetchFollowers, searchTerm]", [3022, 3028], "[fetch<PERSON><PERSON><PERSON><PERSON>, page]", [3073, 3091], "[user, searchTerm, fetchSubscriptions]", [3241, 3247], "[fetchSubscriptions, page]", [2837, 2855], "[user, searchTerm, fetchLikes]", [2997, 3003], "[fetchLikes, page]", [3353, 3377], "[businessId, fetchLikes, searchTerm]", [3519, 3525], [2948, 2974], "[user, sortBy, searchTerm, fetchReviews]", [3118, 3124], "[fetchR<PERSON>ie<PERSON>, page]", [5350, 5382], "[businessId, sortBy, searchTerm, fetchReviews]", [7511, 7517], "[fetchProducts, user]", [7809, 7835], "[activeSearchTerm, fetchProducts, sortBy, user]"]