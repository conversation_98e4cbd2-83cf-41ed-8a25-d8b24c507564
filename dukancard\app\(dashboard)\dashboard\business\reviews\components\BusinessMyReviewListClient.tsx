"use client";

import { useCallback, useEffect, useState } from "react";
import { fetchMyReviews } from "../actions";
import ReviewSortDropdown, { ReviewSortOption } from "@/app/components/shared/reviews/ReviewSortDropdown";
import { Pagination, PaginationContent, Pagin<PERSON>E<PERSON>psis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Star } from "lucide-react";
import ReviewCardSkeleton from "@/app/components/shared/reviews/ReviewCardSkeleton";
import ReviewCard from "@/app/components/shared/reviews/ReviewCard";
import {motion} from "framer-motion"

interface BusinessProfileDataForReview {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: BusinessProfileDataForReview | null;
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  perPage: number;
}

export default function BusinessMyReviewListClient() {
  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [sortBy, setSortBy] = useState<ReviewSortOption>("newest");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    perPage: 10
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  // Fetch reviews from the API
  const fetchReviews = useCallback(async (page: number, sort: ReviewSortOption) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`[BusinessMyReviewListClient] Fetching reviews with page: ${page}, sort: ${sort}`);
      const result = await fetchMyReviews(
        "current_user_id", // Placeholder for current user ID
        page,
        pagination.perPage,
        sort
      );

      if (result.success && result.data) {
        console.log(`[BusinessMyReviewListClient] Received ${result.data.items.length} reviews, totalCount: ${result.data.totalCount}`);
        setReviews(result.data.items as ReviewData[]);
        setPagination({
          currentPage: result.data.currentPage,
          totalPages: Math.ceil(result.data.totalCount / pagination.perPage),
          totalCount: result.data.totalCount,
          perPage: pagination.perPage,
        });
      } else {
        setError(result.error || 'Failed to fetch reviews');
        setReviews([]);
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalCount: 0,
          perPage: pagination.perPage,
        });
      }
    } catch (_err) {
      setError('Failed to load reviews. Please try again.');
      setReviews([]);
    }
  }, [pagination.perPage]);

  // Fetch reviews when page or sort changes
  useEffect(() => {
    fetchReviews(pagination.currentPage, sortBy);
  }, [pagination.currentPage, sortBy, fetchReviews]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  // Handle sort change
  const handleSortChange = (newSortBy: ReviewSortOption) => {
    setSortBy(newSortBy);
    // Reset to first page when sorting changes
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };



  // Handle review deletion
  const handleDeleteSuccess = (reviewId: string) => {
    setReviews(prevReviews => prevReviews.filter(r => r.id !== reviewId));

    // If we deleted the last review on the page, go to previous page
    if (reviews.length === 1 && pagination.currentPage > 1) {
      handlePageChange(pagination.currentPage - 1);
    } else {
      // Refresh the current page
      fetchReviews(pagination.currentPage, sortBy);
    }
  };

  // Generate pagination items
  const renderPaginationItems = () => {
    const { currentPage, totalPages } = pagination;
    const items = [];

    // Always show first page
    items.push(
      <PaginationItem key="first">
        <PaginationLink
          href="#"
          onClick={(e) => {
            e.preventDefault();
            handlePageChange(1);
          }}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );

    // Show ellipsis if needed
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Show current page and surrounding pages
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i === 1 || i === totalPages) continue; // Skip first and last page as they're always shown
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(i);
            }}
            isActive={currentPage === i}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    // Show ellipsis if needed
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Always show last page if there's more than one page
    if (totalPages > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(totalPages);
            }}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    <div className="space-y-8">
      {/* Enhanced Sort Controls */}
      <div className="flex items-center justify-between p-6 bg-neutral-50/50 dark:bg-neutral-900/20 rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50">
        <div className="flex items-center gap-3">
          <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Sort your reviews by:
          </span>
        </div>
        <ReviewSortDropdown
          sortBy={sortBy}
          onSortChange={handleSortChange}
          className="w-[180px]"
        />
      </div>



      {/* Error message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Enhanced Loading state */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <ReviewCardSkeleton key={index} index={index} />
          ))}
        </div>
      ) : reviews.length > 0 ? (
        <>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {reviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
                onDeleteSuccess={handleDeleteSuccess}
              />
            ))}
          </motion.div>

          {/* Enhanced Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center pt-8 border-t border-neutral-200/60 dark:border-neutral-700/60">
              <Pagination>
                <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (pagination.currentPage > 1) {
                        handlePageChange(pagination.currentPage - 1);
                      }
                    }}
                    className={pagination.currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>

                {renderPaginationItems()}

                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (pagination.currentPage < pagination.totalPages) {
                        handlePageChange(pagination.currentPage + 1);
                      }
                    }}
                    className={pagination.currentPage === pagination.totalPages ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-20 text-center">
          <div className="relative mb-8">
            <div className="absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"></div>
            <div className="relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg">
              <Star className="w-10 h-10 text-primary" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3">
            No reviews written yet
          </h3>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2">
            You haven&apos;t written any reviews for other businesses yet.
          </p>
          <p className="text-sm text-neutral-500 dark:text-neutral-500 max-w-md leading-relaxed">
            Share your experiences by reviewing businesses you&apos;ve visited.
          </p>
        </div>
      )}
    </div>
  );
}
