"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { motion } from "framer-motion";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { Loader2, Mail, Shield, CheckCircle } from "lucide-react";
import { sendEmailChangeOTP, confirmEmailChangeOTP, updateCustomerEmailWithOTP, updateCustomerEmail } from "../actions";

// Schemas
const EmailSchema = z.object({
  email: z.string().email('Invalid email address.'),
});

const VerifyOTPSchema = z.object({
  otp: z.string().min(6, 'OTP must be 6 digits.').max(6, 'OTP must be 6 digits.'),
});

interface EmailUpdateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentEmail?: string | null;
  onEmailUpdated?: () => void;
}

export default function EmailUpdateDialog({
  isOpen,
  onClose,
  currentEmail,
  onEmailUpdated,
}: EmailUpdateDialogProps) {
  const [isPending, startTransition] = useTransition();
  const [step, setStep] = useState<'email_input' | 'otp_verification' | 'new_email_input'>('email_input');
  const [emailForOTP, setEmailForOTP] = useState('');
  const [_newEmailToChange, setNewEmailToChange] = useState('');
  const [message, setMessage] = useState('');

  // Form for email input
  const emailForm = useForm<z.infer<typeof EmailSchema>>({
    resolver: zodResolver(EmailSchema),
    defaultValues: { email: '' },
  });

  // Form for OTP verification
  const otpForm = useForm<z.infer<typeof VerifyOTPSchema>>({
    resolver: zodResolver(VerifyOTPSchema),
    defaultValues: { otp: '' },
  });

  // Reset dialog state when closed
  const handleClose = () => {
    setStep('email_input');
    setEmailForOTP('');
    setNewEmailToChange('');
    setMessage('');
    emailForm.reset();
    otpForm.reset();
    onClose();
  };

  // Handle email submission for scenario 1 (existing email) - send OTP to current email
  const handleSendOTPToCurrentEmail = () => {
    startTransition(async () => {
      try {
        const otpResult = await sendEmailChangeOTP();
        if (otpResult.success) {
          setEmailForOTP(otpResult.email || currentEmail || '');
          setStep('otp_verification');
          setMessage(`We&apos;ve sent a 6-digit verification code to ${otpResult.email || currentEmail}.`);
          toast.success("Verification code sent to your current email!");
        } else {
          toast.error(otpResult.message || "Failed to send verification code.");
          setMessage(otpResult.message || "Failed to send verification code.");
        }
      } catch (error) {
        toast.error("An unexpected error occurred.");
        console.error(error);
      }
    });
  };

  // Handle email submission for scenario 2 (no existing email) - link new email
  const onEmailSubmit = (data: z.infer<typeof EmailSchema>) => {
    startTransition(async () => {
      try {
        const formData = new FormData();
        formData.append('newEmail', data.email);
        
        const result = await updateCustomerEmail({ message: null, success: false }, formData);
        
        if (result.success) {
          if (result.message === 'otp_sent') {
            // OTP was sent to the new email
            setEmailForOTP(data.email);
            setStep('otp_verification');
            setMessage(`We&apos;ve sent a 6-digit verification code to ${data.email}.`);
            toast.success("Verification code sent!");
          } else {
            // Email was linked directly
            toast.success("Email linked successfully!");
            handleClose();
            onEmailUpdated?.();
          }
        } else {
          toast.error(result.message || "Failed to link email.");
          setMessage(result.message || "Failed to link email.");
        }
      } catch (error) {
        toast.error("An unexpected error occurred.");
        console.error(error);
      }
    });
  };

  // Handle OTP verification
  const onOTPSubmit = (data: z.infer<typeof VerifyOTPSchema>) => {
    startTransition(async () => {
      try {
        const formData = new FormData();
        formData.append('email', emailForOTP);
        formData.append('otp', data.otp);

        const otpResult = await confirmEmailChangeOTP({
          email: emailForOTP,
          otp: data.otp,
        }, formData);

        if (otpResult.success) {
          if (currentEmail) {
            // Scenario 1: User had existing email, now show new email input
            setStep('new_email_input');
            setMessage("OTP verified! Now enter your new email address.");
            toast.success("OTP verified! Enter your new email.");
          } else {
            // Scenario 2: User had no email, email is now linked
            toast.success("Email verified and linked successfully!");
            handleClose();
            onEmailUpdated?.();
          }
        } else {
          toast.error(otpResult.message || "Invalid OTP. Please try again.");
          setMessage(otpResult.message || "Invalid OTP. Please try again.");
        }
      } catch (error) {
        toast.error("An unexpected error occurred.");
        console.error(error);
      }
    });
  };

  // Handle new email submission (scenario 1 continuation)
  const onNewEmailSubmit = (data: z.infer<typeof EmailSchema>) => {
    startTransition(async () => {
      try {
        const formData = new FormData();
        formData.append('email', data.email);

        const updateResult = await updateCustomerEmailWithOTP({
          message: null,
          success: false,
        }, formData);

        if (updateResult.success) {
          toast.success("Email updated successfully!");
          handleClose();
          onEmailUpdated?.();
        } else {
          toast.error(updateResult.message || "Failed to update email.");
          setMessage(updateResult.message || "Failed to update email.");
        }
      } catch (error) {
        toast.error("An unexpected error occurred.");
        console.error(error);
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-md" hideClose={false}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5 text-primary" />
            Update Email Address
          </DialogTitle>
          <DialogDescription>
            {currentEmail 
              ? "Verify your current email first, then set a new email address."
              : "Link an email address to your account for better security."
            }
          </DialogDescription>
        </DialogHeader>

        <motion.div
          key={step}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.2 }}
          className="space-y-6"
        >
          {/* Step 1: Email Input or Current Email Verification */}
          {step === 'email_input' && (
            <div className="space-y-4">
              {currentEmail ? (
                // Scenario 1: User has existing email
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        Security Verification Required
                      </span>
                    </div>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      To change your email address, we need to verify your current email: <strong>{currentEmail}</strong>
                    </p>
                  </div>
                  <Button 
                    onClick={handleSendOTPToCurrentEmail}
                    disabled={isPending}
                    className="w-full"
                  >
                    {isPending ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Sending Code...
                      </>
                    ) : (
                      <>
                        <Mail className="w-4 h-4 mr-2" />
                        Send Verification Code
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                // Scenario 2: User has no email
                <Form {...emailForm}>
                  <form onSubmit={emailForm.handleSubmit(onEmailSubmit)} className="space-y-4">
                    <FormField
                      control={emailForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Enter your email address"
                              {...field}
                              disabled={isPending}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type="submit" disabled={isPending} className="w-full">
                      {isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Linking Email...
                        </>
                      ) : (
                        <>
                          <Mail className="w-4 h-4 mr-2" />
                          Link Email Address
                        </>
                      )}
                    </Button>
                  </form>
                </Form>
              )}
            </div>
          )}

          {/* Step 2: OTP Verification */}
          {step === 'otp_verification' && (
            <div className="space-y-4">
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-full">
                  <Mail className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Check Your Email</h3>
                <p className="text-sm text-muted-foreground">
                  We&apos;ve sent a 6-digit code to <strong>{emailForOTP}</strong>
                </p>
              </div>

              <Form {...otpForm}>
                <form onSubmit={otpForm.handleSubmit(onOTPSubmit)} className="space-y-4">
                  <FormField
                    control={otpForm.control}
                    name="otp"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Verification Code</FormLabel>
                        <FormControl>
                          <div className="flex justify-center">
                            <InputOTP maxLength={6} {...field} disabled={isPending}>
                              <InputOTPGroup>
                                <InputOTPSlot index={0} />
                                <InputOTPSlot index={1} />
                                <InputOTPSlot index={2} />
                                <InputOTPSlot index={3} />
                                <InputOTPSlot index={4} />
                                <InputOTPSlot index={5} />
                              </InputOTPGroup>
                            </InputOTP>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Verify Code
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </div>
          )}

          {/* Step 3: New Email Input (only for scenario 1) */}
          {step === 'new_email_input' && (
            <div className="space-y-4">
              <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-900 dark:text-green-100">
                    Current Email Verified
                  </span>
                </div>
                <p className="text-sm text-green-700 dark:text-green-300">
                  Current email: <strong>{currentEmail}</strong>
                </p>
              </div>

              <Form {...emailForm}>
                <form onSubmit={emailForm.handleSubmit(onNewEmailSubmit)} className="space-y-4">
                  <FormField
                    control={emailForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>New Email Address</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="Enter your new email address"
                            {...field}
                            disabled={isPending}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Updating Email...
                      </>
                    ) : (
                      <>
                        <Mail className="w-4 h-4 mr-2" />
                        Update Email Address
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </div>
          )}

          {/* Message Display */}
          {message && (
            <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <p className="text-sm text-blue-700 dark:text-blue-300">{message}</p>
            </div>
          )}
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
