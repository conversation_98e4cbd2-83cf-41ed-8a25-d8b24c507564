import { Page } from '@playwright/test';

/**
 * Authentication test utilities for E2E testing
 * These utilities allow us to test different authentication states
 * without relying on real Supabase authentication
 */

export interface TestAuthState {
  isAuthenticated: boolean;
  userType?: 'customer' | 'business';
  hasProfile?: boolean;
  businessSlug?: string;
  planId?: 'free' | 'basic' | 'premium';
}

/**
 * Set authentication state for testing
 * This works with our middleware test environment bypass
 */
export async function setTestAuthState(page: Page, authState: TestAuthState) {
  const headers: Record<string, string> = {};

  if (authState.isAuthenticated) {
    headers['x-test-auth-state'] = 'authenticated';
    headers['x-test-has-profile'] = authState.hasProfile ? 'true' : 'false';

    if (authState.userType) {
      headers['x-test-user-type'] = authState.userType;
    }

    if (authState.businessSlug) {
      headers['x-test-business-slug'] = authState.businessSlug;
    }

    if (authState.planId) {
      headers['x-test-plan-id'] = authState.planId;
    }
  } else {
    headers['x-test-auth-state'] = 'unauthenticated';
  }

  // Set headers for all subsequent requests
  await page.setExtraHTTPHeaders(headers);
}

/**
 * Predefined authentication states for common test scenarios
 */
export const AuthStates = {
  // User not logged in
  UNAUTHENTICATED: {
    isAuthenticated: false,
  },
  
  // User logged in but no profile (first time user)
  AUTHENTICATED_NO_PROFILE: {
    isAuthenticated: true,
    hasProfile: false,
  },
  
  // User logged in with customer profile
  AUTHENTICATED_CUSTOMER: {
    isAuthenticated: true,
    hasProfile: true,
    userType: 'customer' as const,
  },

  // User logged in with business profile (completed onboarding)
  AUTHENTICATED_BUSINESS: {
    isAuthenticated: true,
    hasProfile: true,
    userType: 'business' as const,
    businessSlug: 'test-business',
    planId: 'free' as const,
  },

  // User logged in with business profile (needs onboarding)
  AUTHENTICATED_BUSINESS_NO_SLUG: {
    isAuthenticated: true,
    hasProfile: true,
    userType: 'business' as const,
    // No businessSlug - will trigger onboarding redirect
  },

  // User logged in with premium business profile
  AUTHENTICATED_BUSINESS_PREMIUM: {
    isAuthenticated: true,
    hasProfile: true,
    userType: 'business' as const,
    businessSlug: 'premium-business',
    planId: 'premium' as const,
  },
} as const;

/**
 * Mock Supabase API responses for testing
 * This ensures consistent test data
 */
export async function mockSupabaseResponses(page: Page, authState: TestAuthState) {
  // Mock auth user endpoint
  await page.route('**/auth/v1/user', async (route) => {
    if (authState.isAuthenticated) {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        json: {
          id: 'test-user-id',
          aud: 'authenticated',
          role: 'authenticated',
          email: '<EMAIL>',
          user_metadata: { name: 'Test User' },
          created_at: '2024-01-01T00:00:00Z'
        }
      });
    } else {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        json: { error: 'Unauthorized' }
      });
    }
  });

  // Mock profile endpoints based on user type
  await page.route('**/rest/v1/customer_profiles*', async (route) => {
    const hasCustomerProfile = authState.hasProfile && authState.userType === 'customer';
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      json: hasCustomerProfile ? [{ id: 'test-user-id', user_id: 'test-user-id' }] : []
    });
  });

  await page.route('**/rest/v1/business_profiles*', async (route) => {
    const hasBusinessProfile = authState.hasProfile && authState.userType === 'business';
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      json: hasBusinessProfile ? [{ 
        id: 'test-user-id', 
        user_id: 'test-user-id',
        business_slug: 'test-business',
        trial_end_date: null,
        has_active_subscription: true
      }] : []
    });
  });
}

/**
 * Setup complete test authentication environment
 * Combines middleware bypass and API mocking
 */
export async function setupTestAuth(page: Page, authState: TestAuthState) {
  // Set authentication state for middleware
  await setTestAuthState(page, authState);
  
  // Mock API responses
  await mockSupabaseResponses(page, authState);
}
