"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { createClient } from "@/utils/supabase/client";
import type { RealtimePostgresChangesPayload } from "@supabase/supabase-js";
import { formatIndianNumberShort } from "@/lib/utils";
import { Heart, Star, Users } from "lucide-react";
import AnimatedMetricCard from "./AnimatedMetricCard";

/**
 * Dashboard Overview Client Component
 *
 * This component displays real-time metrics for a business profile:
 * - Total Likes
 * - Total Subscriptions
 * - Average Rating
 *
 * Database Implementation:
 * - All metrics are stored as pre-aggregated values in the business_profiles table
 * - These values are automatically updated by database triggers:
 *   1. update_total_likes() - Updates total_likes when likes are added/removed
 *   2. update_total_subscriptions() - Updates total_subscriptions when subscriptions change
 *   3. update_average_rating() - Updates average_rating when ratings change
 *
 * Real-time Updates:
 * - The component listens for changes to the business_profiles table
 * - When changes occur, the UI updates automatically with animations
 * - This approach is efficient for handling large numbers of likes/subscriptions
 */

// Define types for our data
interface BusinessProfile {
  business_name: string;
  total_likes: number;
  total_subscriptions: number;
  average_rating: number;
}

interface DashboardOverviewClientProps {
  initialProfile: BusinessProfile;
  userId: string;
  userPlan: string | null;
}

export default function DashboardOverviewClient({
  initialProfile,
  userId,
}: DashboardOverviewClientProps) {
  // State for real-time data
  const [profile, setProfile] = useState<BusinessProfile>(initialProfile);
  const [isLikesUpdated, setIsLikesUpdated] = useState(false);
  const [isSubscriptionsUpdated, setIsSubscriptionsUpdated] = useState(false);
  const [isRatingUpdated, setIsRatingUpdated] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  /**
   * Set up Supabase real-time subscriptions
   *
   * This effect sets up a real-time subscription to the business_profiles table
   * to listen for changes to the metrics (total_likes, total_subscriptions, average_rating).
   *
   * When changes are detected:
   * 1. The UI is updated with the new values
   * 2. Animations are triggered to highlight the changes
   *
   * Note: We only listen to the business_profiles table because:
   * - The metrics are pre-aggregated in this table
   * - Database triggers (update_total_likes, update_total_subscriptions, update_average_rating)
   *   automatically update these values when the underlying data changes
   * - This approach is more efficient than listening to individual tables or counting records
   */
  useEffect(() => {
    const supabase = createClient();

    const dashboardChannel = supabase
      .channel("business-dashboard")
      .on<BusinessProfile>(
        "postgres_changes" as const,
        {
          event: "*",
          schema: "public",
          table: "business_profiles",
          filter: `id=eq.${userId}`,
        },
        (payload: RealtimePostgresChangesPayload<BusinessProfile>) => {
          const newData = payload.new as BusinessProfile;
          if (newData) {
            // Check which values have changed and trigger animations accordingly
            if (newData.total_likes !== profile.total_likes) {
              setIsLikesUpdated(true);
              setTimeout(() => setIsLikesUpdated(false), 1000);
            }

            if (newData.total_subscriptions !== profile.total_subscriptions) {
              setIsSubscriptionsUpdated(true);
              setTimeout(() => setIsSubscriptionsUpdated(false), 1000);
            }

            if (newData.average_rating !== profile.average_rating) {
              setIsRatingUpdated(true);
              setTimeout(() => setIsRatingUpdated(false), 1000);
            }

            // Update all profile data
            setProfile((prev) => ({
              ...prev,
              total_likes: newData.total_likes,
              total_subscriptions: newData.total_subscriptions,
              average_rating: newData.average_rating,
            }));
          }
        }
      )
      .subscribe();

    // Cleanup function
    return () => {
      supabase.removeChannel(dashboardChannel);
    };
  }, [userId, profile]);

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Section Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
            Performance Overview
          </div>
          <div className="h-px flex-1 bg-gradient-to-r from-neutral-200 to-transparent dark:from-neutral-700" />
        </div>
        <p className="text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed">
          Real-time insights into your business performance and customer engagement metrics.
        </p>
      </div>

      {/* Hero Stats Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Likes Card */}
        <AnimatedMetricCard
          title="Total Likes"
          value={formatIndianNumberShort(profile.total_likes)}
          icon={Heart}
          description="People who liked your business card"
          color="red"
          isUpdated={isLikesUpdated}
        />

        {/* Subscribers Card */}
        <AnimatedMetricCard
          title="Subscribers"
          value={formatIndianNumberShort(profile.total_subscriptions)}
          icon={Users}
          description="People subscribed to your updates"
          color="blue"
          isUpdated={isSubscriptionsUpdated}
        />

        {/* Rating Card */}
        <AnimatedMetricCard
          title="Average Rating"
          value={`${profile.average_rating?.toFixed(1) || "0.0"}/5.0`}
          icon={Star}
          description="Customer satisfaction rating"
          color="yellow"
          isUpdated={isRatingUpdated}
        />
      </div>
    </motion.div>
  );
}
