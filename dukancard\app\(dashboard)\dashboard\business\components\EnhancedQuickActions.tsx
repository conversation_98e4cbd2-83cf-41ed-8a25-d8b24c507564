"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ard, Package, Heart, Bell } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

interface EnhancedQuickActionsProps {
  userPlan: string | null;
}

export default function EnhancedQuickActions({ userPlan: _userPlan }: EnhancedQuickActionsProps) {
  return (
    <div
      className="group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6 h-full"
    >
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40" />

      <div className="relative z-10">
        <div className="flex items-center justify-between gap-3 mb-6">
          <div className="space-y-1">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
                <CreditCard className="w-5 h-5 text-primary" />
              </div>
              <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
              <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
                Management
              </div>
            </div>
            <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
              Quick Actions
            </h3>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-emerald-500" />
            <span className="text-xs font-medium text-emerald-600 dark:text-emerald-400">Ready</span>
          </div>
        </div>

        <div className="flex flex-col gap-3 overflow-hidden">
          <Button
            asChild
            variant="outline"
            className="w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium"
          >
            <Link href="/dashboard/business/card" className="flex items-center gap-3">
              <CreditCard className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              <span className="text-sm">Edit Business Card</span>
            </Link>
          </Button>

          <Button
            asChild
            variant="outline"
            className="w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium"
          >
            <Link href="/dashboard/business/products" className="flex items-center gap-3">
              <Package className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              <span className="text-sm">Manage Products</span>
            </Link>
          </Button>


          <Button
            asChild
            variant="outline"
            className="w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium"
          >
            <Link href="/dashboard/business/analytics" className="flex items-center gap-3">
              <BarChart3 className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              <span className="text-sm">View Analytics</span>
            </Link>
          </Button>

          <Button
            asChild
            variant="outline"
            className="w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium"
          >
            <Link href="/dashboard/business/likes" className="flex items-center gap-3">
              <Heart className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              <span className="text-sm">My Likes</span>
            </Link>
          </Button>

          <Button
            asChild
            variant="outline"
            className="w-full justify-start border-neutral-200/60 dark:border-neutral-700/60 hover:border-primary/50 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200 py-3 px-4 h-auto font-medium"
          >
            <Link href="/dashboard/business/subscriptions" className="flex items-center gap-3">
              <Bell className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              <span className="text-sm">My Subscriptions</span>
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
