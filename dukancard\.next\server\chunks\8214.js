"use strict";exports.id=8214,exports.ids=[8214],exports.modules={1759:(e,r,t)=>{t.d(r,{Fc:()=>l,TN:()=>d,XL:()=>u});var a=t(37413);t(61120);var s=t(75986);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=s.$;var c=t(66819);let n=((e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return o(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:c}=r,n=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],a=null==c?void 0:c[e];if(null===r)return null;let o=i(r)||i(a);return s[e][o]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,a]=r;return void 0===a||(e[t]=a),e},{});return o(e,n,null==r||null==(a=r.compoundVariants)?void 0:a.reduce((e,r)=>{let{class:t,className:a,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...c,...l}[r]):({...c,...l})[r]===t})?[...e,t,a]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,...t}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,c.cn)(n({variant:r}),e),...t})}function u({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,c.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...r})}function d({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,c.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r})}},17931:(e,r,t)=>{t.d(r,{FD:()=>n,NF:()=>i,NM:()=>o,bD:()=>c,kn:()=>l,yJ:()=>u});var a=t(67218);t(79130);var s=t(32032);async function i(e){if(!e)return{error:"User ID is required."};try{let r=await (0,s.createClient)(),{data:t,error:a}=await r.from("customer_profiles_public").select("*").eq("id",e).maybeSingle();if(a)return console.error("Secure Fetch Error:",a),{error:`Failed to fetch customer profile: ${a.message}`};if(!t)return{error:"Profile not found."};return{data:{id:t.id,name:t.name,email:null,avatar_url:t.avatar_url,created_at:t.created_at,updated_at:t.updated_at}}}catch(e){return console.error("Exception in getSecureCustomerProfileById:",e),{error:"An unexpected error occurred."}}}async function o(e){if(!e||0===e.length)return{data:[]};try{let r=await (0,s.createClient)(),{data:t,error:a}=await r.from("customer_profiles_public").select("id, name, avatar_url, created_at, updated_at").in("id",e);if(a)return console.error("Secure Fetch Error:",a),{error:`Failed to fetch customer profiles: ${a.message}`};return{data:t?.map(e=>({...e,email:null}))||[]}}catch(e){return console.error("Exception in getSecureCustomerProfilesByIds:",e),{error:"An unexpected error occurred."}}}async function c(){try{let e=await (0,s.createClient)(),{data:r,error:t}=await e.from("customer_profiles_public").select("id, name, avatar_url, created_at, updated_at");if(t)return console.error("Secure Fetch Error:",t),{error:`Failed to fetch customer profiles: ${t.message}`};return{data:r?.map(e=>({...e,email:null}))||[]}}catch(e){return console.error("Exception in getAllSecureCustomerProfiles:",e),{error:"An unexpected error occurred."}}}async function n(e){if(!e)return{hasAccess:!1,error:"User ID is required."};try{let r=await (0,s.createClient)(),{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return{hasAccess:!1,error:"User not authenticated."};if(t.id!==e)return{hasAccess:!1,error:"Unauthorized access attempt."};let{data:i,error:o}=await r.from("customer_profiles").select("id").eq("id",e).maybeSingle();if(o)return console.error("Profile Access Check Error:",o),{hasAccess:!1,error:"Database error checking access."};return{hasAccess:!!i}}catch(e){return console.error("Exception in checkUserCustomerProfileAccess:",e),{hasAccess:!1,error:"An unexpected error occurred."}}}async function l(e){if(!e)return{error:"User ID is required."};try{let r=await (0,s.createClient)(),{data:t,error:a}=await r.from("customer_profiles_public").select("id, name, avatar_url").eq("id",e).maybeSingle();if(a)return console.error("Error fetching customer profile:",a),{error:`Failed to fetch customer profile: ${a.message}`};if(t)return{data:{id:t.id,name:t.name,avatar_url:t.avatar_url,is_business:!1}};let{data:i,error:o}=await r.from("business_profiles").select("id, business_name, logo_url").eq("id",e).maybeSingle();if(o)return console.error("Error fetching business profile:",o),{error:`Failed to fetch business profile: ${o.message}`};if(i)return{data:{id:i.id,name:i.business_name,avatar_url:i.logo_url,is_business:!0}};return{error:"User profile not found in either customer or business profiles."}}catch(e){return console.error("Exception in getUserProfileForReview:",e),{error:"An unexpected error occurred."}}}async function u(e){if(!e||0===e.length)return{data:{}};try{let r=await (0,s.createClient)(),{data:t,error:a}=await r.from("customer_profiles_public").select("id, name, avatar_url").in("id",e);if(a)return console.error("Error fetching customer profiles:",a),{error:`Failed to fetch customer profiles: ${a.message}`};let{data:i,error:o}=await r.from("business_profiles").select("id, business_name, logo_url").in("id",e);if(o)return console.error("Error fetching business profiles:",o),{error:`Failed to fetch business profiles: ${o.message}`};let c={};return t?.forEach(e=>{c[e.id]={id:e.id,name:e.name,avatar_url:e.avatar_url,is_business:!1}}),i?.forEach(e=>{c[e.id]||(c[e.id]={id:e.id,name:e.business_name,avatar_url:e.logo_url,is_business:!0})}),{data:c}}catch(e){return console.error("Exception in getUserProfilesForReviews:",e),{error:"An unexpected error occurred."}}}(0,t(17478).D)([i,o,c,n,l,u]),(0,a.A)(i,"40a0351c194e7be86652be597078a17f88ce61ffd9",null),(0,a.A)(o,"4045449fa6b768c246e7c02bd4f71eb87f687a51ce",null),(0,a.A)(c,"00a4c331ed038a2cf419ceb9f8cf4c288598ef18ce",null),(0,a.A)(n,"40f357684af86a0f2f85ef8627317e327164c711c5",null),(0,a.A)(l,"40a77e32b94b97a35280f6414b7ac009f51c69138b",null),(0,a.A)(u,"40775c08651ef90378e889b36dc9807f43121be87b",null)},22637:(e,r,t)=>{t.d(r,{Qc:()=>c,Ws:()=>o});var a=t(32032),s=t(30468),i=t(17931);let o={async fetchSubscriptions(e,r=1,t=20,i=""){try{let o=await (0,a.createClient)(),c=o.from(s.CG.SUBSCRIPTIONS).select(`
          ${s.cZ.ID},
          ${s.CG.BUSINESS_PROFILES}!inner (
            ${s.cZ.ID},
            ${s.cZ.BUSINESS_NAME}
          )
        `,{count:"exact",head:!0}).eq(s.cZ.USER_ID,e);i&&i.trim()&&(c=c.ilike(`${s.CG.BUSINESS_PROFILES}.${s.cZ.BUSINESS_NAME}`,`%${i.trim()}%`));let{count:n,error:l}=await c;if(l)throw Error(`Failed to get subscriptions count: ${l.message}`);if(!n||0===n)return{items:[],totalCount:0,hasMore:!1,currentPage:r};let u=o.from(s.CG.SUBSCRIPTIONS).select(`
          ${s.cZ.ID},
          ${s.cZ.BUSINESS_PROFILE_ID},
          ${s.CG.BUSINESS_PROFILES}!inner (*)
        `).eq(s.cZ.USER_ID,e);i&&i.trim()&&(u=u.ilike(`${s.CG.BUSINESS_PROFILES}.${s.cZ.BUSINESS_NAME}`,`%${i.trim()}%`));let d=(r-1)*t;u=u.range(d,d+t-1);let{data:S,error:f}=await u;if(f)throw Error(`Failed to fetch subscriptions: ${f.message}`);let _=(S||[]).map(e=>({id:e.id,business_profiles:Array.isArray(e.business_profiles)?e.business_profiles[0]:e.business_profiles})),m=n>d+t;return{items:_,totalCount:n,hasMore:m,currentPage:r}}catch(e){throw console.error("Error in fetchSubscriptions:",e),e}},async unsubscribe(e){try{let r=await (0,a.createClient)(),{error:t}=await r.from(s.CG.SUBSCRIPTIONS).delete().eq(s.cZ.ID,e);if(t)throw Error(`Failed to unsubscribe: ${t.message}`)}catch(e){throw console.error("Error in unsubscribe:",e),e}},async fetchBusinessFollowers(e,r=1,t=10){try{let o=await (0,a.createClient)(),c=(r-1)*t,{count:n,error:l}=await o.from(s.CG.SUBSCRIPTIONS).select("*",{count:"exact",head:!0}).eq(s.cZ.BUSINESS_PROFILE_ID,e);if(l)throw Error("Failed to fetch subscription count");if(!n||0===n)return{items:[],totalCount:0,hasMore:!1,currentPage:r};let{data:u,error:d}=await o.from(s.CG.SUBSCRIPTIONS).select(`${s.cZ.ID}, ${s.cZ.USER_ID}, ${s.cZ.CREATED_AT}`).eq(s.cZ.BUSINESS_PROFILE_ID,e).order(s.cZ.CREATED_AT,{ascending:!1}).range(c,c+t-1);if(d)throw Error("Failed to fetch subscriptions");if(!u||0===u.length)return{items:[],totalCount:n||0,hasMore:!1,currentPage:r};let S=u.map(e=>e.user_id),[f,_]=await Promise.all([(0,i.NM)(S),o.from(s.CG.BUSINESS_PROFILES).select(`${s.cZ.ID}, ${s.cZ.BUSINESS_NAME}, ${s.cZ.BUSINESS_SLUG}, ${s.cZ.LOGO_URL}, ${s.cZ.CITY}, ${s.cZ.STATE}, ${s.cZ.PINCODE}, ${s.cZ.ADDRESS_LINE}`).in(s.cZ.ID,S)]);if(f.error)throw Error("Failed to fetch customer profiles");if(_.error)throw Error("Failed to fetch business profiles");let m=new Map(f.data?.map(e=>[e.id,e])||[]),E=new Map(_.data?.map(e=>[e.id,e])||[]),I=u.map(e=>{let r=m.get(e.user_id),t=E.get(e.user_id);return r?{id:e.id,profile:{id:r.id,name:r.name,slug:null,avatar_url:r.avatar_url,city:null,state:null,pincode:null,address_line:null,type:"customer"}}:t?{id:e.id,profile:{id:t.id,name:t.business_name,slug:t.business_slug,logo_url:t.logo_url,city:t.city,state:t.state,pincode:t.pincode,address_line:t.address_line,type:"business"}}:null}).filter(e=>null!==e),p=n>c+t;return{items:I,totalCount:n||0,hasMore:p,currentPage:r}}catch(e){throw e}}},c={async fetchLikes(e,r=1,t=20,i=""){try{let o=await (0,a.createClient)(),c=o.from(s.CG.LIKES).select(`
          ${s.cZ.ID},
          ${s.CG.BUSINESS_PROFILES}!inner (*)
        `).eq(s.cZ.USER_ID,e);i&&i.trim()&&(c=c.ilike(`${s.CG.BUSINESS_PROFILES}.${s.cZ.BUSINESS_NAME}`,`%${i.trim()}%`));let n=o.from(s.CG.LIKES).select(`
          ${s.cZ.ID},
          ${s.CG.BUSINESS_PROFILES}!inner (
            ${s.cZ.ID},
            ${s.cZ.BUSINESS_NAME}
          )
        `,{count:"exact",head:!0}).eq(s.cZ.USER_ID,e);i&&i.trim()&&(n=n.ilike(`${s.CG.BUSINESS_PROFILES}.${s.cZ.BUSINESS_NAME}`,`%${i.trim()}%`));let{count:l,error:u}=await n;if(u)throw Error(`Failed to get likes count: ${u.message}`);if(!l||0===l)return{items:[],totalCount:0,hasMore:!1,currentPage:r};let d=(r-1)*t;c=c.range(d,d+t-1);let{data:S,error:f}=await c;if(f)throw Error(`Failed to fetch likes: ${f.message}`);let _=(S||[]).map(e=>({id:e.id,business_profiles:Array.isArray(e.business_profiles)?e.business_profiles[0]:e.business_profiles})),m=l>d+t;return{items:_,totalCount:l,hasMore:m,currentPage:r}}catch(e){throw console.error("Error in fetchLikes:",e),e}},async unlike(e){try{let r=await (0,a.createClient)(),{error:t}=await r.from(s.CG.LIKES).delete().eq(s.cZ.ID,e);if(t)throw Error(`Failed to unlike: ${t.message}`)}catch(e){throw console.error("Error in unlike:",e),e}},async fetchBusinessLikesReceived(e,r=1,t=10){try{let o=await (0,a.createClient)(),{count:c,error:n}=await o.from(s.CG.LIKES).select(s.cZ.ID,{count:"exact",head:!0}).eq(s.cZ.BUSINESS_PROFILE_ID,e);if(n)throw Error("Failed to get total count");if(!c||0===c)return{items:[],totalCount:0,hasMore:!1,currentPage:r};let l=(r-1)*t,{data:u,error:d}=await o.from(s.CG.LIKES).select(`${s.cZ.ID}, ${s.cZ.USER_ID}`).eq(s.cZ.BUSINESS_PROFILE_ID,e).order(s.cZ.CREATED_AT,{ascending:!1}).range(l,l+t-1);if(d)throw Error("Failed to fetch likes");if(!u||0===u.length)return{items:[],totalCount:c,hasMore:!1,currentPage:r};let S=u.map(e=>e.user_id),[f,_]=await Promise.all([(0,i.NM)(S),o.from(s.CG.BUSINESS_PROFILES).select(`${s.cZ.ID}, ${s.cZ.BUSINESS_NAME}, ${s.cZ.BUSINESS_SLUG}, ${s.cZ.LOGO_URL}, ${s.cZ.CITY}, ${s.cZ.STATE}, ${s.cZ.PINCODE}, ${s.cZ.ADDRESS_LINE}, ${s.cZ.LOCALITY}`).in(s.cZ.ID,S)]),m=new Map(f.data?.map(e=>[e.id,e])||[]),E=new Map(_.data?.map(e=>[e.id,e])||[]),I=u.map(e=>{let r=m.get(e.user_id),t=E.get(e.user_id);return r?{id:e.id,user_id:e.user_id,customer_profiles:r,profile_type:"customer"}:t?{id:e.id,user_id:e.user_id,business_profiles:t,profile_type:"business"}:null}).filter(e=>null!==e),p=c>l+t;return{items:I,totalCount:c,hasMore:p,currentPage:r}}catch(e){throw e}}}},26373:(e,r,t)=>{t.d(r,{A:()=>n});var a=t(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:c="",children:n,iconNode:l,...u},d)=>(0,a.createElement)("svg",{ref:d,...o,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:i("lucide",c),...u},[...l.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(n)?n:[n]])),n=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...o},n)=>(0,a.createElement)(c,{ref:n,iconNode:r,className:i(`lucide-${s(e)}`,t),...o}));return t.displayName=`${e}`,t}},26919:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(26373).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},30468:(e,r,t)=>{t.d(r,{CG:()=>a,SC:()=>s,cZ:()=>i});let a={BLOGS:"blogs",BUSINESS_ACTIVITIES:"business_activities",BUSINESS_POSTS:"business_posts",BUSINESS_PROFILES:"business_profiles",CARD_VISITS:"card_visits",CUSTOMER_POSTS:"customer_posts",CUSTOMER_PROFILES:"customer_profiles",CUSTOMER_PROFILES_PUBLIC:"customer_profiles_public",LIKES:"likes",PAYMENT_SUBSCRIPTIONS:"payment_subscriptions",PINCODES:"pincodes",PRODUCTS_SERVICES:"products_services",PRODUCT_VARIANTS:"product_variants",STORAGE_CLEANUP_CONFIG:"storage_cleanup_config",STORAGE_CLEANUP_PROGRESS:"storage_cleanup_progress",SUBSCRIPTIONS:"subscriptions",SYSTEM_ALERTS:"system_alerts",RATINGS_REVIEWS:"ratings_reviews"},s={BUSINESS:"business",CUSTOMERS:"customers"},i={ID:"id",CREATED_AT:"created_at",UPDATED_AT:"updated_at",NAME:"name",EMAIL:"email",PHONE:"phone",CITY:"city",STATE:"state",PINCODE:"pincode",PLAN_ID:"plan_id",LOCALITY:"locality",CITY_SLUG:"city_slug",STATE_SLUG:"state_slug",LOCALITY_SLUG:"locality_slug",LOGO_URL:"logo_url",IMAGE_URL:"image_url",IMAGES:"images",SLUG:"slug",STATUS:"status",CONTENT:"content",GALLERY:"gallery",DESCRIPTION:"description",TITLE:"title",USER_ID:"user_id",BUSINESS_ID:"business_id",CUSTOMER_ID:"customer_id",BUSINESS_NAME:"business_name",BUSINESS_SLUG:"business_slug",PRODUCT_ID:"product_id",PRODUCT_TYPE:"product_type",BUSINESS_PROFILE_ID:"business_profile_id",RAZORPAY_SUBSCRIPTION_ID:"razorpay_subscription_id",SUBSCRIPTION_STATUS:"subscription_status",RATING:"rating",REVIEW_TEXT:"review_text",AVATAR_URL:"avatar_url",ADDRESS_LINE:"address_line"}},52141:(e,r,t)=>{t.r(r),t.d(r,{"009fe8b9bf3d18ba45e98fe31212c3822d5b54fe2c":()=>a.B,"4011efda3090fa91301814dd778f0962ab6f5478ab":()=>s.Q$,"4013bd8c2d1d5e16fcb96b03a5cb10e0d7dc9ba5df":()=>s.WK,"4036de751d5e11fd003f302b393e6672d1cbdf36d6":()=>s.e8,"407c36d39aa231ea15bece9e167ebf2afc0a15ada1":()=>s.PU,"40b0b029477ba7767397813ae9d196ab075951ccd6":()=>s.J3,"40c0110e5afa3f81c7409c5b91aa05a2c47826a136":()=>s.Wr,"70838c1da0e20e605e0ccb1f558bab8107f4aaa646":()=>s.h6});var a=t(64275),s=t(32293)},54781:(e,r,t)=>{t.d(r,{E:()=>i});var a=t(37413),s=t(66819);function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",e),...r})}},56748:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(62688).A)("Compass",[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},66819:(e,r,t)=>{t.d(r,{cn:()=>i,gV:()=>o});var a=t(75986),s=t(8974);function i(...e){return(0,s.QP)((0,a.$)(e))}function o(e){if(!e)return null;let r=e.trim();return(r.startsWith("+91")?r=r.substring(3):12===r.length&&r.startsWith("91")&&(r=r.substring(2)),/^\d{10}$/.test(r))?r:null}}};