'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Compass, Heart } from 'lucide-react';
import LikeCard, { LikeData } from './LikeCard';

interface LikeListProps {
  initialLikes: LikeData[];
  onUnlikeSuccess?: (_likeId: string) => void;
  showUnlike?: boolean;
  variant?: 'default' | 'compact';
  emptyMessage?: string;
  emptyDescription?: string;
  showDiscoverButton?: boolean;
  showVisitButton?: boolean;
  showAddress?: boolean;
  showRedirectIcon?: boolean;
}

export default function LikeList({
  initialLikes,
  onUnlikeSuccess,
  showUnlike = true,
  variant = 'default',
  emptyMessage = "No likes found.",
  emptyDescription = "Like profiles to see them here.",
  showDiscoverButton = false,
  showVisitButton = true,
  showAddress = true,
  showRedirectIcon = false
}: LikeListProps) {
  const [likes, setLikes] = useState(initialLikes);

  // Update likes when initialLikes changes
  useEffect(() => {
    setLikes(initialLikes);
  }, [initialLikes]);

  // Handle successful unlike
  const handleUnlikeSuccess = (likeId: string) => {
    setLikes((prevLikes) => prevLikes.filter((like) => like.id !== likeId));
    if (onUnlikeSuccess) {
      onUnlikeSuccess(likeId);
    }
  };

  // Enhanced empty state for likes (matching business component style)
  if (likes.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-20 text-center">
        <div className="relative mb-8">
          <div className="absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"></div>
          <div className="relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg">
            <Heart className="w-10 h-10 text-primary" />
          </div>
        </div>
        <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3">
          {emptyMessage}
        </h3>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2">
          {emptyDescription}
        </p>
        <p className="text-sm text-neutral-500 dark:text-neutral-500 max-w-md leading-relaxed mb-8">
          Discover amazing businesses and show your support by liking them.
        </p>
        {showDiscoverButton && (
          <Button asChild variant="outline" className="gap-2 px-6 py-2.5 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200">
            <Link href="/businesses" target="_blank" rel="noopener noreferrer">
              <Compass className="w-4 h-4" />
              Discover Businesses
            </Link>
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {likes.map((like, _index) => {
        const profile = like.profile;

        if (!profile) {
          return null; // Skip items with missing profiles
        }

        return (
          <LikeCard
            key={like.id}
            likeId={like.id}
            profile={profile}
            onUnlikeSuccess={showUnlike ? handleUnlikeSuccess : undefined}
            showUnlike={showUnlike}
            variant={variant}
            showVisitButton={showVisitButton}
            showAddress={showAddress}
            showRedirectIcon={showRedirectIcon}
          />
        );
      })}
    </div>
  );
}
