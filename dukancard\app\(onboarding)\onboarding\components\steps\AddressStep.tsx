import React from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Home, MapPin, Building2, Loader2 } from "lucide-react";
import { StepComponentProps } from "../../types/onboarding";

export function AddressStep({
  form,
  isSubmitting,
  availableLocalities = [],
  isPincodeLoading = false,
  handlePincodeChange
}: StepComponentProps) {
  return (
    <>
      <FormField
        control={form.control}
        name="addressLine"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <Home className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Address Line
            </FormLabel>
            <FormControl>
              <div className="relative">
                <Input
                  placeholder="e.g., 123 Main Street, Building A"
                  {...field}
                  className="bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg"
                  disabled={isSubmitting}
                />
                <Home className="absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400" />
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="pincode"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <MapPin className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Pincode
            </FormLabel>
            <FormControl>
              <div className="relative">
                <Input
                  placeholder="e.g., 110001"
                  type="tel"
                  pattern="[0-9]*"
                  inputMode="numeric"
                  {...field}
                  onChange={(e) => {
                    // Only allow numeric input
                    const value = e.target.value.replace(/\D/g, '');
                    field.onChange(value);

                    // Trigger pincode lookup when 6 digits are entered
                    if (value.length === 6 && handlePincodeChange) {
                      handlePincodeChange(value);
                    }
                  }}
                  onKeyDown={(e) => {
                    // Prevent non-numeric input (except for control keys like backspace, arrows, etc.)
                    const isNumeric = /^[0-9]$/.test(e.key);
                    const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);

                    if (!isNumeric && !isControl) {
                      e.preventDefault();
                    }
                  }}
                  className="bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 pr-10 h-12 text-foreground transition-all rounded-lg"
                  disabled={isSubmitting}
                />
                <MapPin className="absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400" />
                {isPincodeLoading && (
                  <Loader2 className="absolute right-3 top-3.5 w-5 h-5 text-muted-foreground animate-spin" />
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="city"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-foreground flex items-center gap-2">
                <MapPin className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
                City
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Auto-filled from pincode"
                  {...field}
                  className="bg-muted/50 border-border text-foreground dark:bg-neutral-800/30 dark:border-neutral-700 h-12 transition-all rounded-lg cursor-not-allowed"
                  disabled={true}
                  readOnly
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="state"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-foreground flex items-center gap-2">
                <MapPin className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
                State
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Auto-filled from pincode"
                  {...field}
                  className="bg-muted/50 border-border text-foreground dark:bg-neutral-800/30 dark:border-neutral-700 h-12 transition-all rounded-lg cursor-not-allowed"
                  disabled={true}
                  readOnly
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <FormField
        control={form.control}
        name="locality"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <MapPin className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Locality/Area
            </FormLabel>
            <Select
              onValueChange={field.onChange}
              value={field.value ?? ""}
              disabled={availableLocalities.length === 0 || isSubmitting}
            >
              <FormControl>
                <SelectTrigger className="w-full bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 h-12 text-foreground transition-all rounded-lg">
                  <SelectValue
                    placeholder={
                      availableLocalities.length === 0
                        ? "Enter Pincode first"
                        : "Select your locality"
                    }
                  />
                </SelectTrigger>
              </FormControl>
              <SelectContent className="w-full border border-border dark:border-neutral-700 rounded-lg shadow-lg">
                {availableLocalities.map((loc) => (
                  <SelectItem key={loc} value={loc} className="text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20">
                    {loc}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="businessStatus"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <Building2 className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Business Status
            </FormLabel>
            <FormControl>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => field.onChange("online")}
                  className={`flex-1 p-3 rounded-lg border transition-all ${
                    field.value === "online"
                      ? "bg-primary/10 border-primary text-primary dark:bg-[var(--brand-gold)]/10 dark:border-[var(--brand-gold)] dark:text-[var(--brand-gold)]"
                      : "bg-background border-border text-muted-foreground hover:border-primary/50 dark:bg-neutral-800/50 dark:border-neutral-700 dark:hover:border-[var(--brand-gold)]/50"
                  }`}
                  disabled={isSubmitting}
                >
                  <div className="text-center">
                    <div className="font-medium">Online</div>
                    <div className="text-xs opacity-75">Ready to serve customers</div>
                  </div>
                </button>
                <button
                  type="button"
                  onClick={() => field.onChange("offline")}
                  className={`flex-1 p-3 rounded-lg border transition-all ${
                    field.value === "offline"
                      ? "bg-primary/10 border-primary text-primary dark:bg-[var(--brand-gold)]/10 dark:border-[var(--brand-gold)] dark:text-[var(--brand-gold)]"
                      : "bg-background border-border text-muted-foreground hover:border-primary/50 dark:bg-neutral-800/50 dark:border-neutral-700 dark:hover:border-[var(--brand-gold)]/50"
                  }`}
                  disabled={isSubmitting}
                >
                  <div className="text-center">
                    <div className="font-medium">Offline</div>
                    <div className="text-xs opacity-75">Setting up business</div>
                  </div>
                </button>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
}
