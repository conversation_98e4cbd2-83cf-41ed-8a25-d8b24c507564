"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { Users, UserCheck } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import { cn, formatIndianNumberShort } from "@/lib/utils";
import {
  SubscriptionSearch,
  SubscriptionPagination,
  SubscriptionListSkeleton,
  SubscriptionList,
  SubscriptionData
} from "@/app/components/shared/subscriptions";
import { SubscriberWithProfile, BusinessFollowingWithProfile } from "../actions";

interface BusinessSubscriptionsPageClientProps {
  initialSubscribers: SubscriberWithProfile[];
  subscribersCount: number;
  subscribersCurrentPage: number;
  initialFollowing: BusinessFollowingWithProfile[];
  followingCount: number;
  followingCurrentPage: number;
  searchTerm: string;
  activeTab: string;
}

export default function BusinessSubscriptionsPageClient({
  initialSubscribers,
  subscribersCount,
  subscribersCurrentPage,
  initialFollowing,
  followingCount,
  followingCurrentPage,
  searchTerm,
  activeTab,
}: BusinessSubscriptionsPageClientProps) {
  const router = useRouter();
  const _searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };



  // Reset loading state when component receives new data
  useEffect(() => {
    setIsLoading(false);
  }, [initialSubscribers, initialFollowing]);

  // Handle tab change
  const handleTabChange = useCallback((tab: string) => {
    setIsLoading(true);
    const params = new URLSearchParams();
    params.set('tab', tab);
    params.set('page', '1'); // Reset to page 1 on tab change
    router.push(`/dashboard/business/subscriptions?${params.toString()}`);
  }, [router]);

  // Handle search (only for following tab)
  const handleSearch = useCallback((term: string) => {
    if (activeTab !== 'following') return; // Only allow search for following tab

    setIsLoading(true);
    const params = new URLSearchParams();
    params.set('tab', activeTab);
    if (term) {
      params.set('search', term);
    }
    params.set('page', '1'); // Reset to page 1 on new search
    router.push(`/dashboard/business/subscriptions?${params.toString()}`);
  }, [router, activeTab]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setIsLoading(true);
    const params = new URLSearchParams();
    params.set('tab', activeTab);
    // Only include search term for following tab
    if (activeTab === 'following' && searchTerm) {
      params.set('search', searchTerm);
    }
    params.set('page', page.toString());
    router.push(`/dashboard/business/subscriptions?${params.toString()}`);
  }, [router, activeTab, searchTerm]);

  // Transform data for the shared components
  const transformedSubscribers: SubscriptionData[] = useMemo(() => {
    return initialSubscribers.map(sub => ({
      id: sub.id,
      profile: sub.profile
    })).filter(sub => sub.profile !== null) as SubscriptionData[];
  }, [initialSubscribers]);

  const transformedFollowing: SubscriptionData[] = useMemo(() => {
    return initialFollowing.map(sub => ({
      id: sub.id,
      profile: sub.business_profiles ? {
        id: sub.business_profiles.id,
        name: sub.business_profiles.business_name,
        slug: sub.business_profiles.business_slug,
        logo_url: sub.business_profiles.logo_url,
        city: sub.business_profiles.city,
        state: sub.business_profiles.state,
        pincode: sub.business_profiles.pincode,
        address_line: sub.business_profiles.address_line,
        type: 'business' as const,
      } : null
    })).filter(sub => sub.profile !== null) as SubscriptionData[];
  }, [initialFollowing]);

  // Calculate pagination data
  const currentData = activeTab === 'subscribers' ? transformedSubscribers : transformedFollowing;
  const currentCount = activeTab === 'subscribers' ? subscribersCount : followingCount;
  const currentPage = activeTab === 'subscribers' ? subscribersCurrentPage : followingCurrentPage;
  const itemsPerPage = 10;
  const totalPages = Math.max(1, Math.ceil(currentCount / itemsPerPage));

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Modern SaaS Header - Full Width */}
      <motion.div
        className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        <div className="space-y-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
              <Users className="w-5 h-5 text-primary" />
            </div>
            <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
            <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
              Community Management
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
            Subscriptions
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed">
            Build and manage your business community. Track your subscribers and discover new businesses to follow.
          </p>
        </div>
      </motion.div>

      {/* Enhanced Tabs Section - Full Width */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <div className="w-full flex justify-center mb-8">
          <div className="flex p-1.5 rounded-2xl bg-neutral-100/80 dark:bg-neutral-800/80 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleTabChange('subscribers')}
              className={cn(
                "rounded-xl py-3 px-6 font-medium flex items-center gap-2 transition-all duration-200",
                activeTab === 'subscribers'
                  ? "bg-white dark:bg-neutral-900 text-neutral-900 dark:text-neutral-50 shadow-lg border-primary/20"
                  : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200"
              )}
            >
              <Users className="w-4 h-4 text-blue-500" />
              <span>Subscribers</span>
              <span className="ml-1 px-2 py-0.5 text-xs bg-neutral-200 dark:bg-neutral-700 rounded-full">
                {formatIndianNumberShort(subscribersCount)}
              </span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleTabChange('following')}
              className={cn(
                "rounded-xl py-3 px-6 font-medium flex items-center gap-2 transition-all duration-200",
                activeTab === 'following'
                  ? "bg-white dark:bg-neutral-900 text-neutral-900 dark:text-neutral-50 shadow-lg border-primary/20"
                  : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200"
              )}
            >
              <UserCheck className="w-4 h-4 text-green-500" />
              <span>Following</span>
              <span className="ml-1 px-2 py-0.5 text-xs bg-neutral-200 dark:bg-neutral-700 rounded-full">
                {formatIndianNumberShort(followingCount)}
              </span>
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Enhanced Search Section - Full Width */}
      {activeTab === 'following' && (
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="p-6 bg-neutral-50/50 dark:bg-neutral-900/20 rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50"
        >
          <div className="flex items-center gap-3 mb-4">
            <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Search businesses you&apos;re following:
            </span>
          </div>
          <SubscriptionSearch
            onSearch={handleSearch}
            initialSearchTerm={searchTerm}
            placeholder="Search businesses..."
          />
        </motion.div>
      )}

      {/* Enhanced Content Section - Full Width */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        {/* Enhanced Count display - only show for following tab with search */}
        {activeTab === 'following' && searchTerm && !isLoading && (
          <div className="mb-8 p-4 bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-xl">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-primary"></div>
              <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                Found {formatIndianNumberShort(currentCount)} {currentCount === 1 ? 'result' : 'results'}
                {searchTerm ? ` matching "${searchTerm}"` : ''}
              </span>
            </div>
          </div>
        )}

        {/* Enhanced content with loading states */}
        {isLoading ? (
          <div className="space-y-6">
            <SubscriptionListSkeleton />
          </div>
        ) : (
          <div className="space-y-8">
            {/* Enhanced Subscription List */}
            <div className="min-h-[400px]">
              <SubscriptionList
                initialSubscriptions={currentData}
                showUnsubscribe={activeTab === 'following'}
                emptyMessage={
                  activeTab === 'subscribers'
                    ? "No subscribers yet."
                    : "You haven't subscribed to any businesses yet."
                }
                emptyDescription={
                  activeTab === 'subscribers'
                    ? "When people subscribe to your business, they'll appear here."
                    : "Subscribe to businesses to receive updates and notifications."
                }
                showDiscoverButton={activeTab === 'following'}
              />
            </div>

            {/* Enhanced Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center pt-8 border-t border-neutral-200/60 dark:border-neutral-700/60">
                <SubscriptionPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
