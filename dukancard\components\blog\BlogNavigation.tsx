"use client";

import { motion } from "framer-motion";
import {
  ChevronLeft,
  ChevronRight,
  Home,
  BookOpen,
  ArrowUp,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Blog } from "@/lib/types/blog";
import Link from "next/link";
import { useEffect, useState } from "react";

interface BlogNavigationProps {
  currentBlog?: Blog;
  previousBlog?: Blog;
  nextBlog?: Blog;
  showBackToTop?: boolean;
}

export default function BlogNavigation({
  currentBlog,
  previousBlog,
  nextBlog,
  showBackToTop = true,
}: BlogNavigationProps) {
  const [showScrollTop, setShowScrollTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    if (showBackToTop) {
      window.addEventListener("scroll", handleScroll);
      return () => window.removeEventListener("scroll", handleScroll);
    }
  }, [showBackToTop]);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <>
      {/* Breadcrumb Navigation */}
      <motion.nav
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-2 text-sm text-muted-foreground mb-8"
      >
        <Link
          href="/"
          className="hover:text-foreground transition-colors flex items-center"
        >
          <Home className="h-4 w-4 mr-1" />
          Home
        </Link>
        <ChevronRight className="h-4 w-4" />
        <Link
          href="/blog"
          className="hover:text-foreground transition-colors flex items-center"
        >
          <BookOpen className="h-4 w-4 mr-1" />
          Blog
        </Link>
        {currentBlog && (
          <>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground truncate max-w-xs">
              {currentBlog.title}
            </span>
          </>
        )}
      </motion.nav>

      {/* Previous/Next Navigation */}
      {(previousBlog || nextBlog) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 md:grid-cols-2 gap-6 my-12"
        >
          {/* Previous Blog */}
          <div className="flex justify-start">
            {previousBlog ? (
              <Link
                href={`/blog/${previousBlog.slug}`}
                className="block w-full"
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary/50 hover:border-l-primary">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                          <ChevronLeft className="h-5 w-5 text-primary" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-muted-foreground mb-1">
                          Previous
                        </p>
                        <h3 className="font-semibold text-foreground line-clamp-2 mb-2">
                          {previousBlog.title}
                        </h3>
                        {previousBlog.categories &&
                          previousBlog.categories.length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {previousBlog.categories[0]}
                            </Badge>
                          )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ) : (
              <div className="w-full opacity-50">
                <Card className="h-full border-dashed">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-center h-20 text-muted-foreground">
                      <span className="text-sm">No previous post</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>

          {/* Next Blog */}
          <div className="flex justify-end">
            {nextBlog ? (
              <Link href={`/blog/${nextBlog.slug}`} className="block w-full">
                <Card className="h-full hover:shadow-lg transition-all duration-300 border-r-4 border-r-primary/50 hover:border-r-primary">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-1 min-w-0 text-right">
                        <p className="text-sm text-muted-foreground mb-1">
                          Next
                        </p>
                        <h3 className="font-semibold text-foreground line-clamp-2 mb-2">
                          {nextBlog.title}
                        </h3>
                        {nextBlog.categories &&
                          nextBlog.categories.length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {nextBlog.categories[0]}
                            </Badge>
                          )}
                      </div>
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                          <ChevronRight className="h-5 w-5 text-primary" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ) : (
              <div className="w-full opacity-50">
                <Card className="h-full border-dashed">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-center h-20 text-muted-foreground">
                      <span className="text-sm">No next post</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Back to Top Button */}
      {showBackToTop && showScrollTop && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="fixed bottom-8 right-8 z-50"
        >
          <Button
            onClick={scrollToTop}
            size="icon"
            className="rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <ArrowUp className="h-4 w-4" />
          </Button>
        </motion.div>
      )}

      {/* Quick Navigation Menu */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-wrap gap-4 justify-center my-8"
      >
        <Link href="/blog">
          <Button variant="outline" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            All Posts
          </Button>
        </Link>

        {currentBlog?.categories &&
          currentBlog.categories.map((category) => (
            <Link
              key={category}
              href={`/blog?category=${encodeURIComponent(category)}`}
            >
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
              >
                <Badge variant="outline" className="text-xs">
                  {category}
                </Badge>
                More in {category}
              </Button>
            </Link>
          ))}
      </motion.div>
    </>
  );
}
