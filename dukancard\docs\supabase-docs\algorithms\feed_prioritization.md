# Feed Prioritization Algorithm Documentation

## Overview

The Dukancard feed system implements a sophisticated "Hybrid Time + Plan Prioritization" algorithm across ALL feed types (smart, locality, pincode, city, state, all). The system perfectly balances recency with business plan benefits while respecting location-based filters. The algorithm ensures the latest content gets priority while guaranteeing each business plan tier gets representation, maintaining fairness and preventing content loss.

## Core Principles

### 1. Optimized Hybrid Strategy (Zero Post Loss)
- **Fetch exactly target posts**: No over-fetching, no content loss
- **Latest post priority**: Plan prioritization applies only to latest post per business
- **Fair competition**: Multiple posts from same business compete on time, not plan
- **Equal treatment**: Customer and business posts compete equally by timestamp

### 2. Universal Application Across All Feed Types
- **Smart Feed**: Hybrid algorithm + user personalization filters
- **Locality Feed**: Hybrid algorithm + locality_slug filter
- **Pincode Feed**: Hybrid algorithm + pincode filter
- **City Feed**: Hybrid algorithm + city_slug filter
- **State Feed**: Hybrid algorithm + state_slug filter
- **All Posts Feed**: Hybrid algorithm with no location filters

### 3. Perfect Pagination (Zero Loss Guarantee)
- **Exact fetch strategy**: Fetch 10 posts, process 10 posts, show 10 posts
- **Standard database pagination**: Page 1: posts 1-10, Page 2: posts 11-20, etc.
- **100% content visibility**: Every single post appears across pagination
- **Efficient processing**: No over-fetching, optimal performance

### 4. Content Completeness & Fairness
- **All posts shown**: Every post appears eventually through pagination
- **Latest content priority**: Most recent posts get immediate visibility
- **Plan benefits**: Higher tiers get guaranteed representation
- **Fair competition**: Remaining slots are purely time-based

### 5. User Experience Optimization
- **Fresh content**: Latest posts always appear first
- **Plan diversity**: Each page shows variety of business tiers
- **No content hiding**: Lower-plan businesses still get visibility through time-based slots
- **Predictable pagination**: Users can expect consistent content distribution

## Algorithm Details

### Step 1: Database Fetch (Chronological)
```javascript
// Fetch posts in chronological order from database
const { data } = await query
  .order('created_at', { ascending: false })
  .range(from, to);
```

**Purpose**: Get posts in time order from database. This maintains database-level pagination compatibility.

### Step 2: Content Separation
```javascript
// Separate customer and business posts
const customerPosts = posts.filter(post => post.post_source === 'customer');
const businessPosts = posts.filter(post => post.post_source === 'business');
```

**Purpose**: Handle customer and business posts with different strategies.

### Step 3: Business Posts - Latest Post Priority Only
```javascript
// Group posts by business (author_id)
const postsByBusiness = groupPostsByBusiness(businessPosts);

// Separate latest posts (get plan priority) from other posts (time-based only)
const latestPostsPerBusiness = [];
const otherPostsFromBusinesses = [];

postsByBusiness.forEach((posts, businessId) => {
  // Latest post gets plan priority
  latestPostsPerBusiness.push(posts[0]);

  // Other posts from same business compete on time only
  if (posts.length > 1) {
    otherPostsFromBusinesses.push(...posts.slice(1));
  }
});

// Sort latest posts by plan priority + timestamp
const prioritizedLatestPosts = latestPostsPerBusiness.sort(byPlanAndTime);

// Sort other posts purely by timestamp (no plan priority)
const timeBasedOtherPosts = otherPostsFromBusinesses.sort(byTimeOnly);

// Combine: prioritized latest posts first, then time-based other posts
return [...prioritizedLatestPosts, ...timeBasedOtherPosts];
```

**Purpose**:
- **Fair plan benefits**: Only latest post per business gets plan priority
- **Prevents dominance**: Multiple posts from same business compete on time
- **Balanced representation**: No single business can monopolize high positions

### Step 4: Customer Posts Processing
```javascript
// Sort customer posts chronologically (latest first)
const sortedCustomerPosts = customerPosts.sort((a, b) =>
  new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
);

// Apply diversity rules if enabled
const processedCustomerPosts = enableDiversity
  ? applyDiversityRules(sortedCustomerPosts)
  : sortedCustomerPosts;
```

**Purpose**: Maintain chronological order for customer posts with optional diversity.

### Step 5: Equal Treatment Merging
```javascript
// Merge customer and business posts with EQUAL treatment
// Customer posts compete equally with business posts based on timestamp
// Plan prioritization only applies within business posts
const mergedPosts = [...customerPosts, ...businessPosts]
  .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
```

**Purpose**:
- **Equal treatment**: No priority between customer vs business posts
- **Time-based competition**: All posts compete based on timestamp
- **Plan benefits preserved**: Business posts already sorted by plan priority

## Feed Type Implementation

### How Algorithm Works with Different Feed Types

All feed types follow the same hybrid algorithm pattern but with different database filters:

```javascript
// 1. Apply location/filter-specific database query
switch (filter) {
  case 'locality':
    query = query.eq('locality_slug', locality_slug);
    break;
  case 'pincode':
    query = query.eq('pincode', pincode);
    break;
  case 'city':
    query = query.eq('city_slug', city_slug);
    break;
  case 'state':
    query = query.eq('state_slug', state_slug);
    break;
  case 'all':
    // No additional filters
    break;
  case 'smart':
    // User personalization filters (subscriptions, location, etc.)
    break;
}

// 2. Fetch posts chronologically with location filter applied
const { data } = await query.order('created_at', { ascending: false });

// 3. Apply SAME hybrid algorithm to filtered results
const prioritizedData = processHybridTimeAndPlan(data, page, options);
```

### Feed Type Examples

**Locality Feed (Mumbai Bandra):**
- Database: Fetch posts where `locality_slug = 'mumbai-bandra'`
- Algorithm: Apply hybrid time + plan to Bandra posts only
- Result: Latest Bandra posts with plan prioritization

**City Feed (Delhi):**
- Database: Fetch posts where `city_slug = 'delhi'`
- Algorithm: Apply hybrid time + plan to Delhi posts only
- Result: Latest Delhi posts with plan prioritization

**All Posts Feed:**
- Database: Fetch all posts (no location filter)
- Algorithm: Apply hybrid time + plan to all posts
- Result: Latest posts globally with plan prioritization

### Benefits of Universal Application

1. **Consistent Experience**: Same algorithm across all tabs
2. **Plan Benefits Everywhere**: Business plan advantages in all locations
3. **Local Relevance**: Location filters + plan prioritization combined
4. **No Content Loss**: Algorithm ensures all posts appear across all feed types
5. **Maintainable Code**: Single algorithm for all feed types

## Plan Priority Matrix

| Plan | Priority Value | Visibility Level | Behavior |
|------|----------------|------------------|----------|
| Enterprise | 5 | Highest | All posts appear before lower-plan businesses |
| Pro | 4 | High | Posts appear before growth/basic/free businesses |
| Growth | 3 | Medium-High | Posts appear before basic/free businesses |
| Basic | 2 | Medium | Posts appear before free businesses |
| Free | 1 | Standard | Posts appear in chronological order among free businesses |

## Example: Optimized Hybrid Algorithm (Zero Loss)

### Input: Page 1 Database Fetch (Exactly 10 posts):
**Database Query**: `ORDER BY created_at DESC LIMIT 10 OFFSET 0`
1. Customer X Post1 (15:30) ← Latest overall
2. Enterprise A Post1 (15:00) ← Latest from Enterprise A
3. Pro B Post1 (14:30) ← Latest from Pro B
4. Growth C Post1 (14:00) ← Latest from Growth C
5. Customer Y Post1 (13:45) ← Customer post
6. Free D Post1 (13:30) ← Latest from Free D
7. Enterprise A Post2 (13:00) ← 2nd post from Enterprise A
8. Pro B Post2 (12:30) ← 2nd post from Pro B
9. Customer Z Post1 (12:15) ← Customer post
10. Growth C Post2 (12:00) ← 2nd post from Growth C

### Algorithm Processing:

**Step 1**: Separate Posts
- **Customer Posts**: [X Post1 (15:30), Y Post1 (13:45), Z Post1 (12:15)]
- **Business Posts**: [A Post1 (15:00), B Post1 (14:30), C Post1 (14:00), D Post1 (13:30), A Post2 (13:00), B Post2 (12:30), C Post2 (12:00)]

**Step 2**: Process Customer Posts (Chronological)
- X Post1 (15:30) ← Latest customer
- Y Post1 (13:45) ← 2nd latest customer
- Z Post1 (12:15) ← 3rd latest customer

**Step 3**: Process Business Posts (Latest Post Priority Only)

**3a**: Latest Posts Per Business (Get Plan Priority)
- Enterprise A Post1 (15:00) ← Latest from A, gets enterprise priority
- Pro B Post1 (14:30) ← Latest from B, gets pro priority
- Growth C Post1 (14:00) ← Latest from C, gets growth priority
- Free D Post1 (13:30) ← Latest from D, gets free priority

**3b**: Other Posts From Same Businesses (Time-Based Only)
- Enterprise A Post2 (13:00) ← 2nd post from A, NO plan priority
- Pro B Post2 (12:30) ← 2nd post from B, NO plan priority
- Growth C Post2 (12:00) ← 2nd post from C, NO plan priority

**3c**: Final Business Order
- Enterprise A Post1 (15:00) ← Plan priority
- Pro B Post1 (14:30) ← Plan priority
- Growth C Post1 (14:00) ← Plan priority
- Free D Post1 (13:30) ← Plan priority
- Enterprise A Post2 (13:00) ← Time-based only
- Pro B Post2 (12:30) ← Time-based only
- Growth C Post2 (12:00) ← Time-based only

**Step 4**: Merge by Timestamp (Equal Treatment)

### Page 1 Final Results (All 10 posts shown):
1. Customer X Post1 (15:30) ← Latest overall (time wins)
2. Enterprise A Post1 (15:00) ← Latest from A + plan priority
3. Pro B Post1 (14:30) ← Latest from B + plan priority
4. Growth C Post1 (14:00) ← Latest from C + plan priority
5. Customer Y Post1 (13:45) ← Customer post (time-based)
6. Free D Post1 (13:30) ← Latest from D + plan priority
7. Enterprise A Post2 (13:00) ← 2nd from A, time-based only
8. Pro B Post2 (12:30) ← 2nd from B, time-based only
9. Customer Z Post1 (12:15) ← Customer post (time-based)
10. Growth C Post2 (12:00) ← 2nd from C, time-based only

### Page 2: Next 10 Posts (11-20)
**Database Query**: `ORDER BY created_at DESC LIMIT 10 OFFSET 10`
- Processes next 10 posts with same algorithm
- **Zero overlap** with Page 1
- **Zero posts lost**

### Result Analysis:
- ✅ **Zero post loss**: All 10 fetched posts are shown
- ✅ **Fair plan benefits**: Only latest post per business gets plan priority
- ✅ **Prevents business dominance**: Multiple posts from same business compete on time
- ✅ **Time prioritization**: Latest content gets preference overall
- ✅ **Equal treatment**: Customer vs business posts compete by timestamp only
- ✅ **Perfect pagination**: Page 2 gets posts 11-20, no gaps or overlaps

## Performance Considerations

### Modular Architecture
- **Diversity Engine**: `lib/utils/feed/diversityEngine.ts` - Handles author diversity
- **Plan Prioritizer**: `lib/utils/feed/planPrioritizer.ts` - Manages business plan logic
- **Feed Merger**: `lib/utils/feed/feedMerger.ts` - Intelligently combines post types
- **Smart Algorithm**: `lib/utils/feed/smartFeedAlgorithm.ts` - Main orchestrator

### Database Query Strategy
- **All Feed Types**: Fetch exactly target posts (10 posts for 10 per page) - zero waste
- **Location Filters**: Applied at database level before algorithm processing
- **Query Complexity**: Simple chronological query with location filters, optimal efficiency

### Client-Side Processing
- **Time Complexity**: O(n log n) for sorting operations across modules
- **Space Complexity**: O(n) for grouping and temporary arrays in each utility
- **Performance Impact**: Minimal due to modular design and typical feed sizes (30-100 posts)
- **Maintainability**: High due to separation of concerns

### Pagination Handling
```javascript
// Process all fetched posts through smart algorithm
const processedPosts = processSmartFeed(data, options);

// Apply pagination to final processed results
const paginatedResults = paginateSmartFeed(processedPosts, page, limit);
```

**Rationale**: Process the complete dataset first to ensure proper prioritization and diversity, then apply pagination to the optimized results.

## Edge Cases and Handling

### 1. No Business Posts
- **Scenario**: Only customer posts in feed
- **Handling**: Applies diversity engine to customer posts only
- **Result**: Chronological customer feed with author diversity

### 2. No Customer Posts
- **Scenario**: Only business posts in feed
- **Handling**: Applies plan prioritization with diversity
- **Result**: Business-focused feed with plan benefits and author variety

### 3. Single Author Dominance
- **Scenario**: One author has many consecutive posts
- **Handling**: Diversity engine redistributes posts using round-robin
- **Result**: Author's posts spread throughout feed, no consecutive appearance

### 4. Same Plan Businesses
- **Scenario**: Multiple businesses on same plan tier
- **Handling**: Round-robin within plan tier, then chronological sorting
- **Result**: Fair distribution among same-tier businesses

### 5. Mixed Content Density
- **Scenario**: Some authors post frequently, others rarely
- **Handling**: Round-robin ensures all authors get representation
- **Result**: Balanced visibility preventing any single author dominance

## Benefits

### For Businesses
- **Higher Plans**: Priority positioning and increased visibility
- **All Plans**: Guaranteed content visibility with fair distribution
- **Fair Competition**: Recent activity and engagement still matter
- **Growth Incentive**: Clear, tangible benefits to upgrading plans

### For Users
- **Content Diversity**: No author dominates the feed experience
- **Natural Flow**: Feed feels organic like major social media platforms
- **Quality Experience**: Premium businesses get prominence without overwhelming
- **Engaging Variety**: Author diversity keeps users scrolling longer

### For Platform
- **Revenue Incentive**: Clear value proposition for business plan upgrades
- **User Retention**: Engaging, diverse feed experience increases session time
- **Scalability**: Modular architecture allows easy feature additions
- **Maintainability**: Clean separation of concerns in codebase

## Configuration

### Plan Priority Weights
```javascript
// lib/utils/feed/planPrioritizer.ts
export const PLAN_PRIORITY: Record<string, number> = {
  'enterprise': 5,  // Highest priority
  'pro': 4,         // High priority
  'growth': 3,      // Medium-high priority
  'basic': 2,       // Medium priority
  'free': 1         // Standard priority
};
```

### Smart Feed Options
```javascript
// lib/utils/feed/smartFeedAlgorithm.ts
const options: SmartFeedOptions = {
  prioritizeBusinessPosts: true,    // Enable business plan prioritization
  maintainChronologicalFlow: true, // Respect post timestamps
  enableDiversity: true,           // Prevent consecutive same-author posts
  businessPostWeight: 0.6          // Slightly favor business content (0-1)
};
```

### Diversity Settings
```javascript
// lib/utils/feed/diversityEngine.ts
const diversityOptions: DiversityOptions = {
  maxConsecutiveFromSameAuthor: 1, // Strict diversity (no consecutive posts)
  prioritizeRecency: true          // Newer posts get preference
};
```

## Future Enhancements

### 1. Machine Learning Integration
- **User Behavior Analysis**: Learn from user interactions to improve feed relevance
- **Engagement Prediction**: Predict which posts users are likely to engage with
- **Dynamic Plan Weighting**: Adjust plan priorities based on user engagement patterns

### 2. Advanced Diversity Algorithms
- **Content Type Diversity**: Ensure variety in post types (text, images, videos)
- **Topic Diversity**: Prevent topic clustering using NLP analysis
- **Temporal Diversity**: Balance recent vs. older content intelligently

### 3. Real-Time Personalization
- **User Interest Profiling**: Track user preferences and adjust feed accordingly
- **Social Graph Integration**: Consider user's business subscriptions and interactions
- **Location-Based Relevance**: Prioritize geographically relevant content

### 4. Performance Optimizations
- **Caching Layer**: Cache processed feeds for improved response times
- **Incremental Updates**: Only reprocess new posts instead of entire feed
- **Database Views**: Move some processing to database level for better performance

### 5. A/B Testing Framework
- **Algorithm Variants**: Test different prioritization strategies
- **Engagement Metrics**: Measure impact on user engagement and retention
- **Business Metrics**: Track conversion rates and plan upgrade patterns

This modular, Facebook/Instagram-inspired algorithm provides a sophisticated yet maintainable approach to feed prioritization that benefits all stakeholders while maintaining excellent user experience and code organization.
