"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Star, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import BusinessReviewListClient from "./BusinessReviewListClient";
import BusinessMyReviewListClient from "./BusinessMyReviewListClient";
import { cn } from "@/lib/utils";
import { formatIndianNumberShort } from "@/lib/utils";

interface BusinessReviewsPageClientProps {
  businessProfileId: string;
  reviewsReceivedCount: number;
  myReviewsCount: number;
}

export default function BusinessReviewsPageClient({
  businessProfileId,
  reviewsReceivedCount,
  myReviewsCount
}: BusinessReviewsPageClientProps) {
  const [activeTab, setActiveTab] = useState("received");

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };



  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Modern SaaS Header - Full Width */}
      <motion.div
        className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        <div className="space-y-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
              <Star className="w-5 h-5 text-primary" />
            </div>
            <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
            <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
              Review Management
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
            Business Reviews
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed">
            Monitor customer feedback and manage your business reputation. Track reviews received and reviews you&apos;ve given.
          </p>
        </div>
      </motion.div>

      {/* Enhanced Tabs Section - Full Width */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <div className="w-full flex justify-center mb-8">
          <div className="flex p-1.5 rounded-2xl bg-neutral-100/80 dark:bg-neutral-800/80 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setActiveTab('received')}
              className={cn(
                "rounded-xl py-3 px-6 font-medium flex items-center gap-2 transition-all duration-200",
                activeTab === 'received'
                  ? "bg-white dark:bg-neutral-900 text-neutral-900 dark:text-neutral-50 shadow-lg border-primary/20"
                  : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200"
              )}
            >
              <MessageSquare className="w-4 h-4 text-blue-500" />
              <span>Reviews Received</span>
              <span className="ml-1 px-2 py-0.5 text-xs bg-neutral-200 dark:bg-neutral-700 rounded-full">
                {formatIndianNumberShort(reviewsReceivedCount)}
              </span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setActiveTab('given')}
              className={cn(
                "rounded-xl py-3 px-6 font-medium flex items-center gap-2 transition-all duration-200",
                activeTab === 'given'
                  ? "bg-white dark:bg-neutral-900 text-neutral-900 dark:text-neutral-50 shadow-lg border-primary/20"
                  : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-200"
              )}
            >
              <Star className="w-4 h-4 text-amber-500" />
              <span>My Reviews</span>
              <span className="ml-1 px-2 py-0.5 text-xs bg-neutral-200 dark:bg-neutral-700 rounded-full">
                {formatIndianNumberShort(myReviewsCount)}
              </span>
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Enhanced Content Section - Full Width */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="min-h-[400px]">
          {activeTab === 'received' && (
            <BusinessReviewListClient businessProfileId={businessProfileId} />
          )}
          {activeTab === 'given' && (
            <BusinessMyReviewListClient />
          )}
        </div>
      </motion.div>
    </motion.div>
  );
}
