import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  // Ensure NEXT_PUBLIC_BASE_URL is set in your environment variables
  // Default to https://dukancard.in if not set
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";

  return {
    rules: [
      {
        userAgent: "*", // Applies to all crawlers
        allow: "/", // Allow crawling of the entire site
        disallow: [
          "/dashboard/", // Disallow dashboard pages
          "/login", // Disallow login page
          "/auth/", // Disallow auth pages
          "/api/", // Disallow API routes
          // Temporarily disallow sitemaps to reduce server load - uncomment when ready
          "/cards/sitemap.xml", // Temporarily disallow cards sitemap
          "/products/sitemap.xml", // Temporarily disallow products sitemap
        ],
      },
      {
        userAgent: "meta-externalagent", // Facebook/Meta crawler
        disallow: [
          "/cards/", // Disallow business card pages
          "/products/", // Disallow product pages
        ],
      },
      {
        userAgent: "facebookexternalhit", // Facebook link preview crawler
        disallow: [
          "/cards/", // Disallow business card pages
          "/products/", // Disallow product pages
        ],
      },
    ],
    // Include only main sitemap to reduce crawler load
    sitemap: [
      `${siteUrl}/sitemap.xml`, // Main sitemap only
      // TODO: Uncomment these when server is upgraded and ready for crawler load
      // `${siteUrl}/cards/sitemap.xml`, // Business cards sitemap
      // `${siteUrl}/products/sitemap.xml`, // Products sitemap
    ],
  };
}
