module.exports = {

"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-rsc] (ecmascript)");
;
async function createClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Check if we're in a test environment
    let headersList = null;
    let cookieStore = null;
    try {
        // Dynamically import next/headers to avoid issues in edge runtime
        const { headers, cookies } = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        headersList = await headers();
        cookieStore = await cookies();
    } catch (error) {
        // If next/headers is not available (e.g., in edge runtime), continue without it
        console.warn('next/headers not available in this context, using fallback');
    }
    const isTestEnvironment = ("TURBOPACK compile-time value", "development") === 'test' || process.env.PLAYWRIGHT_TESTING === 'true' || headersList && headersList.get('x-playwright-testing') === 'true';
    if (isTestEnvironment && headersList) {
        // Return a mocked Supabase client for testing
        return createMockSupabaseClient(headersList);
    }
    // If cookies are not available, create a basic server client
    if (!cookieStore) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
            cookies: {
                getAll () {
                    return [];
                },
                setAll () {
                // No-op when cookies are not available
                }
            }
        });
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
        cookies: {
            async getAll () {
                return await cookieStore.getAll();
            },
            async setAll (cookiesToSet) {
                try {
                    for (const { name, value, options } of cookiesToSet){
                        await cookieStore.set(name, value, options);
                    }
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
function createMockSupabaseClient(headersList) {
    const testAuthState = headersList.get('x-test-auth-state');
    const testUserType = headersList.get('x-test-user-type');
    const testHasProfile = testUserType === 'customer' || testUserType === 'business';
    const testBusinessSlug = headersList.get('x-test-business-slug');
    const testPlanId = headersList.get('x-test-plan-id') || 'free';
    return {
        auth: {
            getUser: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            user: {
                                id: 'test-user-id',
                                email: '<EMAIL>'
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        user: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            getSession: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            session: {
                                user: {
                                    id: 'test-user-id',
                                    email: '<EMAIL>'
                                }
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        session: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            signInWithOtp: async ()=>({
                    data: {
                        user: null,
                        session: null
                    },
                    error: null
                }),
            signOut: async ()=>({
                    error: null
                })
        },
        from: (table)=>createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId)
    };
}
function createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    const getMockData = ()=>getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);
    const createChainableMock = (data)=>({
            select: (_columns)=>createChainableMock(data),
            eq: (_column, _value)=>createChainableMock(data),
            neq: (_column, _value)=>createChainableMock(data),
            gt: (_column, _value)=>createChainableMock(data),
            gte: (_column, _value)=>createChainableMock(data),
            lt: (_column, _value)=>createChainableMock(data),
            lte: (_column, _value)=>createChainableMock(data),
            like: (_column, _pattern)=>createChainableMock(data),
            ilike: (_column, _pattern)=>createChainableMock(data),
            is: (_column, _value)=>createChainableMock(data),
            in: (_column, _values)=>createChainableMock(data),
            contains: (_column, _value)=>createChainableMock(data),
            containedBy: (_column, _value)=>createChainableMock(data),
            rangeGt: (_column, _value)=>createChainableMock(data),
            rangeGte: (_column, _value)=>createChainableMock(data),
            rangeLt: (_column, _value)=>createChainableMock(data),
            rangeLte: (_column, _value)=>createChainableMock(data),
            rangeAdjacent: (_column, _value)=>createChainableMock(data),
            overlaps: (_column, _value)=>createChainableMock(data),
            textSearch: (_column, _query)=>createChainableMock(data),
            match: (_query)=>createChainableMock(data),
            not: (_column, _operator, _value)=>createChainableMock(data),
            or: (_filters)=>createChainableMock(data),
            filter: (_column, _operator, _value)=>createChainableMock(data),
            order: (_column, _options)=>createChainableMock(data),
            limit: (_count, _options)=>createChainableMock(data),
            range: (_from, _to, _options)=>createChainableMock(data),
            abortSignal: (_signal)=>createChainableMock(data),
            single: async ()=>getMockData(),
            maybeSingle: async ()=>getMockData(),
            then: async (callback)=>{
                const result = getMockData();
                return callback ? callback(result) : result;
            },
            data: data || [],
            error: null,
            count: data ? data.length : 0,
            status: 200,
            statusText: 'OK'
        });
    return {
        select: (_columns)=>createChainableMock(),
        insert: (data)=>({
                select: (_columns)=>({
                        single: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        maybeSingle: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        then: async (_callback)=>{
                            const result = {
                                data: Array.isArray(data) ? data : [
                                    data
                                ],
                                error: null
                            };
                            return _callback ? _callback(result) : result;
                        }
                    }),
                then: async (_callback)=>{
                    const result = {
                        data: Array.isArray(data) ? data : [
                            data
                        ],
                        error: null
                    };
                    return _callback ? _callback(result) : result;
                }
            }),
        update: (data)=>createChainableMock(data),
        upsert: (data)=>createChainableMock(data),
        delete: ()=>createChainableMock(),
        rpc: (_functionName, _params)=>createChainableMock()
    };
}
/**
 * Helper function to get mock table data based on test state
 */ function getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    if (table === 'customer_profiles') {
        const hasCustomerProfile = testHasProfile && testUserType === 'customer';
        return {
            data: hasCustomerProfile ? {
                id: 'test-user-id',
                name: 'Test Customer',
                avatar_url: null,
                phone: '+1234567890',
                email: '<EMAIL>',
                address: 'Test Address',
                city: 'Test City',
                state: 'Test State',
                pincode: '123456'
            } : null,
            error: null
        };
    }
    if (table === 'business_profiles') {
        const hasBusinessProfile = testHasProfile && testUserType === 'business';
        return {
            data: hasBusinessProfile ? {
                id: 'test-user-id',
                business_slug: testBusinessSlug || null,
                trial_end_date: null,
                has_active_subscription: true,
                business_name: 'Test Business',
                city_slug: 'test-city',
                state_slug: 'test-state',
                locality_slug: 'test-locality',
                pincode: '123456',
                business_description: 'Test business description',
                business_category: 'retail',
                phone: '+1234567890',
                email: '<EMAIL>',
                website: 'https://testbusiness.com'
            } : null,
            error: null
        };
    }
    if (table === 'payment_subscriptions') {
        return {
            data: testUserType === 'business' ? {
                id: 'test-subscription-id',
                plan_id: testPlanId,
                business_profile_id: 'test-user-id',
                status: 'active',
                created_at: '2024-01-01T00:00:00Z'
            } : null,
            error: null
        };
    }
    if (table === 'products') {
        return {
            data: testUserType === 'business' ? [
                {
                    id: 'test-product-1',
                    name: 'Test Product 1',
                    price: 100,
                    business_profile_id: 'test-user-id',
                    available: true
                },
                {
                    id: 'test-product-2',
                    name: 'Test Product 2',
                    price: 200,
                    business_profile_id: 'test-user-id',
                    available: false
                }
            ] : [],
            error: null
        };
    }
    // Default return for unknown tables
    return {
        data: null,
        error: null
    };
}
}}),
"[project]/lib/schemas/authSchemas.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmailOTPSchema": (()=>EmailOTPSchema),
    "IndianMobileSchema": (()=>IndianMobileSchema),
    "MobilePasswordLoginSchema": (()=>MobilePasswordLoginSchema),
    "PasswordComplexitySchema": (()=>PasswordComplexitySchema),
    "VerifyOTPSchema": (()=>VerifyOTPSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
const IndianMobileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().trim().min(10, {
    message: "Mobile number must be 10 digits"
}).max(10, {
    message: "Mobile number must be 10 digits"
}).regex(/^[6-9]\d{9}$/, {
    message: "Please enter a valid Indian mobile number"
});
const EmailOTPSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Email is required"
    }).email({
        message: "Please enter a valid email address"
    })
});
const VerifyOTPSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Email is required"
    }).email({
        message: "Please enter a valid email address"
    }),
    otp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().trim().min(6, {
        message: "OTP must be 6 digits"
    }).max(6, {
        message: "OTP must be 6 digits"
    }).regex(/^\d{6}$/, {
        message: "OTP must be 6 digits"
    })
});
const PasswordComplexitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().min(6, "Password must be at least 6 characters long").regex(/[A-Z]/, "Password must contain at least one uppercase letter").regex(/[a-z]/, "Password must contain at least one lowercase letter").regex(/\d/, "Password must contain at least one number").regex(/[^a-zA-Z0-9]/, "Password must contain at least one special character");
const MobilePasswordLoginSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    mobile: IndianMobileSchema,
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Password is required"
    })
});
}}),
"[project]/lib/utils/supabaseErrorHandler.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getRetryDelay": (()=>getRetryDelay),
    "handleSupabaseAuthError": (()=>handleSupabaseAuthError),
    "isEmailRateLimitError": (()=>isEmailRateLimitError),
    "isRateLimitError": (()=>isRateLimitError),
    "isTemporaryError": (()=>isTemporaryError)
});
/**
 * User-friendly error messages for Supabase auth error codes
 */ const AUTH_ERROR_MESSAGES = {
    // Rate limiting errors
    over_email_send_rate_limit: "Email rate limit exceeded. Please wait before requesting another OTP.",
    over_request_rate_limit: "Too many requests. Please wait a few minutes before trying again.",
    over_sms_send_rate_limit: "Too many SMS messages sent. Please wait before requesting another OTP.",
    // OTP related errors
    otp_expired: "OTP has expired. Please request a new one.",
    otp_disabled: "OTP authentication is currently disabled.",
    // Token/JWT errors
    bad_jwt: "Invalid authentication token. Please sign in again.",
    session_expired: "Your session has expired. Please sign in again.",
    session_not_found: "Session not found. Please sign in again.",
    refresh_token_not_found: "Authentication expired. Please sign in again.",
    refresh_token_already_used: "Authentication expired. Please sign in again.",
    // User/account errors
    user_not_found: "User account not found.",
    user_banned: "Your account has been temporarily suspended.",
    email_not_confirmed: "Please verify your email address before signing in.",
    phone_not_confirmed: "Please verify your phone number before signing in.",
    invalid_credentials: "Invalid email or password.",
    // Signup/registration errors
    signup_disabled: "New account registration is currently disabled.",
    email_exists: "An account with this email already exists.",
    phone_exists: "An account with this phone number already exists.",
    weak_password: "Password does not meet security requirements.",
    email_address_invalid: "Please enter a valid email address.",
    email_address_not_authorized: "This email address is not authorized for registration.",
    // Provider/OAuth errors
    provider_disabled: "This sign-in method is currently disabled.",
    oauth_provider_not_supported: "This sign-in provider is not supported.",
    provider_email_needs_verification: "Please verify your email address to complete sign-in.",
    // Validation errors
    validation_failed: "Please check your input and try again.",
    bad_json: "Invalid request format. Please try again.",
    // MFA errors
    mfa_challenge_expired: "MFA challenge expired. Please try again.",
    mfa_verification_failed: "Invalid MFA code. Please try again.",
    insufficient_aal: "Additional authentication required.",
    // CAPTCHA errors
    captcha_failed: "CAPTCHA verification failed. Please try again.",
    // General errors
    conflict: "A conflict occurred. Please try again.",
    request_timeout: "Request timed out. Please try again.",
    unexpected_failure: "An unexpected error occurred. Please try again.",
    same_password: "New password must be different from your current password.",
    // Flow state errors
    flow_state_expired: "Authentication session expired. Please start over.",
    flow_state_not_found: "Authentication session not found. Please start over.",
    // Reauthentication errors
    reauthentication_needed: "Please verify your identity to continue.",
    reauthentication_not_valid: "Identity verification failed. Please try again."
};
/**
 * Default error message for unknown error codes
 */ const DEFAULT_ERROR_MESSAGE = "An error occurred. Please try again.";
function handleSupabaseAuthError(error) {
    if (!error) {
        return DEFAULT_ERROR_MESSAGE;
    }
    // Check if it's an AuthError with a code property
    if ('code' in error && error.code) {
        const userMessage = AUTH_ERROR_MESSAGES[error.code];
        if (userMessage) {
            return userMessage;
        }
    }
    // Check if it's an AuthError with a message we can parse for specific cases
    if (error.message) {
        const message = error.message.toLowerCase();
        // Handle common network-related messages first
        if (message.includes('failed to fetch') || message.includes('network request failed') || message.includes('network error')) {
            return "Network error. Please check your internet connection and try again.";
        }
        // Handle other common message patterns that might not have specific codes
        if (message.includes('token has expired') || message.includes('expired')) {
            return "Your session has expired. Please sign in again.";
        }
        if (message.includes('invalid token') || message.includes('invalid otp')) {
            return "Invalid code. Please check and try again.";
        }
        if (message.includes('rate limit') || message.includes('too many')) {
            return "Too many attempts. Please wait before trying again.";
        }
        if (message.includes('email already exists') || message.includes('user already exists')) {
            return "An account with this email already exists.";
        }
        if (message.includes('invalid email')) {
            return "Please enter a valid email address.";
        }
        if (message.includes('weak password')) {
            return "Password does not meet security requirements.";
        }
    }
    // Return default message for unknown errors
    return DEFAULT_ERROR_MESSAGE;
}
function isRateLimitError(error) {
    if (!error) return false;
    if ('code' in error && error.code) {
        return [
            'over_email_send_rate_limit',
            'over_request_rate_limit',
            'over_sms_send_rate_limit'
        ].includes(error.code);
    }
    return false;
}
function isEmailRateLimitError(error) {
    if (!error) return false;
    if ('code' in error && error.code) {
        return error.code === 'over_email_send_rate_limit';
    }
    return false;
}
function isTemporaryError(error) {
    if (!error) return false;
    if ('code' in error && error.code) {
        return [
            'over_email_send_rate_limit',
            'over_request_rate_limit',
            'over_sms_send_rate_limit',
            'request_timeout',
            'conflict',
            'unexpected_failure'
        ].includes(error.code);
    }
    return false;
}
function getRetryDelay(error) {
    if (!error || !isRateLimitError(error)) return null;
    if ('code' in error && error.code) {
        switch(error.code){
            case 'over_email_send_rate_limit':
                return 60; // 1 minute for email rate limits
            case 'over_sms_send_rate_limit':
                return 60; // 1 minute for SMS rate limits
            case 'over_request_rate_limit':
                return 300; // 5 minutes for general rate limits
            default:
                return 60;
        }
    }
    return null;
}
}}),
"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40476f0ade05992429e5520119c8940bc9ac50a28d":"loginWithMobilePassword","40c382d01f32ba231e3b87edecb6a907b27fe5705e":"verifyOTP","40c4645d03ce93888084da699909d1cb8ed4c08028":"sendOTP"},"",""] */ __turbopack_context__.s({
    "loginWithMobilePassword": (()=>loginWithMobilePassword),
    "sendOTP": (()=>sendOTP),
    "verifyOTP": (()=>verifyOTP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/schemas/authSchemas.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$supabaseErrorHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/supabaseErrorHandler.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
// Validate email format
function validateEmail(email) {
    if (!email) {
        return {
            isValid: false,
            message: 'Email is required'
        };
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        return {
            isValid: false,
            message: 'Please enter a valid email address'
        };
    }
    return {
        isValid: true
    };
}
// Validate OTP format
function validateOTP(otp) {
    if (!otp) {
        return {
            isValid: false,
            message: 'OTP is required'
        };
    }
    if (otp.length !== 6) {
        return {
            isValid: false,
            message: 'OTP must be 6 digits'
        };
    }
    if (!/^\d{6}$/.test(otp)) {
        return {
            isValid: false,
            message: 'OTP must contain only numbers'
        };
    }
    return {
        isValid: true
    };
}
async function sendOTP(values) {
    const { email } = values;
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
        return {
            success: false,
            error: emailValidation.message
        };
    }
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        const { error } = await supabase.auth.signInWithOtp({
            email: email,
            options: {
                // Set shouldCreateUser to true to allow new user registration
                shouldCreateUser: true,
                data: {
                    auth_type: "email"
                }
            }
        });
        if (error) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$supabaseErrorHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmailRateLimitError"])(error)) {
                return {
                    success: false,
                    error: "Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.",
                    isConfigurationError: true
                };
            }
            return {
                success: false,
                error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$supabaseErrorHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["handleSupabaseAuthError"])(error)
            };
        }
        return {
            success: true,
            message: "OTP sent to your email address. Please check your inbox."
        };
    } catch (error) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$supabaseErrorHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmailRateLimitError"])(error)) {
            return {
                success: false,
                error: "Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.",
                isConfigurationError: true
            };
        }
        return {
            success: false,
            error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$supabaseErrorHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["handleSupabaseAuthError"])(error)
        };
    }
}
async function verifyOTP(values) {
    const { email, otp } = values;
    const otpValidation = validateOTP(otp);
    if (!otpValidation.isValid) {
        return {
            success: false,
            error: otpValidation.message
        };
    }
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { data, error } = await supabase.auth.verifyOtp({
            email: email,
            token: otp,
            type: 'email'
        });
        if (error) {
            return {
                success: false,
                error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$supabaseErrorHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["handleSupabaseAuthError"])(error)
            };
        }
        return {
            success: true,
            data: data,
            message: "Successfully signed in!"
        };
    } catch (error) {
        return {
            success: false,
            error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$supabaseErrorHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["handleSupabaseAuthError"])(error)
        };
    }
}
async function loginWithMobilePassword(values) {
    const validatedFields = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$schemas$2f$authSchemas$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MobilePasswordLoginSchema"].safeParse(values);
    if (!validatedFields.success) {
        return {
            success: false,
            error: "Invalid mobile number or password format"
        };
    }
    const { mobile, password } = validatedFields.data;
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Format mobile number with +91 prefix
        const phoneNumber = `+91${mobile}`;
        const { data, error } = await supabase.auth.signInWithPassword({
            phone: phoneNumber,
            password: password
        });
        if (error) {
            return {
                success: false,
                error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$supabaseErrorHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["handleSupabaseAuthError"])(error)
            };
        }
        return {
            success: true,
            data: data,
            message: "Successfully signed in!"
        };
    } catch (error) {
        return {
            success: false,
            error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$supabaseErrorHandler$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["handleSupabaseAuthError"])(error)
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    sendOTP,
    verifyOTP,
    loginWithMobilePassword
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(sendOTP, "40c4645d03ce93888084da699909d1cb8ed4c08028", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(verifyOTP, "40c382d01f32ba231e3b87edecb6a907b27fe5705e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(loginWithMobilePassword, "40476f0ade05992429e5520119c8940bc9ac50a28d", null);
}}),
"[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)");
;
;
;
}}),
"[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$login$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => "[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "40476f0ade05992429e5520119c8940bc9ac50a28d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["loginWithMobilePassword"]),
    "40c382d01f32ba231e3b87edecb6a907b27fe5705e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifyOTP"]),
    "40c4645d03ce93888084da699909d1cb8ed4c08028": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendOTP"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$login$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => "[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "40476f0ade05992429e5520119c8940bc9ac50a28d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$login$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40476f0ade05992429e5520119c8940bc9ac50a28d"]),
    "40c382d01f32ba231e3b87edecb6a907b27fe5705e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$login$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40c382d01f32ba231e3b87edecb6a907b27fe5705e"]),
    "40c4645d03ce93888084da699909d1cb8ed4c08028": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$login$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40c4645d03ce93888084da699909d1cb8ed4c08028"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$login$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => "[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$main$292f$login$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f28$main$292f$login$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(main)/login/page/actions.js { ACTIONS_MODULE0 => "[project]/app/(main)/login/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(main)/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(main)/login/LoginForm.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoginForm": (()=>LoginForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const LoginForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(main)/login/LoginForm.tsx <module evaluation>", "LoginForm");
}}),
"[project]/app/(main)/login/LoginForm.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoginForm": (()=>LoginForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const LoginForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(main)/login/LoginForm.tsx", "LoginForm");
}}),
"[project]/app/(main)/login/LoginForm.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$LoginForm$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/(main)/login/LoginForm.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$LoginForm$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/(main)/login/LoginForm.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$LoginForm$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/(main)/components/auth/AuthPageBackground.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(main)/components/auth/AuthPageBackground.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(main)/components/auth/AuthPageBackground.tsx <module evaluation>", "default");
}}),
"[project]/app/(main)/components/auth/AuthPageBackground.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(main)/components/auth/AuthPageBackground.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(main)/components/auth/AuthPageBackground.tsx", "default");
}}),
"[project]/app/(main)/components/auth/AuthPageBackground.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$components$2f$auth$2f$AuthPageBackground$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/(main)/components/auth/AuthPageBackground.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$components$2f$auth$2f$AuthPageBackground$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/(main)/components/auth/AuthPageBackground.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$components$2f$auth$2f$AuthPageBackground$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/(main)/login/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LoginPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$LoginForm$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/login/LoginForm.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$components$2f$auth$2f$AuthPageBackground$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(main)/components/auth/AuthPageBackground.tsx [app-rsc] (ecmascript)");
;
;
;
;
async function generateMetadata() {
    const title = "Sign In";
    const description = "Sign in to your Dukancard account or create a new account with just your email address.";
    return {
        title,
        description,
        robots: "noindex, follow"
    };
}
function LoginPage() {
    return(// Use semantic background and add top padding
    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full min-h-[calc(100vh-80px)] md:min-h-[calc(100vh-64px)] flex items-center justify-center bg-neutral-50 dark:bg-neutral-950 p-2 sm:p-4 pt-6 pb-20 md:pb-6 relative overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$components$2f$auth$2f$AuthPageBackground$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(main)/login/page.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Suspense"], {
                fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col justify-center items-center min-h-screen gap-2 relative z-10",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[var(--brand-gold)]"
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/login/page.tsx",
                            lineNumber: 29,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-muted-foreground",
                            children: "Loading sign in form..."
                        }, void 0, false, {
                            fileName: "[project]/app/(main)/login/page.tsx",
                            lineNumber: 30,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(main)/login/page.tsx",
                    lineNumber: 28,
                    columnNumber: 11
                }, void 0),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$main$292f$login$2f$LoginForm$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LoginForm"], {}, void 0, false, {
                    fileName: "[project]/app/(main)/login/page.tsx",
                    lineNumber: 34,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(main)/login/page.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(main)/login/page.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, this));
}
}}),
"[project]/app/(main)/login/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(main)/login/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__96936fa4._.js.map