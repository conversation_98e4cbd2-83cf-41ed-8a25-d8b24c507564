"use server";

import { createClient } from "@/utils/supabase/server";
import { Tables } from "@/types/supabase";

type Blogs = Tables<'blogs'>;
import { calculateReadingTime } from "@/lib/utils/markdown";

export interface BlogSearchParams {
  query?: string;
  sort?: "newest" | "oldest";
  page?: number;
  limit?: number;
}

export type BlogListItem = Blogs;

export interface BlogSearchResult {
  blogs: BlogListItem[];
  total: number;
  page: number;
  totalPages: number;
  hasMore: boolean;
}

const DEFAULT_LIMIT = 12;

/**
 * Search and paginate blogs with database-level operations
 */
export async function searchBlogs(
  params: BlogSearchParams
): Promise<BlogSearchResult> {
  const supabase = await createClient();

  const {
    query = "",
    sort = "newest",
    page = 1,
    limit = DEFAULT_LIMIT,
  } = params;

  try {
    // Build the base query
    let supabaseQuery = supabase
      .from("blogs")
      .select(
        `
        id,
        title,
        slug,
        excerpt,
        featured_image_url,
        author_name,
        categories,
        tags,
        content,
        published_at
      `,
        { count: "exact" }
      )
      .eq("status", "published");

    // Apply search filter if query exists
    if (query.trim()) {
      supabaseQuery = supabaseQuery.or(
        `title.ilike.%${query.trim()}%,content.ilike.%${query.trim()}%,excerpt.ilike.%${query.trim()}%`
      );
    }

    // Apply sorting
    supabaseQuery = supabaseQuery.order("published_at", {
      ascending: sort === "oldest",
    });

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    supabaseQuery = supabaseQuery.range(from, to);

    const { data, error, count } = await supabaseQuery;

    if (error) {
      console.error("Error searching blogs:", error);
      throw new Error("Failed to search blogs");
    }

    // Calculate reading time for each blog
    const blogs: BlogListItem[] = (data || []).map(
      (blog: { content: string | null; [key: string]: unknown }) =>
        ({
          ...blog,
          // Add missing required fields with default values
          author_email: null,
          status: "published",
          meta_title: null,
          meta_description: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          reading_time_minutes: calculateReadingTime(blog.content || ""),
        } as BlogListItem)
    );

    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    return {
      blogs,
      total: totalCount,
      page,
      totalPages,
      hasMore: page < totalPages,
    };
  } catch (error) {
    console.error("Error in searchBlogs:", error);
    throw new Error("Failed to search blogs");
  }
}

/**
 * Get all available blog categories (for future use if needed)
 */
export async function getBlogCategories(): Promise<string[]> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase
      .from("blogs")
      .select("categories")
      .eq("status", "published");

    if (error) {
      console.error("Error fetching blog categories:", error);
      return [];
    }

    // Extract unique categories
    const allCategories = data
      .flatMap((blog: { categories: string[] | null }) => blog.categories || [])
      .filter((category: string, index: number, array: string[]) => array.indexOf(category) === index)
      .sort();

    return allCategories;
  } catch (error) {
    console.error("Error in getBlogCategories:", error);
    return [];
  }
}
