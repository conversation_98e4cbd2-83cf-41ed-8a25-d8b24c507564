{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_a5b8fa46._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_3918d6b0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ncBpXed9UwQbPz0a/bmL1chBGgqOiGXjJNJo5COomUs=", "__NEXT_PREVIEW_MODE_ID": "1d0a9b30d314ff9500e660e8743a3935", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "608c6de9b3868fb1ae37e95659bfaacc8ef7b95efc60f5854ed4ad225d73d234", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "36de9c139f72fc05c7d12f11c29593cb3c1d3e01f11b7bc4c9e047ffb5ad355f"}}}, "instrumentation": null, "functions": {}}