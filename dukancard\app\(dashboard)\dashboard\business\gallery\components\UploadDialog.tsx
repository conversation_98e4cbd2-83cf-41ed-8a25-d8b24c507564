import { Upload, Loader2 } from "lucide-react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { UploadState } from "../types/galleryTypes";


interface UploadDialogProps {
  uploadState: UploadState;
  isDragging: boolean;
  imagesCount: number;
  galleryLimit: number;
  canAddMore: boolean;
  onOpenChange: (_open: boolean) => void;
  onFileChange: (_e: React.ChangeEvent<HTMLInputElement>) => void;
  onDragEnter: (_e: React.DragEvent<HTMLDivElement>) => void;
  onDragOver: (_e: React.DragEvent<HTMLDivElement>) => void;
  onDragLeave: (_e: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (_e: React.DragEvent<HTMLDivElement>) => void;
  onUpload: () => void;
  onClearPreview: () => void;
}

export default function UploadDialog({
  uploadState,
  isDragging,
  imagesCount: _imagesCount,
  galleryLimit: _galleryLimit,
  canAddMore,
  onOpenChange,
  onFileChange,
  onDragEnter,
  onDragOver,
  onDragLeave,
  onDrop,
  onUpload,
  onClearPreview,
}: UploadDialogProps) {
  return (
    <Dialog open={uploadState.uploadDialogOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Gallery Image</DialogTitle>
          <DialogDescription>
            Add a new photo to showcase your business
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          {uploadState.previewUrl ? (
            <div className="relative aspect-square overflow-hidden rounded-lg border shadow-md">
              <Image
                src={uploadState.previewUrl}
                alt="Preview"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute top-2 right-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/20"
                  onClick={onClearPreview}
                >
                  Change
                </Button>
              </div>
            </div>
          ) : (
            <div
              className={cn(
                "flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-12 transition-all duration-200",
                !canAddMore
                  ? "border-neutral-200 dark:border-neutral-700 bg-neutral-100/50 dark:bg-neutral-800/50 opacity-60"
                  : isDragging
                  ? "border-primary bg-primary/10"
                  : "border-neutral-200 dark:border-neutral-700 bg-muted/30 hover:bg-muted/50"
              )}
              onDragEnter={onDragEnter}
              onDragOver={onDragOver}
              onDragLeave={onDragLeave}
              onDrop={onDrop}
            >
              <div className={cn(
                "w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-200",
                !canAddMore
                  ? "bg-neutral-200/50 dark:bg-neutral-700/50"
                  : isDragging
                  ? "bg-primary/20 scale-110"
                  : "bg-primary/10"
              )}>
                <Upload className={cn(
                  "h-8 w-8 transition-all duration-200",
                  !canAddMore
                    ? "text-neutral-400 dark:text-neutral-600"
                    : isDragging
                    ? "text-primary opacity-100"
                    : "text-primary opacity-80"
                )} />
              </div>
              <label
                htmlFor="gallery-image"
                className={cn(
                  "text-center",
                  canAddMore ? "cursor-pointer" : "cursor-not-allowed"
                )}
              >
                <span className={cn(
                  "text-sm font-medium transition-all duration-200",
                  !canAddMore
                    ? "text-neutral-500 dark:text-neutral-400"
                    : isDragging
                    ? "text-primary"
                    : "text-primary"
                )}>
                  {!canAddMore
                    ? "Gallery limit reached"
                    : isDragging
                    ? "Drop image here"
                    : "Click to upload"
                  }
                </span>
                <span className="text-sm text-muted-foreground block mt-1">
                  {!canAddMore
                    ? "Remove images to upload new ones"
                    : !isDragging && "or drag and drop"
                  }
                </span>
                {canAddMore && (
                  <span className="text-xs text-muted-foreground block mt-1">
                    JPG, PNG, WebP, or GIF (max. 15MB)
                  </span>
                )}
                <input
                  id="gallery-image"
                  type="file"
                  accept="image/jpeg,image/png,image/webp,image/gif"
                  className="hidden"
                  onChange={onFileChange}
                  disabled={!canAddMore}
                />
              </label>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={uploadState.isUploading}
            className="border-neutral-200 hover:bg-neutral-100 dark:border-neutral-800 dark:hover:bg-neutral-800"
          >
            Cancel
          </Button>
          <Button
            onClick={onUpload}
            disabled={!uploadState.selectedFile || uploadState.isUploading || !canAddMore}
            className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium"
          >
            {uploadState.isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : !canAddMore ? (
              "Gallery Limit Reached"
            ) : (
              "Upload"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
