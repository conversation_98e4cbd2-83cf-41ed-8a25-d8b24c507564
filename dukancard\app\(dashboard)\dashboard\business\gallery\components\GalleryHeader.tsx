import { motion } from "framer-motion";
import { Camera } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface GalleryHeaderProps {
  canAddMore: boolean;
  isUploading: boolean;
  onUploadClick: () => void;
}

export default function GalleryHeader({
  canAddMore,
  isUploading,
  onUploadClick,
}: GalleryHeaderProps) {
  return (
    <motion.div
      className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60"
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.1 }}
    >
      <div className="space-y-1">
        <div className="flex items-center gap-3 mb-2">
          <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
            <Camera className="w-5 h-5 text-primary" />
          </div>
          <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
          <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
            Media Management
          </div>
        </div>
        <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
          Gallery Management
        </h1>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed">
          Showcase your business with beautiful photos and manage your visual content with drag-and-drop organization.
        </p>
      </div>

      <div className="flex items-center gap-3">
        <Button
          onClick={onUploadClick}
          disabled={!canAddMore || isUploading}
          className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium"
          size="lg"
        >
          <Camera className="mr-2 h-4 w-4" />
          {isUploading ? "Uploading..." : "Add Photo"}
        </Button>
      </div>
    </motion.div>
  );
}
