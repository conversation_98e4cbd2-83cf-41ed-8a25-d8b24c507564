"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  LayoutDashboard,
  CreditCard,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import DashboardOverviewClient from "./DashboardOverviewClient";
import AnimatedSubscriptionStatus from "./AnimatedSubscriptionStatus";
import EnhancedQuickActions from "./EnhancedQuickActions";
import BusinessStatusAlert from "./BusinessStatusAlert";
import { PricingPlan } from "@/lib/PricingPlans";

interface BusinessDashboardClientProps {
  initialProfile: {
    business_name: string;
    business_slug: string;
    plan_id: string | null;
    plan_cycle: string | null;
    has_active_subscription: boolean | null;
    trial_end_date: string | null;
    total_likes: number;
    total_subscriptions: number;
    average_rating: number;
    logo_url: string | null;
    title: string | null;
    status: string; // "online" or "offline"
  };
  userId: string;
  subscriptionStatus: "active" | "trial" | "inactive";
  planDetails: PricingPlan | undefined;
  subscription?: {
    subscription_status: string | null;
    plan_id: string | null;
    plan_cycle: string | null;
  } | null;
}

export default function BusinessDashboardClient({
  initialProfile,
  userId,
  subscriptionStatus,
  planDetails,
  subscription,
}: BusinessDashboardClientProps) {
  const [_unreadActivitiesCount, setUnreadActivitiesCount] = useState(0);

  /**
   * Fetch Unread Activities Count
   *
   * This effect fetches the initial unread activities count and sets up an interval
   * to refresh it every minute. This ensures the dashboard shows accurate counts
   * even if the realtime subscription misses any events.
   *
   * Note: For realtime updates of the activities count, you need to enable realtime
   * for the business_activities table in Supabase:
   * 1. Go to Supabase Dashboard > Database > Replication
   * 2. Find the "business_activities" table in the list
   * 3. Enable realtime by toggling it on
   */
  useEffect(() => {
    // Activities feature is disabled
    setUnreadActivitiesCount(0);
  }, [userId]);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-10"
    >
      {/* Welcome Section - Full Width */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60">
        <div className="space-y-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
              <LayoutDashboard className="w-5 h-5 text-primary" />
            </div>
            <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
            <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
              Business Dashboard
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
            Welcome, {initialProfile.business_name || 'Business Owner'}
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed">
            Monitor your business performance, track key metrics, and manage your digital presence with real-time insights.
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Link href="/dashboard/business/card">
            <Button
              className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-2.5 h-auto font-medium"
              size="lg"
            >
              <CreditCard className="mr-2 h-4 w-4" />
              Manage Card
            </Button>
          </Link>
        </div>
      </div>

      {/* Business Status Alert - Show only when status is offline */}
      {initialProfile.status === "offline" && (
        <motion.div variants={itemVariants}>
          <BusinessStatusAlert />
        </motion.div>
      )}

      {/* Dashboard Overview Client - Full Width */}
      <motion.div variants={itemVariants}>
        <DashboardOverviewClient
          initialProfile={initialProfile}
          userId={userId}
          userPlan={initialProfile.plan_id}
        />
      </motion.div>

      {/* Subscription Status and Quick Actions - Full Width */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Animated Subscription Status */}
          <AnimatedSubscriptionStatus
            subscriptionStatus={subscriptionStatus}
            planDetails={planDetails}
            trialEndDate={initialProfile.trial_end_date}
            planCycle={initialProfile.plan_cycle}
            subscription={subscription}
          />

          {/* Enhanced Quick Actions */}
          <EnhancedQuickActions userPlan={initialProfile.plan_id} />
        </div>
      </motion.div>

      {/* Recent Activities - Full Width */}
      <motion.div variants={itemVariants}>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Activities</h3>
          <p className="text-gray-600">Activities feature is coming soon.</p>
        </div>
      </motion.div>
    </motion.div>
  );
}
