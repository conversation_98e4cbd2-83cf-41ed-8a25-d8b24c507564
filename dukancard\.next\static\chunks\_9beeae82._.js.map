{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { Database } from \"@/types/supabase\";\r\n\r\nimport { z } from 'zod';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { IndianMobileSchema } from '@/lib/schemas/authSchemas';\r\n\r\n// Define the schema for profile updates\r\nconst ProfileSchema = z.object({\r\n  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),\r\n  // Add other fields here if needed in the future\r\n});\r\n\r\n// Define the schema for phone updates\r\nconst PhoneSchema = z.object({\r\n  phone: IndianMobileSchema,\r\n});\r\n\r\n// Define the schema for address updates with proper validation\r\nconst AddressSchema = z.object({\r\n  address: z\r\n    .string()\r\n    .max(100, { message: \"Address cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  pincode: z\r\n    .string()\r\n    .min(1, { message: \"Pincode is required\" })\r\n    .regex(/^\\d{6}$/, { message: \"Must be a valid 6-digit pincode\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"City cannot be empty\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"State cannot be empty\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"Locality cannot be empty\" }),\r\n});\r\n\r\n// Define the schema for email updates\r\nconst EmailSchema = z.object({\r\n  email: z.string().email({ message: \"Please enter a valid email address\" }),\r\n});\r\n\r\n// Define the schema for mobile updates\r\nconst MobileSchema = z.object({\r\n  mobile: IndianMobileSchema,\r\n});\r\n\r\n// Define the unified schema for profile and address updates\r\nconst UnifiedProfileSchema = z.object({\r\n  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),\r\n  address: z\r\n    .string()\r\n    .max(100, { message: \"Address cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  pincode: z\r\n    .string()\r\n    .min(1, { message: \"Pincode is required\" })\r\n    .regex(/^\\d{6}$/, { message: \"Must be a valid 6-digit pincode\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  city: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  state: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  locality: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n});\r\n\r\nexport type ProfileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    name?: string[];\r\n    // Add other fields here\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type PhoneFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    phone?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type AddressFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    address?: string[];\r\n    pincode?: string[];\r\n    city?: string[];\r\n    state?: string[];\r\n    locality?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type EmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type MobileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    mobile?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type UnifiedProfileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    name?: string[];\r\n    address?: string[];\r\n    pincode?: string[];\r\n    city?: string[];\r\n    state?: string[];\r\n    locality?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerProfile(\r\n  prevState: ProfileFormState,\r\n  formData: FormData\r\n): Promise<ProfileFormState> {\r\n  const supabase = await createClient(); // Added await\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = ProfileSchema.safeParse({\r\n    name: formData.get('name'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { name } = validatedFields.data;\r\n\r\n  try {\r\n    // Update name in auth.users table (full_name in user_metadata)\r\n    // The database trigger will automatically sync this to customer_profiles table\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      data: { full_name: name }\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth user metadata:', authUpdateError);\r\n      return { message: `Auth Error: ${authUpdateError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate the profile page and potentially the layout to reflect name change\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer/layout'); // To update sidebar/header name\r\n\r\n    return { message: 'Profile updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating profile:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerAddress(\r\n  prevState: AddressFormState,\r\n  formData: FormData\r\n): Promise<AddressFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = AddressSchema.safeParse({\r\n    address: formData.get('address'),\r\n    pincode: formData.get('pincode'),\r\n    city: formData.get('city'),\r\n    state: formData.get('state'),\r\n    locality: formData.get('locality'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { address, pincode, city, state, locality } = validatedFields.data;\r\n\r\n  try {\r\n    const { error: updateError } = await supabase\r\n      .from('customer_profiles')\r\n      .update({\r\n        address: address || null,\r\n        pincode,\r\n        city,\r\n        state,\r\n        locality\r\n      })\r\n      .eq('id', user.id);\r\n\r\n    if (updateError) {\r\n      console.error('Error updating customer address:', updateError);\r\n      return { message: `Database Error: ${updateError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return { message: 'Address updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating address:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerPhone(\r\n  prevState: PhoneFormState,\r\n  formData: FormData\r\n): Promise<PhoneFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = PhoneSchema.safeParse({\r\n    phone: formData.get('phone'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { phone } = validatedFields.data;\r\n\r\n  try {\r\n    // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n    // Update phone in customer_profiles table\r\n    const { error: updateError } = await supabase\r\n      .from('customer_profiles')\r\n      .update({ phone: phone })\r\n      .eq('id', user.id);\r\n\r\n    if (updateError) {\r\n      console.error('Error updating customer phone:', updateError);\r\n      return { message: `Database Error: ${updateError.message}`, success: false };\r\n    }\r\n\r\n    // Update phone in Supabase auth.users table to maintain user ID consistency\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${phone}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.warn('Failed to update auth phone field:', authUpdateError.message);\r\n      // Don't fail the operation for this, just log the warning\r\n      // The customer_profiles table is updated successfully\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return { message: 'Phone number updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating phone:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerEmail(\r\n  prevState: EmailFormState,\r\n  formData: FormData\r\n): Promise<EmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = EmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user registered with Google OAuth (matching settings page logic)\r\n    const isGoogleLogin = user.app_metadata?.provider === 'google';\r\n\r\n    // Check if the user has email/password authentication\r\n    let hasEmailAuth = false;\r\n\r\n    if (isGoogleLogin && user.email) {\r\n      try {\r\n        // Use admin client to check user identities\r\n        const supabase = await createClient();\r\n        const { data: authData } = await supabase.auth.admin.getUserById(user.id);\r\n\r\n        // Check if the user has email/password authentication\r\n        if (authData?.user?.identities) {\r\n          hasEmailAuth = authData.user.identities.some(\r\n            (identity: { provider: string; }) => identity.provider === \"email\"\r\n          );\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking user auth methods:\", error);\r\n        hasEmailAuth = false;\r\n      }\r\n    }\r\n\r\n    // Only disable email changes if they're using Google and don't have email auth\r\n    const shouldDisableEmailChange = isGoogleLogin && !hasEmailAuth;\r\n\r\n    if (shouldDisableEmailChange) {\r\n      return {\r\n        message: 'Email cannot be changed for Google accounts. Your email is linked to your Google account.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Check if email is the same as current\r\n    if (user.email === email) {\r\n      return {\r\n        message: 'Email address is the same as current.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Update email in Supabase auth.users table\r\n    // This is the primary source of truth for email\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth email:', authUpdateError);\r\n\r\n      // Provide user-friendly error messages\r\n      let errorMessage = 'Failed to update email address.';\r\n      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {\r\n        errorMessage = 'This email address is already in use by another account.';\r\n      } else if (authUpdateError.message.includes('check constraint')) {\r\n        errorMessage = 'Invalid email format provided.';\r\n      } else if (authUpdateError.message.includes('rate limit')) {\r\n        errorMessage = 'Too many requests. Please try again later.';\r\n      }\r\n\r\n      return {\r\n        message: errorMessage,\r\n        success: false\r\n      };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: 'Email address updated successfully! You may need to verify the new email address.',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating email:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerMobile(\r\n  prevState: MobileFormState,\r\n  formData: FormData\r\n): Promise<MobileFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = MobileSchema.safeParse({\r\n    mobile: formData.get('mobile'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { mobile } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if mobile is the same as current\r\n    const currentMobile = user.phone ? user.phone.replace(/^\\+91/, '') : '';\r\n    if (mobile === currentMobile) {\r\n      return {\r\n        message: 'Mobile number is the same as current.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Note: Mobile uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n    // Update mobile in Supabase auth.users table\r\n    // This is the primary source of truth for mobile\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${mobile}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth mobile:', authUpdateError);\r\n\r\n      // Provide user-friendly error messages\r\n      let errorMessage = 'Failed to update mobile number.';\r\n      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {\r\n        errorMessage = 'This mobile number is already in use by another account.';\r\n      } else if (authUpdateError.message.includes('check constraint')) {\r\n        errorMessage = 'Invalid mobile number format provided.';\r\n      } else if (authUpdateError.message.includes('rate limit')) {\r\n        errorMessage = 'Too many requests. Please try again later.';\r\n      }\r\n\r\n      return {\r\n        message: errorMessage,\r\n        success: false\r\n      };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: 'Mobile number updated successfully!',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating mobile:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerProfileAndAddress(\r\n  prevState: UnifiedProfileFormState,\r\n  formData: FormData\r\n): Promise<UnifiedProfileFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UnifiedProfileSchema.safeParse({\r\n    name: formData.get('name'),\r\n    address: formData.get('address'),\r\n    pincode: formData.get('pincode'),\r\n    city: formData.get('city'),\r\n    state: formData.get('state'),\r\n    locality: formData.get('locality'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { name, address, pincode, city, state, locality } = validatedFields.data;\r\n\r\n  // Validate that if any address field is provided, required fields are present\r\n  const hasAnyAddressField = pincode || city || state || locality;\r\n  if (hasAnyAddressField) {\r\n    if (!pincode || !city || !state || !locality) {\r\n      return {\r\n        message: 'If providing address information, pincode, city, state, and locality are required.',\r\n        success: false,\r\n      };\r\n    }\r\n  }\r\n\r\n  try {\r\n    // Update name in auth.users table (full_name in user_metadata)\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      data: { full_name: name }\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth user metadata:', authUpdateError);\r\n      return { message: `Auth Error: ${authUpdateError.message}`, success: false };\r\n    }\r\n\r\n    // Update address in customer_profiles table if address data is provided\r\n    if (hasAnyAddressField) {\r\n      const { error: updateError } = await supabase\r\n        .from('customer_profiles')\r\n        .update({\r\n          address: address || null,\r\n          pincode,\r\n          city,\r\n          state,\r\n          locality\r\n        })\r\n        .eq('id', user.id);\r\n\r\n      if (updateError) {\r\n        console.error('Error updating customer address:', updateError);\r\n        return { message: `Database Error: ${updateError.message}`, success: false };\r\n      }\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer/layout');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: hasAnyAddressField\r\n        ? 'Profile and address updated successfully!'\r\n        : 'Profile updated successfully!',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating profile and address:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8IsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/ProfileForm.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState, useTransition, forwardRef, useImperativeHandle } from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { z } from 'zod';\r\nimport { updateCustomerProfile, type ProfileFormState } from './actions';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { toast } from 'sonner';\r\nimport { User, Save, Loader2 } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Re-define schema slightly for client-side use with react-hook-form\r\nconst ProfileFormSchema = z.object({\r\n  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),\r\n});\r\n\r\ntype ProfileFormData = z.infer<typeof ProfileFormSchema>;\r\n\r\ninterface ProfileFormProps {\r\n  initialName: string | null;\r\n  hideSubmitButton?: boolean;\r\n}\r\n\r\nexport interface ProfileFormRef {\r\n  getFormData: () => ProfileFormData | null;\r\n  validateForm: () => boolean;\r\n  getFormErrors: () => Record<string, unknown>;\r\n}\r\n\r\nexport const ProfileForm = forwardRef<ProfileFormRef, ProfileFormProps>(\r\n  function ProfileForm({ initialName, hideSubmitButton = false }, ref) {\r\n    const [isPending, startTransition] = useTransition();\r\n    const [formState, setFormState] = useState<ProfileFormState>({\r\n      message: null,\r\n      errors: {},\r\n      success: false\r\n    });\r\n\r\n    const form = useForm<ProfileFormData>({\r\n      resolver: zodResolver(ProfileFormSchema),\r\n      defaultValues: {\r\n        name: initialName || '',\r\n      },\r\n      mode: 'onChange', // Validate on change\r\n    });\r\n\r\n    // Expose form methods via ref\r\n    useImperativeHandle(ref, () => ({\r\n      getFormData: () => {\r\n        // Always return current values - let the parent handle validation\r\n        const values = form.getValues();\r\n        return values;\r\n      },\r\n      validateForm: () => {\r\n        // Trigger validation and return the result\r\n        form.trigger();\r\n        const hasErrors = Object.keys(form.formState.errors).length > 0;\r\n        const hasValidName = (values: ProfileFormData) => Boolean(values.name && values.name.trim().length > 0);\r\n        const currentValues = form.getValues();\r\n        return !hasErrors && hasValidName(currentValues);\r\n      },\r\n      getFormErrors: () => {\r\n        return form.formState.errors;\r\n      }\r\n    }));\r\n\r\n  useEffect(() => {\r\n    console.log('Form state changed:', formState);\r\n\r\n    // Check if we've received a response from the server\r\n    if (formState.message !== null || Object.keys(formState.errors || {}).length > 0) {\r\n      console.log('Response received from server');\r\n\r\n      if (formState.success) {\r\n        toast.success(formState.message || 'Profile updated successfully!');\r\n        // Optionally reset form or redirect, but revalidation handles UI update\r\n      } else if (!formState.success) {\r\n        // Show general errors if they exist and aren't field specific\r\n        // Field specific errors are handled by react-hook-form\r\n        if (!formState.errors || Object.keys(formState.errors).length === 0) {\r\n           toast.error(formState.message);\r\n        }\r\n      }\r\n    }\r\n  }, [formState]);\r\n\r\n   // Update default value if initialName changes after mount (e.g., due to revalidation)\r\n   useEffect(() => {\r\n    if (initialName) {\r\n      form.reset({ name: initialName });\r\n    }\r\n  }, [initialName, form]);\r\n\r\n\r\n  // Handle form submission with React's startTransition\r\n  const onSubmit = async (data: ProfileFormData) => {\r\n    console.log('Form submission started');\r\n\r\n    // Create FormData from the form values\r\n    const formData = new FormData();\r\n    formData.append('name', data.name);\r\n\r\n    // Use startTransition to handle the server action\r\n    startTransition(async () => {\r\n      try {\r\n        console.log('Dispatching form data to server action');\r\n\r\n        // Create initial state to pass to the server action\r\n        const initialState: ProfileFormState = {\r\n          message: null,\r\n          errors: {},\r\n          success: false\r\n        };\r\n\r\n        // Call the server action with the initial state and form data\r\n        const result = await updateCustomerProfile(initialState, formData);\r\n        console.log('Server action completed:', result);\r\n\r\n        // Update the local form state with the result\r\n        setFormState(result);\r\n      } catch (error) {\r\n        console.error('Error submitting form:', error);\r\n        setFormState({\r\n          message: 'An unexpected error occurred. Please try again.',\r\n          success: false,\r\n          errors: {}\r\n        });\r\n        toast.error('An unexpected error occurred. Please try again.');\r\n      }\r\n    });\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\" data-testid=\"profile-form\">\r\n      {/* Display non-field specific errors from server action */}\r\n      {formState.message && !formState.success && (Object.keys(formState.errors || {}).length === 0) && (\r\n        <div className=\"p-3 rounded-md bg-red-50 dark:bg-red-950/30 text-red-600 dark:text-red-400 text-sm\">\r\n          {formState.message}\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"name\" className=\"text-sm font-medium text-neutral-700 dark:text-neutral-300\">\r\n          Full Name\r\n        </Label>\r\n        <div className=\"relative\">\r\n          <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400 dark:text-neutral-500\" />\r\n          <Input\r\n            id=\"name\"\r\n            {...form.register('name')}\r\n            className={cn(\r\n              \"pl-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800\",\r\n              \"focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]\",\r\n              \"transition-all duration-200\",\r\n              isPending && \"opacity-70\"\r\n            )}\r\n            placeholder=\"Your full name\"\r\n            aria-invalid={!!form.formState.errors.name || !!formState.errors?.name}\r\n            aria-describedby=\"name-error\"\r\n            disabled={isPending}\r\n          />\r\n        </div>\r\n        {/* Client-side validation error */}\r\n        {form.formState.errors.name && (\r\n          <p id=\"name-error\" className=\"text-sm font-medium text-red-500 dark:text-red-400 mt-1\">\r\n            {form.formState.errors.name.message}\r\n          </p>\r\n        )}\r\n        {/* Server-side validation error */}\r\n        {formState.errors?.name && (\r\n          <p id=\"name-error-server\" className=\"text-sm font-medium text-red-500 dark:text-red-400 mt-1\">\r\n            {formState.errors.name.join(', ')}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      {!hideSubmitButton && (\r\n        <div className=\"mt-6 flex justify-end\">\r\n          <Button\r\n            type=\"submit\"\r\n            disabled={isPending || !form.formState.isValid}\r\n            className=\"bg-primary hover:bg-primary/90 text-primary-foreground\"\r\n          >\r\n            {isPending ? (\r\n              <>\r\n                <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                Saving...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Save className=\"h-4 w-4 mr-2\" />\r\n                Save Changes\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </form>\r\n  );\r\n});\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AAZA;;;;;;;;;;;;AAcA,qEAAqE;AACrE,MAAM,oBAAoB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,KAAK;AAC3D;AAeO,MAAM,4BAAc,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,UAClC,SAAS,YAAY,EAAE,WAAW,EAAE,mBAAmB,KAAK,EAAE,EAAE,GAAG;;IACjE,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAC3D,SAAS;QACT,QAAQ,CAAC;QACT,SAAS;IACX;IAEA,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QACpC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM,eAAe;QACvB;QACA,MAAM;IACR;IAEA,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;uDAAK,IAAM,CAAC;gBAC9B,WAAW;mEAAE;wBACX,kEAAkE;wBAClE,MAAM,SAAS,KAAK,SAAS;wBAC7B,OAAO;oBACT;;gBACA,YAAY;mEAAE;wBACZ,2CAA2C;wBAC3C,KAAK,OAAO;wBACZ,MAAM,YAAY,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG;wBAC9D,MAAM;wFAAe,CAAC,SAA4B,QAAQ,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG;;wBACrG,MAAM,gBAAgB,KAAK,SAAS;wBACpC,OAAO,CAAC,aAAa,aAAa;oBACpC;;gBACA,aAAa;mEAAE;wBACb,OAAO,KAAK,SAAS,CAAC,MAAM;oBAC9B;;YACF,CAAC;;IAEH,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,qDAAqD;YACrD,IAAI,UAAU,OAAO,KAAK,QAAQ,OAAO,IAAI,CAAC,UAAU,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,GAAG;gBAChF,QAAQ,GAAG,CAAC;gBAEZ,IAAI,UAAU,OAAO,EAAE;oBACrB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,UAAU,OAAO,IAAI;gBACnC,wEAAwE;gBAC1E,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;oBAC7B,8DAA8D;oBAC9D,uDAAuD;oBACvD,IAAI,CAAC,UAAU,MAAM,IAAI,OAAO,IAAI,CAAC,UAAU,MAAM,EAAE,MAAM,KAAK,GAAG;wBAClE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,UAAU,OAAO;oBAChC;gBACF;YACF;QACF;4CAAG;QAAC;KAAU;IAEb,sFAAsF;IACtF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACT,IAAI,aAAa;gBACf,KAAK,KAAK,CAAC;oBAAE,MAAM;gBAAY;YACjC;QACF;4CAAG;QAAC;QAAa;KAAK;IAGtB,sDAAsD;IACtD,MAAM,WAAW,OAAO;QACtB,QAAQ,GAAG,CAAC;QAEZ,uCAAuC;QACvC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,KAAK,IAAI;QAEjC,kDAAkD;QAClD,gBAAgB;YACd,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,oDAAoD;gBACpD,MAAM,eAAiC;oBACrC,SAAS;oBACT,QAAQ,CAAC;oBACT,SAAS;gBACX;gBAEA,8DAA8D;gBAC9D,MAAM,SAAS,MAAM,CAAA,GAAA,mMAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc;gBACzD,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,8CAA8C;gBAC9C,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,aAAa;oBACX,SAAS;oBACT,SAAS;oBACT,QAAQ,CAAC;gBACX;gBACA,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU,KAAK,YAAY,CAAC;QAAW,WAAU;QAAY,eAAY;;YAE5E,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,IAAK,OAAO,IAAI,CAAC,UAAU,MAAM,IAAI,CAAC,GAAG,MAAM,KAAK,mBAC1F,6LAAC;gBAAI,WAAU;0BACZ,UAAU,OAAO;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6HAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAO,WAAU;kCAA6D;;;;;;kCAG7F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACF,GAAG,KAAK,QAAQ,CAAC,OAAO;gCACzB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2EACA,sFACA,+BACA,aAAa;gCAEf,aAAY;gCACZ,gBAAc,CAAC,CAAC,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,UAAU,MAAM,EAAE;gCAClE,oBAAiB;gCACjB,UAAU;;;;;;;;;;;;oBAIb,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,kBACzB,6LAAC;wBAAE,IAAG;wBAAa,WAAU;kCAC1B,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;;;;;;oBAItC,UAAU,MAAM,EAAE,sBACjB,6LAAC;wBAAE,IAAG;wBAAoB,WAAU;kCACjC,UAAU,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;YAKjC,CAAC,kCACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,UAAU,aAAa,CAAC,KAAK,SAAS,CAAC,OAAO;oBAC9C,WAAU;8BAET,0BACC;;0CACE,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAA8B;;qDAInD;;0CACE,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AASjD;;QAxKyC,6JAAA,CAAA,gBAAa;QAOrC,iKAAA,CAAA,UAAO;;;;QAPiB,6JAAA,CAAA,gBAAa;QAOrC,iKAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { Database } from \"@/types/supabase\";\r\n\r\nimport { z } from 'zod';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { IndianMobileSchema } from '@/lib/schemas/authSchemas';\r\n\r\n// Define the schema for profile updates\r\nconst ProfileSchema = z.object({\r\n  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),\r\n  // Add other fields here if needed in the future\r\n});\r\n\r\n// Define the schema for phone updates\r\nconst PhoneSchema = z.object({\r\n  phone: IndianMobileSchema,\r\n});\r\n\r\n// Define the schema for address updates with proper validation\r\nconst AddressSchema = z.object({\r\n  address: z\r\n    .string()\r\n    .max(100, { message: \"Address cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  pincode: z\r\n    .string()\r\n    .min(1, { message: \"Pincode is required\" })\r\n    .regex(/^\\d{6}$/, { message: \"Must be a valid 6-digit pincode\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"City cannot be empty\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"State cannot be empty\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"Locality cannot be empty\" }),\r\n});\r\n\r\n// Define the schema for email updates\r\nconst EmailSchema = z.object({\r\n  email: z.string().email({ message: \"Please enter a valid email address\" }),\r\n});\r\n\r\n// Define the schema for mobile updates\r\nconst MobileSchema = z.object({\r\n  mobile: IndianMobileSchema,\r\n});\r\n\r\n// Define the unified schema for profile and address updates\r\nconst UnifiedProfileSchema = z.object({\r\n  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),\r\n  address: z\r\n    .string()\r\n    .max(100, { message: \"Address cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  pincode: z\r\n    .string()\r\n    .min(1, { message: \"Pincode is required\" })\r\n    .regex(/^\\d{6}$/, { message: \"Must be a valid 6-digit pincode\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  city: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  state: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  locality: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n});\r\n\r\nexport type ProfileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    name?: string[];\r\n    // Add other fields here\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type PhoneFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    phone?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type AddressFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    address?: string[];\r\n    pincode?: string[];\r\n    city?: string[];\r\n    state?: string[];\r\n    locality?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type EmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type MobileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    mobile?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type UnifiedProfileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    name?: string[];\r\n    address?: string[];\r\n    pincode?: string[];\r\n    city?: string[];\r\n    state?: string[];\r\n    locality?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerProfile(\r\n  prevState: ProfileFormState,\r\n  formData: FormData\r\n): Promise<ProfileFormState> {\r\n  const supabase = await createClient(); // Added await\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = ProfileSchema.safeParse({\r\n    name: formData.get('name'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { name } = validatedFields.data;\r\n\r\n  try {\r\n    // Update name in auth.users table (full_name in user_metadata)\r\n    // The database trigger will automatically sync this to customer_profiles table\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      data: { full_name: name }\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth user metadata:', authUpdateError);\r\n      return { message: `Auth Error: ${authUpdateError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate the profile page and potentially the layout to reflect name change\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer/layout'); // To update sidebar/header name\r\n\r\n    return { message: 'Profile updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating profile:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerAddress(\r\n  prevState: AddressFormState,\r\n  formData: FormData\r\n): Promise<AddressFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = AddressSchema.safeParse({\r\n    address: formData.get('address'),\r\n    pincode: formData.get('pincode'),\r\n    city: formData.get('city'),\r\n    state: formData.get('state'),\r\n    locality: formData.get('locality'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { address, pincode, city, state, locality } = validatedFields.data;\r\n\r\n  try {\r\n    const { error: updateError } = await supabase\r\n      .from('customer_profiles')\r\n      .update({\r\n        address: address || null,\r\n        pincode,\r\n        city,\r\n        state,\r\n        locality\r\n      })\r\n      .eq('id', user.id);\r\n\r\n    if (updateError) {\r\n      console.error('Error updating customer address:', updateError);\r\n      return { message: `Database Error: ${updateError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return { message: 'Address updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating address:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerPhone(\r\n  prevState: PhoneFormState,\r\n  formData: FormData\r\n): Promise<PhoneFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = PhoneSchema.safeParse({\r\n    phone: formData.get('phone'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { phone } = validatedFields.data;\r\n\r\n  try {\r\n    // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n    // Update phone in customer_profiles table\r\n    const { error: updateError } = await supabase\r\n      .from('customer_profiles')\r\n      .update({ phone: phone })\r\n      .eq('id', user.id);\r\n\r\n    if (updateError) {\r\n      console.error('Error updating customer phone:', updateError);\r\n      return { message: `Database Error: ${updateError.message}`, success: false };\r\n    }\r\n\r\n    // Update phone in Supabase auth.users table to maintain user ID consistency\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${phone}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.warn('Failed to update auth phone field:', authUpdateError.message);\r\n      // Don't fail the operation for this, just log the warning\r\n      // The customer_profiles table is updated successfully\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return { message: 'Phone number updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating phone:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerEmail(\r\n  prevState: EmailFormState,\r\n  formData: FormData\r\n): Promise<EmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = EmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user registered with Google OAuth (matching settings page logic)\r\n    const isGoogleLogin = user.app_metadata?.provider === 'google';\r\n\r\n    // Check if the user has email/password authentication\r\n    let hasEmailAuth = false;\r\n\r\n    if (isGoogleLogin && user.email) {\r\n      try {\r\n        // Use admin client to check user identities\r\n        const supabase = await createClient();\r\n        const { data: authData } = await supabase.auth.admin.getUserById(user.id);\r\n\r\n        // Check if the user has email/password authentication\r\n        if (authData?.user?.identities) {\r\n          hasEmailAuth = authData.user.identities.some(\r\n            (identity: { provider: string; }) => identity.provider === \"email\"\r\n          );\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking user auth methods:\", error);\r\n        hasEmailAuth = false;\r\n      }\r\n    }\r\n\r\n    // Only disable email changes if they're using Google and don't have email auth\r\n    const shouldDisableEmailChange = isGoogleLogin && !hasEmailAuth;\r\n\r\n    if (shouldDisableEmailChange) {\r\n      return {\r\n        message: 'Email cannot be changed for Google accounts. Your email is linked to your Google account.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Check if email is the same as current\r\n    if (user.email === email) {\r\n      return {\r\n        message: 'Email address is the same as current.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Update email in Supabase auth.users table\r\n    // This is the primary source of truth for email\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth email:', authUpdateError);\r\n\r\n      // Provide user-friendly error messages\r\n      let errorMessage = 'Failed to update email address.';\r\n      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {\r\n        errorMessage = 'This email address is already in use by another account.';\r\n      } else if (authUpdateError.message.includes('check constraint')) {\r\n        errorMessage = 'Invalid email format provided.';\r\n      } else if (authUpdateError.message.includes('rate limit')) {\r\n        errorMessage = 'Too many requests. Please try again later.';\r\n      }\r\n\r\n      return {\r\n        message: errorMessage,\r\n        success: false\r\n      };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: 'Email address updated successfully! You may need to verify the new email address.',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating email:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerMobile(\r\n  prevState: MobileFormState,\r\n  formData: FormData\r\n): Promise<MobileFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = MobileSchema.safeParse({\r\n    mobile: formData.get('mobile'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { mobile } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if mobile is the same as current\r\n    const currentMobile = user.phone ? user.phone.replace(/^\\+91/, '') : '';\r\n    if (mobile === currentMobile) {\r\n      return {\r\n        message: 'Mobile number is the same as current.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Note: Mobile uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n    // Update mobile in Supabase auth.users table\r\n    // This is the primary source of truth for mobile\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${mobile}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth mobile:', authUpdateError);\r\n\r\n      // Provide user-friendly error messages\r\n      let errorMessage = 'Failed to update mobile number.';\r\n      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {\r\n        errorMessage = 'This mobile number is already in use by another account.';\r\n      } else if (authUpdateError.message.includes('check constraint')) {\r\n        errorMessage = 'Invalid mobile number format provided.';\r\n      } else if (authUpdateError.message.includes('rate limit')) {\r\n        errorMessage = 'Too many requests. Please try again later.';\r\n      }\r\n\r\n      return {\r\n        message: errorMessage,\r\n        success: false\r\n      };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: 'Mobile number updated successfully!',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating mobile:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerProfileAndAddress(\r\n  prevState: UnifiedProfileFormState,\r\n  formData: FormData\r\n): Promise<UnifiedProfileFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UnifiedProfileSchema.safeParse({\r\n    name: formData.get('name'),\r\n    address: formData.get('address'),\r\n    pincode: formData.get('pincode'),\r\n    city: formData.get('city'),\r\n    state: formData.get('state'),\r\n    locality: formData.get('locality'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { name, address, pincode, city, state, locality } = validatedFields.data;\r\n\r\n  // Validate that if any address field is provided, required fields are present\r\n  const hasAnyAddressField = pincode || city || state || locality;\r\n  if (hasAnyAddressField) {\r\n    if (!pincode || !city || !state || !locality) {\r\n      return {\r\n        message: 'If providing address information, pincode, city, state, and locality are required.',\r\n        success: false,\r\n      };\r\n    }\r\n  }\r\n\r\n  try {\r\n    // Update name in auth.users table (full_name in user_metadata)\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      data: { full_name: name }\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth user metadata:', authUpdateError);\r\n      return { message: `Auth Error: ${authUpdateError.message}`, success: false };\r\n    }\r\n\r\n    // Update address in customer_profiles table if address data is provided\r\n    if (hasAnyAddressField) {\r\n      const { error: updateError } = await supabase\r\n        .from('customer_profiles')\r\n        .update({\r\n          address: address || null,\r\n          pincode,\r\n          city,\r\n          state,\r\n          locality\r\n        })\r\n        .eq('id', user.id);\r\n\r\n      if (updateError) {\r\n        console.error('Error updating customer address:', updateError);\r\n        return { message: `Database Error: ${updateError.message}`, success: false };\r\n      }\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer/layout');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: hasAnyAddressField\r\n        ? 'Profile and address updated successfully!'\r\n        : 'Profile updated successfully!',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating profile and address:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAkMsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-2\", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,6HAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/location.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { Database } from \"@/types/supabase\";\r\n\r\n\r\n// --- Pincode Lookup Action ---\r\nexport async function getPincodeDetails(pincode: string): Promise<{\r\n  data?: {\r\n    city: string;\r\n    state: string;\r\n    localities: string[];\r\n  };\r\n  city?: string;\r\n  state?: string;\r\n  localities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!pincode || !/^\\d{6}$/.test(pincode)) {\r\n    return { error: \"Invalid Pincode format.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n  try {\r\n    // First get city and state from pincodes table\r\n    const { data: pincodeData, error: pincodeError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"OfficeName, DivisionName, StateName\")\r\n      .eq(\"Pincode\", pincode) // Updated column name to match database\r\n      .order(\"OfficeName\");\r\n\r\n    if (pincodeError) {\r\n      console.error(\"Pincode Fetch Error:\", pincodeError);\r\n      return { error: \"Database error fetching pincode details.\" };\r\n    }\r\n\r\n    if (!pincodeData || pincodeData.length === 0) {\r\n      return { error: \"Pincode not found.\" };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = pincodeData[0].StateName;\r\n\r\n    // Use DivisionName as the city (already cleaned)\r\n    const city = pincodeData[0].DivisionName;\r\n\r\n    // Get unique localities from post office names\r\n    const localities = [\r\n      ...new Set(pincodeData.map((item: { OfficeName: string }) => item.OfficeName)),\r\n    ] as string[];\r\n\r\n    return {\r\n      data: { city, state, localities },\r\n      city,\r\n      state,\r\n      localities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"Pincode Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during pincode lookup.\" };\r\n  }\r\n}\r\n// --- End Pincode Lookup ---\r\n\r\n// --- City Lookup Action ---\r\nexport async function getCityDetails(city: string): Promise<{\r\n  data?: {\r\n    pincodes: string[];\r\n    state: string;\r\n    localities: string[];\r\n  };\r\n  pincodes?: string[];\r\n  state?: string;\r\n  localities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!city || city.length < 2) {\r\n    return { error: \"City name must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n  try {\r\n    // Get pincodes and state for the city - DivisionName is the city column\r\n    const { data: cityData, error: cityError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"Pincode, OfficeName, StateName, DivisionName\")\r\n      .ilike(\"DivisionName\", `%${city}%`)\r\n      .order(\"Pincode\");\r\n\r\n    if (cityError) {\r\n      console.error(\"City Fetch Error:\", cityError);\r\n      return { error: \"Database error fetching city details.\" };\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      return { error: \"City not found.\" };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = cityData[0].StateName;\r\n\r\n    // Get unique pincodes\r\n    const pincodes = [...new Set(cityData.map((item: { Pincode: string }) => item.Pincode))] as string[];\r\n\r\n    // Get unique localities from post office names\r\n    const localities = [\r\n      ...new Set(cityData.map((item: { OfficeName: string }) => item.OfficeName)),\r\n    ] as string[];\r\n\r\n    return {\r\n      data: { pincodes, state, localities },\r\n      pincodes,\r\n      state,\r\n      localities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"City Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during city lookup.\" };\r\n  }\r\n}\r\n// --- End City Lookup ---\r\n\r\n// --- City Autocomplete Action ---\r\n/**\r\n * Get city suggestions based on a search query\r\n *\r\n * This function uses the Supabase PostgreSQL function 'get_distinct_cities' to fetch unique city names.\r\n * The PostgreSQL function is defined as:\r\n *\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION get_distinct_cities(search_query TEXT, result_limit INTEGER)\r\n * RETURNS TABLE(city TEXT) AS $$\r\n * BEGIN\r\n *   RETURN QUERY\r\n *   SELECT DISTINCT \"DivisionName\" as city\r\n *   FROM pincodes\r\n *   WHERE \"DivisionName\" ILIKE search_query\r\n *   ORDER BY \"DivisionName\"\r\n *   LIMIT result_limit;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n * ```\r\n *\r\n * @param query The search query (minimum 2 characters)\r\n * @returns Array of up to 5 unique city suggestions\r\n */\r\nexport async function getCitySuggestions(query: string): Promise<{\r\n  data?: {\r\n    cities: string[];\r\n  };\r\n  cities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!query || query.length < 2) {\r\n    return { error: \"Query must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabase = (await createClient()) as SupabaseClient<Database>;\r\n  try {\r\n    // Use the PostgreSQL function to get distinct cities (up to 5)\r\n    const { data: cityData, error: cityError } = await supabase\r\n      .rpc('get_distinct_cities', {\r\n        search_query: `%${query}%`,\r\n        result_limit: 5\r\n      });\r\n\r\n    if (cityError) {\r\n      console.error(\"City Suggestions Error:\", cityError);\r\n\r\n      // Fallback to regular query if RPC fails\r\n      try {\r\n        // Use a regular query as fallback\r\n        const { data: fallbackData, error: fallbackError } = await supabase\r\n          .from(\"pincodes\")\r\n          .select(\"DivisionName\")\r\n          .ilike(\"DivisionName\", `%${query}%`)\r\n          .order(\"DivisionName\")\r\n          .limit(100);\r\n\r\n        if (fallbackError) {\r\n          throw fallbackError;\r\n        }\r\n\r\n        if (!fallbackData || fallbackData.length === 0) {\r\n          return { data: { cities: [] }, cities: [] };\r\n        }\r\n\r\n        // Get unique cities and format them\r\n        const cities = [...new Set(fallbackData.map((item: { DivisionName: string }) =>\r\n          item.DivisionName.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n        ))] as string[];\r\n\r\n        const topCities = cities.slice(0, 5);\r\n\r\n        return {\r\n          data: { cities: topCities },\r\n          cities: topCities\r\n        };\r\n      } catch (fallbackErr) {\r\n        console.error(\"Fallback City Query Error:\", fallbackErr);\r\n        return { error: \"Database error fetching city suggestions.\" };\r\n      }\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      return { data: { cities: [] }, cities: [] };\r\n    }\r\n\r\n    // Format the city names to Title Case\r\n    const cities = cityData.map((item: { city: string }) =>\r\n      item.city.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n    );\r\n\r\n    return {\r\n      data: { cities },\r\n      cities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"City Suggestions Exception:\", e);\r\n    return { error: \"An unexpected error occurred while fetching city suggestions.\" };\r\n  }\r\n}\r\n// --- End City Autocomplete ---\r\n"], "names": [], "mappings": ";;;;;;IAQsB,oBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/hooks/usePincodeDetails.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { getPincodeDetails } from \"@/lib/actions/location\";\r\n\r\ninterface AddressFormData {\r\n  address?: string;\r\n  pincode: string;\r\n  city: string;\r\n  state: string;\r\n  locality: string;\r\n}\r\n\r\ninterface UsePincodeDetailsOptions {\r\n  form: UseFormReturn<AddressFormData>;\r\n  initialPincode?: string | null;\r\n  initialLocality?: string | null;\r\n}\r\n\r\nexport function usePincodeDetails({\r\n  form,\r\n  initialPincode,\r\n  initialLocality\r\n}: UsePincodeDetailsOptions) {\r\n  const [isPincodeLoading, setIsPincodeLoading] = useState(false);\r\n  const [availableLocalities, setAvailableLocalities] = useState<string[]>([]);\r\n\r\n  // Pincode change handler\r\n  const handlePincodeChange = useCallback(async (pincode: string) => {\r\n    if (pincode.length !== 6) return;\r\n\r\n    setIsPincodeLoading(true);\r\n    setAvailableLocalities([]);\r\n\r\n    // Reset form fields\r\n    form.setValue(\"locality\", \"\");\r\n    form.setValue(\"city\", \"\");\r\n    form.setValue(\"state\", \"\");\r\n\r\n    const result = await getPincodeDetails(pincode);\r\n    setIsPincodeLoading(false);\r\n\r\n    if (result.error) {\r\n      toast.error(result.error);\r\n    } else if (result.city && result.state && result.localities) {\r\n      // Set city and state\r\n      form.setValue(\"city\", result.city, { shouldValidate: true });\r\n      form.setValue(\"state\", result.state, { shouldValidate: true });\r\n\r\n      // Update localities\r\n      setAvailableLocalities(result.localities);\r\n\r\n      // If only one locality, auto-select it\r\n      if (result.localities.length === 1) {\r\n        form.setValue(\"locality\", result.localities[0], {\r\n          shouldValidate: true,\r\n          shouldDirty: true\r\n        });\r\n      }\r\n\r\n      toast.success(\"City and State auto-filled. Please select your locality.\");\r\n    }\r\n  }, [form]);\r\n\r\n  // Effect to fetch localities on initial load if pincode exists\r\n  useEffect(() => {\r\n    if (!initialPincode || initialPincode.length !== 6) return;\r\n\r\n    const fetchAndValidateLocalities = async (pincode: string) => {\r\n      setIsPincodeLoading(true);\r\n      setAvailableLocalities([]);\r\n\r\n      try {\r\n        const result = await getPincodeDetails(pincode);\r\n\r\n        if (result.error) {\r\n          toast.error(`Failed to fetch details for pincode ${pincode}: ${result.error}`);\r\n          setAvailableLocalities([]);\r\n        } else if (result.city && result.state && result.localities) {\r\n          // Set city/state\r\n          form.setValue(\"city\", result.city, { shouldValidate: true });\r\n          form.setValue(\"state\", result.state, { shouldValidate: true });\r\n          setAvailableLocalities(result.localities);\r\n\r\n          // Validate existing locality\r\n          if (initialLocality) {\r\n            const localityExists = result.localities.some(\r\n              loc => loc.toLowerCase() === initialLocality.toLowerCase()\r\n            );\r\n            \r\n            if (localityExists) {\r\n              // Keep the existing locality if it's valid\r\n              form.setValue(\"locality\", initialLocality, {\r\n                shouldValidate: true,\r\n                shouldDirty: false\r\n              });\r\n            } else {\r\n              // Clear invalid locality\r\n              form.setValue(\"locality\", \"\", {\r\n                shouldValidate: true,\r\n                shouldDirty: true\r\n              });\r\n              toast.warning(`The locality \"${initialLocality}\" is not available for pincode ${pincode}. Please select a valid locality.`);\r\n            }\r\n          }\r\n        } else {\r\n          setAvailableLocalities([]);\r\n          toast.warning(`No localities found for pincode ${pincode}.`);\r\n          if (initialLocality) {\r\n            form.setValue(\"locality\", \"\", {\r\n              shouldValidate: true,\r\n              shouldDirty: true\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching pincode details:\", error);\r\n        toast.error(\"An unexpected error occurred while fetching pincode details.\");\r\n        setAvailableLocalities([]);\r\n        if (initialLocality) {\r\n          form.setValue(\"locality\", \"\", {\r\n            shouldValidate: true,\r\n            shouldDirty: true\r\n          });\r\n        }\r\n      } finally {\r\n        setIsPincodeLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchAndValidateLocalities(initialPincode);\r\n  }, [initialPincode, initialLocality, form]);\r\n\r\n  return {\r\n    isPincodeLoading,\r\n    availableLocalities,\r\n    handlePincodeChange\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;;AALA;;;;AAqBO,SAAS,kBAAkB,EAChC,IAAI,EACJ,cAAc,EACd,eAAe,EACU;;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE3E,yBAAyB;IACzB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,OAAO;YAC7C,IAAI,QAAQ,MAAM,KAAK,GAAG;YAE1B,oBAAoB;YACpB,uBAAuB,EAAE;YAEzB,oBAAoB;YACpB,KAAK,QAAQ,CAAC,YAAY;YAC1B,KAAK,QAAQ,CAAC,QAAQ;YACtB,KAAK,QAAQ,CAAC,SAAS;YAEvB,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;YACvC,oBAAoB;YAEpB,IAAI,OAAO,KAAK,EAAE;gBAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU,EAAE;gBAC3D,qBAAqB;gBACrB,KAAK,QAAQ,CAAC,QAAQ,OAAO,IAAI,EAAE;oBAAE,gBAAgB;gBAAK;gBAC1D,KAAK,QAAQ,CAAC,SAAS,OAAO,KAAK,EAAE;oBAAE,gBAAgB;gBAAK;gBAE5D,oBAAoB;gBACpB,uBAAuB,OAAO,UAAU;gBAExC,uCAAuC;gBACvC,IAAI,OAAO,UAAU,CAAC,MAAM,KAAK,GAAG;oBAClC,KAAK,QAAQ,CAAC,YAAY,OAAO,UAAU,CAAC,EAAE,EAAE;wBAC9C,gBAAgB;wBAChB,aAAa;oBACf;gBACF;gBAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF;6DAAG;QAAC;KAAK;IAET,+DAA+D;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;YAEpD,MAAM;0EAA6B,OAAO;oBACxC,oBAAoB;oBACpB,uBAAuB,EAAE;oBAEzB,IAAI;wBACF,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;wBAEvC,IAAI,OAAO,KAAK,EAAE;4BAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,oCAAoC,EAAE,QAAQ,EAAE,EAAE,OAAO,KAAK,EAAE;4BAC7E,uBAAuB,EAAE;wBAC3B,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU,EAAE;4BAC3D,iBAAiB;4BACjB,KAAK,QAAQ,CAAC,QAAQ,OAAO,IAAI,EAAE;gCAAE,gBAAgB;4BAAK;4BAC1D,KAAK,QAAQ,CAAC,SAAS,OAAO,KAAK,EAAE;gCAAE,gBAAgB;4BAAK;4BAC5D,uBAAuB,OAAO,UAAU;4BAExC,6BAA6B;4BAC7B,IAAI,iBAAiB;gCACnB,MAAM,iBAAiB,OAAO,UAAU,CAAC,IAAI;6GAC3C,CAAA,MAAO,IAAI,WAAW,OAAO,gBAAgB,WAAW;;gCAG1D,IAAI,gBAAgB;oCAClB,2CAA2C;oCAC3C,KAAK,QAAQ,CAAC,YAAY,iBAAiB;wCACzC,gBAAgB;wCAChB,aAAa;oCACf;gCACF,OAAO;oCACL,yBAAyB;oCACzB,KAAK,QAAQ,CAAC,YAAY,IAAI;wCAC5B,gBAAgB;wCAChB,aAAa;oCACf;oCACA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,gBAAgB,+BAA+B,EAAE,QAAQ,iCAAiC,CAAC;gCAC5H;4BACF;wBACF,OAAO;4BACL,uBAAuB,EAAE;4BACzB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,gCAAgC,EAAE,QAAQ,CAAC,CAAC;4BAC3D,IAAI,iBAAiB;gCACnB,KAAK,QAAQ,CAAC,YAAY,IAAI;oCAC5B,gBAAgB;oCAChB,aAAa;gCACf;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,uBAAuB,EAAE;wBACzB,IAAI,iBAAiB;4BACnB,KAAK,QAAQ,CAAC,YAAY,IAAI;gCAC5B,gBAAgB;gCAChB,aAAa;4BACf;wBACF;oBACF,SAAU;wBACR,oBAAoB;oBACtB;gBACF;;YAEA,2BAA2B;QAC7B;sCAAG;QAAC;QAAgB;QAAiB;KAAK;IAE1C,OAAO;QACL;QACA;QACA;IACF;AACF;GAvHgB", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/AddressForm.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState, useTran<PERSON>tion, forwardRef, useImperativeHandle } from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { z } from 'zod';\r\nimport { useSearchParams } from 'next/navigation';\r\nimport { updateCustomerAddress, type AddressFormState } from '../actions';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { toast } from 'sonner';\r\nimport { MapPin, Save, Loader2, Globe, Building2, Info } from 'lucide-react';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n  Form,\r\n} from \"@/components/ui/form\";\r\nimport { usePincodeDetails } from './hooks/usePincodeDetails';\r\n\r\n// Address form schema with proper validation\r\nconst AddressFormSchema = z.object({\r\n  address: z\r\n    .string()\r\n    .max(100, { message: \"Address cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  pincode: z\r\n    .string()\r\n    .min(1, { message: \"Pincode is required\" })\r\n    .regex(/^\\d{6}$/, { message: \"Must be a valid 6-digit pincode\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"City cannot be empty\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"State cannot be empty\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"Locality cannot be empty\" }),\r\n});\r\n\r\ntype AddressFormData = z.infer<typeof AddressFormSchema>;\r\n\r\ninterface AddressFormProps {\r\n  initialData?: {\r\n    address?: string | null;\r\n    pincode?: string | null;\r\n    city?: string | null;\r\n    state?: string | null;\r\n    locality?: string | null;\r\n  };\r\n  hideSubmitButton?: boolean;\r\n}\r\n\r\nexport interface AddressFormRef {\r\n  getFormData: () => AddressFormData | null;\r\n  validateForm: () => boolean;\r\n  getFormErrors: () => Record<string, unknown>;\r\n}\r\n\r\nconst AddressForm = forwardRef<AddressFormRef, AddressFormProps>(\r\n  ({ initialData, hideSubmitButton = false }, ref) => {\r\n  const [isPending, startTransition] = useTransition();\r\n  const [formState, setFormState] = useState<AddressFormState>({\r\n    message: null,\r\n    success: false,\r\n    errors: {}\r\n  });\r\n  const searchParams = useSearchParams();\r\n  const redirectMessage = searchParams.get('message');\r\n\r\n  const form = useForm<AddressFormData>({\r\n    resolver: zodResolver(AddressFormSchema),\r\n    defaultValues: {\r\n      address: initialData?.address || '',\r\n      pincode: initialData?.pincode || '',\r\n      city: initialData?.city || '',\r\n      state: initialData?.state || '',\r\n      locality: initialData?.locality || '',\r\n    },\r\n  });\r\n\r\n  // Expose form methods via ref\r\n  useImperativeHandle(ref, () => ({\r\n    getFormData: () => {\r\n      // Always return current values - let the parent handle validation\r\n      const values = form.getValues();\r\n      return values;\r\n    },\r\n    validateForm: () => {\r\n      // Trigger validation and return the result\r\n      form.trigger();\r\n      return Object.keys(form.formState.errors).length === 0;\r\n    },\r\n    getFormErrors: () => {\r\n      return form.formState.errors;\r\n    }\r\n  }));\r\n\r\n  // Use pincode details hook\r\n  const { isPincodeLoading, availableLocalities, handlePincodeChange } =\r\n    usePincodeDetails({\r\n      form,\r\n      initialPincode: initialData?.pincode,\r\n      initialLocality: initialData?.locality,\r\n    });\r\n\r\n  // Handle redirect message toast\r\n  useEffect(() => {\r\n    if (redirectMessage) {\r\n      toast.info(redirectMessage);\r\n    }\r\n  }, [redirectMessage]);\r\n\r\n  // Handle form state changes\r\n  useEffect(() => {\r\n    if (formState.message) {\r\n      if (formState.success) {\r\n        toast.success(formState.message);\r\n        // Reset form state after success\r\n        setFormState({ message: null, success: false, errors: {} });\r\n      } else {\r\n        toast.error(formState.message);\r\n      }\r\n    }\r\n  }, [formState]);\r\n\r\n  const onSubmit = (data: AddressFormData) => {\r\n    const formData = new FormData();\r\n    formData.append('address', data.address || '');\r\n    formData.append('pincode', data.pincode);\r\n    formData.append('city', data.city);\r\n    formData.append('state', data.state);\r\n    formData.append('locality', data.locality);\r\n\r\n    startTransition(async () => {\r\n      try {\r\n        const initialState: AddressFormState = {\r\n          message: null,\r\n          errors: {},\r\n          success: false\r\n        };\r\n\r\n        const result = await updateCustomerAddress(initialState, formData);\r\n        setFormState(result);\r\n      } catch (error) {\r\n        console.error('Error submitting address form:', error);\r\n        setFormState({\r\n          message: 'An unexpected error occurred. Please try again.',\r\n          success: false,\r\n          errors: {}\r\n        });\r\n      }\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Show redirect message if present */}\r\n      {redirectMessage && (\r\n        <Alert className=\"border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/50\">\r\n          <Info className=\"h-4 w-4 text-amber-600 dark:text-amber-400\" />\r\n          <AlertDescription className=\"text-amber-800 dark:text-amber-200\">\r\n            {redirectMessage}\r\n          </AlertDescription>\r\n        </Alert>\r\n      )}\r\n\r\n      <Form {...form}>\r\n        <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\r\n          {/* Address Field (Optional) */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"address\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel className=\"text-sm font-medium\">\r\n                  Address (Optional)\r\n                </FormLabel>\r\n                <FormControl>\r\n                  <Input\r\n                    placeholder=\"e.g., House/Flat No., Street Name\"\r\n                    {...field}\r\n                    value={field.value ?? \"\"}\r\n                    className=\"w-full\"\r\n                  />\r\n                </FormControl>\r\n                <FormDescription className=\"text-xs text-muted-foreground\">\r\n                  Your street address or building details\r\n                </FormDescription>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* Pincode Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"pincode\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                  <Globe className=\"h-4 w-4 text-primary\" />\r\n                  Pincode *\r\n                </FormLabel>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <FormControl className=\"flex-1\">\r\n                    <Input\r\n                      placeholder=\"e.g., 751001\"\r\n                      {...field}\r\n                      value={field.value ?? \"\"}\r\n                      maxLength={6}\r\n                      type=\"number\"\r\n                      onChange={(e) => {\r\n                        field.onChange(e);\r\n                        if (e.target.value.length === 6) {\r\n                          handlePincodeChange(e.target.value);\r\n                        }\r\n                      }}\r\n                      onInput={(e) => {\r\n                        const target = e.target as HTMLInputElement;\r\n                        target.value = target.value.replace(/[^0-9]/g, \"\");\r\n                      }}\r\n                    />\r\n                  </FormControl>\r\n                  {isPincodeLoading && (\r\n                    <Loader2 className=\"h-4 w-4 animate-spin text-primary\" />\r\n                  )}\r\n                </div>\r\n                <FormDescription className=\"text-xs text-muted-foreground\">\r\n                  6-digit pincode to auto-fill city and state\r\n                </FormDescription>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* City and State fields */}\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n            <FormField\r\n              control={form.control}\r\n              name=\"city\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                    <MapPin className=\"h-4 w-4 text-primary/50\" />\r\n                    City *\r\n                  </FormLabel>\r\n                  <FormControl>\r\n                    <Input\r\n                      placeholder=\"Auto-filled from Pincode\"\r\n                      {...field}\r\n                      value={field.value ?? \"\"}\r\n                      className=\"bg-muted cursor-not-allowed\"\r\n                      readOnly\r\n                    />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n\r\n            <FormField\r\n              control={form.control}\r\n              name=\"state\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                    <MapPin className=\"h-4 w-4 text-primary/50\" />\r\n                    State *\r\n                  </FormLabel>\r\n                  <FormControl>\r\n                    <Input\r\n                      placeholder=\"Auto-filled from Pincode\"\r\n                      {...field}\r\n                      value={field.value ?? \"\"}\r\n                      className=\"bg-muted cursor-not-allowed\"\r\n                      readOnly\r\n                    />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n          </div>\r\n\r\n          {/* Locality Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"locality\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                  <Building2 className=\"h-4 w-4 text-primary\" />\r\n                  Locality / Area *\r\n                </FormLabel>\r\n                <Select\r\n                  onValueChange={field.onChange}\r\n                  value={field.value ?? \"\"}\r\n                  disabled={availableLocalities.length === 0}\r\n                >\r\n                  <FormControl>\r\n                    <SelectTrigger\r\n                      disabled={availableLocalities.length === 0}\r\n                      className=\"w-full\"\r\n                    >\r\n                      <SelectValue\r\n                        placeholder={\r\n                          availableLocalities.length === 0\r\n                            ? \"Enter Pincode first\"\r\n                            : \"Select your locality\"\r\n                        }\r\n                      />\r\n                    </SelectTrigger>\r\n                  </FormControl>\r\n                  <SelectContent className=\"w-full\">\r\n                    {availableLocalities.map((loc) => (\r\n                      <SelectItem key={loc} value={loc}>\r\n                        {loc}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n                <FormDescription className=\"text-xs text-muted-foreground\">\r\n                  Select the specific area within the pincode\r\n                </FormDescription>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {!hideSubmitButton && (\r\n            <div className=\"mt-6 flex justify-end\">\r\n              <Button\r\n                type=\"submit\"\r\n                disabled={isPending}\r\n                className=\"bg-primary hover:bg-primary/90 text-primary-foreground\"\r\n              >\r\n                {isPending ? (\r\n                  <>\r\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                    Updating...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Save className=\"h-4 w-4 mr-2\" />\r\n                    Update Address\r\n                  </>\r\n                )}\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  );\r\n});\r\n\r\nAddressForm.displayName = 'AddressForm';\r\n\r\nexport default AddressForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAOA;AASA;;;AA7BA;;;;;;;;;;;;;;;AA+BA,6CAA6C;AAC7C,MAAM,oBAAoB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,SAAS,uIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,KAAK;QAAE,SAAS;IAAwC,GAC5D,QAAQ,GACR,EAAE,CAAC,uIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,SAAS,uIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAsB,GACxC,KAAK,CAAC,WAAW;QAAE,SAAS;IAAkC;IACjE,MAAM,uIAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAmB,GACrC,MAAM,CAAC,CAAC,MAAQ,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG;QAAE,SAAS;IAAuB;IAC5E,OAAO,uIAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,MAAM,CAAC,CAAC,MAAQ,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG;QAAE,SAAS;IAAwB;IAC7E,UAAU,uIAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAuB,GACzC,MAAM,CAAC,CAAC,MAAQ,IAAI,IAAI,GAAG,MAAM,GAAG,GAAG;QAAE,SAAS;IAA2B;AAClF;AAqBA,MAAM,4BAAc,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,UAC3B,CAAC,EAAE,WAAW,EAAE,mBAAmB,KAAK,EAAE,EAAE;;IAC5C,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAC3D,SAAS;QACT,SAAS;QACT,QAAQ,CAAC;IACX;IACA,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,kBAAkB,aAAa,GAAG,CAAC;IAEzC,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QACpC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,SAAS,aAAa,WAAW;YACjC,SAAS,aAAa,WAAW;YACjC,MAAM,aAAa,QAAQ;YAC3B,OAAO,aAAa,SAAS;YAC7B,UAAU,aAAa,YAAY;QACrC;IACF;IAEA,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;2CAAK,IAAM,CAAC;gBAC9B,WAAW;uDAAE;wBACX,kEAAkE;wBAClE,MAAM,SAAS,KAAK,SAAS;wBAC7B,OAAO;oBACT;;gBACA,YAAY;uDAAE;wBACZ,2CAA2C;wBAC3C,KAAK,OAAO;wBACZ,OAAO,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,MAAM,KAAK;oBACvD;;gBACA,aAAa;uDAAE;wBACb,OAAO,KAAK,SAAS,CAAC,MAAM;oBAC9B;;YACF,CAAC;;IAED,2BAA2B;IAC3B,MAAM,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,GAClE,CAAA,GAAA,uMAAA,CAAA,oBAAiB,AAAD,EAAE;QAChB;QACA,gBAAgB,aAAa;QAC7B,iBAAiB,aAAa;IAChC;IAEF,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,iBAAiB;gBACnB,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;YACb;QACF;gCAAG;QAAC;KAAgB;IAEpB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,UAAU,OAAO,EAAE;gBACrB,IAAI,UAAU,OAAO,EAAE;oBACrB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,UAAU,OAAO;oBAC/B,iCAAiC;oBACjC,aAAa;wBAAE,SAAS;wBAAM,SAAS;wBAAO,QAAQ,CAAC;oBAAE;gBAC3D,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,UAAU,OAAO;gBAC/B;YACF;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,WAAW,CAAC;QAChB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,WAAW,KAAK,OAAO,IAAI;QAC3C,SAAS,MAAM,CAAC,WAAW,KAAK,OAAO;QACvC,SAAS,MAAM,CAAC,QAAQ,KAAK,IAAI;QACjC,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;QACnC,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;QAEzC,gBAAgB;YACd,IAAI;gBACF,MAAM,eAAiC;oBACrC,SAAS;oBACT,QAAQ,CAAC;oBACT,SAAS;gBACX;gBAEA,MAAM,SAAS,MAAM,CAAA,GAAA,mMAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc;gBACzD,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,aAAa;oBACX,SAAS;oBACT,SAAS;oBACT,QAAQ,CAAC;gBACX;YACF;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,iCACC,6LAAC,6HAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC,6HAAA,CAAA,mBAAgB;wBAAC,WAAU;kCACzB;;;;;;;;;;;;0BAKP,6LAAC,4HAAA,CAAA,OAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6LAAC;oBAAK,UAAU,KAAK,YAAY,CAAC;oBAAW,WAAU;;sCAErD,6LAAC,4HAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;;sDACP,6LAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,6LAAC,4HAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,6HAAA,CAAA,QAAK;gDACJ,aAAY;gDACX,GAAG,KAAK;gDACT,OAAO,MAAM,KAAK,IAAI;gDACtB,WAAU;;;;;;;;;;;sDAGd,6LAAC,4HAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAgC;;;;;;sDAG3D,6LAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAMlB,6LAAC,4HAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;;sDACP,6LAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAG5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4HAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,6LAAC,6HAAA,CAAA,QAAK;wDACJ,aAAY;wDACX,GAAG,KAAK;wDACT,OAAO,MAAM,KAAK,IAAI;wDACtB,WAAW;wDACX,MAAK;wDACL,UAAU,CAAC;4DACT,MAAM,QAAQ,CAAC;4DACf,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;gEAC/B,oBAAoB,EAAE,MAAM,CAAC,KAAK;4DACpC;wDACF;wDACA,SAAS,CAAC;4DACR,MAAM,SAAS,EAAE,MAAM;4DACvB,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,OAAO,CAAC,WAAW;wDACjD;;;;;;;;;;;gDAGH,kCACC,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;sDAGvB,6LAAC,4HAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAgC;;;;;;sDAG3D,6LAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,4HAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;;8DACP,6LAAC,4HAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAA4B;;;;;;;8DAGhD,6LAAC,4HAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,6HAAA,CAAA,QAAK;wDACJ,aAAY;wDACX,GAAG,KAAK;wDACT,OAAO,MAAM,KAAK,IAAI;wDACtB,WAAU;wDACV,QAAQ;;;;;;;;;;;8DAGZ,6LAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,6LAAC,4HAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;;8DACP,6LAAC,4HAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAA4B;;;;;;;8DAGhD,6LAAC,4HAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,6HAAA,CAAA,QAAK;wDACJ,aAAY;wDACX,GAAG,KAAK;wDACT,OAAO,MAAM,KAAK,IAAI;wDACtB,WAAU;wDACV,QAAQ;;;;;;;;;;;8DAGZ,6LAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;sCAOpB,6LAAC,4HAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;;sDACP,6LAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAGhD,6LAAC,8HAAA,CAAA,SAAM;4CACL,eAAe,MAAM,QAAQ;4CAC7B,OAAO,MAAM,KAAK,IAAI;4CACtB,UAAU,oBAAoB,MAAM,KAAK;;8DAEzC,6LAAC,4HAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,8HAAA,CAAA,gBAAa;wDACZ,UAAU,oBAAoB,MAAM,KAAK;wDACzC,WAAU;kEAEV,cAAA,6LAAC,8HAAA,CAAA,cAAW;4DACV,aACE,oBAAoB,MAAM,KAAK,IAC3B,wBACA;;;;;;;;;;;;;;;;8DAKZ,6LAAC,8HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACtB,oBAAoB,GAAG,CAAC,CAAC,oBACxB,6LAAC,8HAAA,CAAA,aAAU;4DAAW,OAAO;sEAC1B;2DADc;;;;;;;;;;;;;;;;sDAMvB,6LAAC,4HAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAgC;;;;;;sDAG3D,6LAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;wBAKjB,CAAC,kCACA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,0BACC;;sDACE,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;iEAInD;;sDACE,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD;;QAtSuC,6JAAA,CAAA,gBAAa;QAM7B,qIAAA,CAAA,kBAAe;QAGvB,iKAAA,CAAA,UAAO;QA8BlB,uMAAA,CAAA,oBAAiB;;;;QAvCkB,6JAAA,CAAA,gBAAa;QAM7B,qIAAA,CAAA,kBAAe;QAGvB,iKAAA,CAAA,UAAO;QA8BlB,uMAAA,CAAA,oBAAiB;;;;AAiQrB,YAAY,WAAW,GAAG;uCAEX", "debugId": null}}, {"offset": {"line": 1695, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/avatar-actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { getCustomerAvatarPath } from \"@/lib/utils/storage-paths\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\n// Action to upload avatar and return public URL\r\nexport async function uploadAvatarAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"avatarFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No avatar file provided.\" };\r\n  }\r\n\r\n  const allowedTypes = [\"image/png\", \"image/jpeg\", \"image/gif\", \"image/webp\"];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation (15MB limit)\r\n  if (file.size > 15 * 1024 * 1024) {\r\n    return { success: false, error: \"File size must be less than 15MB.\" };\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    const bucketName = \"customers\"; // Plural form - matches the bucket name\r\n    const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000); // Add random milliseconds for extra uniqueness\r\n    const fullPath = getCustomerAvatarPath(userId, timestamp);\r\n\r\n    // Upload the processed image\r\n    const { error: uploadError } = await supabase.storage\r\n      .from(bucketName)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use original file type (already compressed)\r\n        upsert: true,\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Avatar Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload avatar: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // No need to add timestamp to URL as we already have it in the filename\r\n    const { data: urlData } = supabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n\r\n// Action to specifically update only the avatar URL\r\nexport async function updateAvatarUrl(\r\n  avatarUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"customer_profiles\")\r\n    .update({ avatar_url: avatarUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Avatar URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update avatar URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  // Revalidate paths to update the UI\r\n  revalidatePath(\"/dashboard/customer\");\r\n  revalidatePath(\"/dashboard/customer/profile\");\r\n\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Delete customer avatar from storage and update profile\r\n */\r\nexport async function deleteCustomerAvatar(\r\n  avatarUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Extract file path from the public URL\r\n    const filePath = extractFilePathFromUrl(avatarUrl);\r\n    if (!filePath) {\r\n      return { success: false, error: \"Invalid avatar URL provided.\" };\r\n    }\r\n\r\n    // Delete the image from storage\r\n    const { error: deleteError } = await supabase.storage\r\n      .from(\"customers\")\r\n      .remove([filePath]);\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting avatar from storage:\", deleteError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to delete avatar from storage: ${deleteError.message}`,\r\n      };\r\n    }\r\n\r\n    // Update the customer profile to remove the avatar URL\r\n    const { error: updateError } = await supabase\r\n      .from(\"customer_profiles\")\r\n      .update({ avatar_url: null, updated_at: new Date().toISOString() })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      console.error(\r\n        \"Error updating customer profile after avatar deletion:\",\r\n        updateError\r\n      );\r\n      return {\r\n        success: false,\r\n        error: `Failed to update profile after avatar deletion: ${updateError.message}`,\r\n      };\r\n    }\r\n\r\n    // Revalidate paths to update the UI\r\n    revalidatePath(\"/dashboard/customer\");\r\n    revalidatePath(\"/dashboard/customer/profile\");\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Error in deleteCustomerAvatar:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Failed to delete avatar\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Extract file path from URL for deletion\r\n */\r\nfunction extractFilePathFromUrl(url: string): string | null {\r\n  try {\r\n    const urlObj = new URL(url);\r\n    const pathParts = urlObj.pathname.split(\"/\");\r\n\r\n    // Find the bucket name and extract everything after it\r\n    const bucketIndex = pathParts.findIndex(\r\n      (part) => part === \"business\" || part === \"customers\"\r\n    );\r\n\r\n    if (bucketIndex === -1 || bucketIndex === pathParts.length - 1) {\r\n      return null;\r\n    }\r\n\r\n    return pathParts.slice(bucketIndex + 1).join(\"/\");\r\n  } catch (error) {\r\n    console.error(\"Error extracting file path from URL:\", error);\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAOsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1711, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/avatar-actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { getCustomerAvatarPath } from \"@/lib/utils/storage-paths\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\n// Action to upload avatar and return public URL\r\nexport async function uploadAvatarAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"avatarFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No avatar file provided.\" };\r\n  }\r\n\r\n  const allowedTypes = [\"image/png\", \"image/jpeg\", \"image/gif\", \"image/webp\"];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation (15MB limit)\r\n  if (file.size > 15 * 1024 * 1024) {\r\n    return { success: false, error: \"File size must be less than 15MB.\" };\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    const bucketName = \"customers\"; // Plural form - matches the bucket name\r\n    const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000); // Add random milliseconds for extra uniqueness\r\n    const fullPath = getCustomerAvatarPath(userId, timestamp);\r\n\r\n    // Upload the processed image\r\n    const { error: uploadError } = await supabase.storage\r\n      .from(bucketName)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use original file type (already compressed)\r\n        upsert: true,\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Avatar Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload avatar: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // No need to add timestamp to URL as we already have it in the filename\r\n    const { data: urlData } = supabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n\r\n// Action to specifically update only the avatar URL\r\nexport async function updateAvatarUrl(\r\n  avatarUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"customer_profiles\")\r\n    .update({ avatar_url: avatarUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Avatar URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update avatar URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  // Revalidate paths to update the UI\r\n  revalidatePath(\"/dashboard/customer\");\r\n  revalidatePath(\"/dashboard/customer/profile\");\r\n\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Delete customer avatar from storage and update profile\r\n */\r\nexport async function deleteCustomerAvatar(\r\n  avatarUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Extract file path from the public URL\r\n    const filePath = extractFilePathFromUrl(avatarUrl);\r\n    if (!filePath) {\r\n      return { success: false, error: \"Invalid avatar URL provided.\" };\r\n    }\r\n\r\n    // Delete the image from storage\r\n    const { error: deleteError } = await supabase.storage\r\n      .from(\"customers\")\r\n      .remove([filePath]);\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting avatar from storage:\", deleteError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to delete avatar from storage: ${deleteError.message}`,\r\n      };\r\n    }\r\n\r\n    // Update the customer profile to remove the avatar URL\r\n    const { error: updateError } = await supabase\r\n      .from(\"customer_profiles\")\r\n      .update({ avatar_url: null, updated_at: new Date().toISOString() })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      console.error(\r\n        \"Error updating customer profile after avatar deletion:\",\r\n        updateError\r\n      );\r\n      return {\r\n        success: false,\r\n        error: `Failed to update profile after avatar deletion: ${updateError.message}`,\r\n      };\r\n    }\r\n\r\n    // Revalidate paths to update the UI\r\n    revalidatePath(\"/dashboard/customer\");\r\n    revalidatePath(\"/dashboard/customer/profile\");\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Error in deleteCustomerAvatar:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Failed to delete avatar\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Extract file path from URL for deletion\r\n */\r\nfunction extractFilePathFromUrl(url: string): string | null {\r\n  try {\r\n    const urlObj = new URL(url);\r\n    const pathParts = urlObj.pathname.split(\"/\");\r\n\r\n    // Find the bucket name and extract everything after it\r\n    const bucketIndex = pathParts.findIndex(\r\n      (part) => part === \"business\" || part === \"customers\"\r\n    );\r\n\r\n    if (bucketIndex === -1 || bucketIndex === pathParts.length - 1) {\r\n      return null;\r\n    }\r\n\r\n    return pathParts.slice(bucketIndex + 1).join(\"/\");\r\n  } catch (error) {\r\n    console.error(\"Error extracting file path from URL:\", error);\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAqFsB,kBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1727, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/avatar-actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { getCustomerAvatarPath } from \"@/lib/utils/storage-paths\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\n// Action to upload avatar and return public URL\r\nexport async function uploadAvatarAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"avatarFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No avatar file provided.\" };\r\n  }\r\n\r\n  const allowedTypes = [\"image/png\", \"image/jpeg\", \"image/gif\", \"image/webp\"];\r\n  if (!allowedTypes.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation (15MB limit)\r\n  if (file.size > 15 * 1024 * 1024) {\r\n    return { success: false, error: \"File size must be less than 15MB.\" };\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    const bucketName = \"customers\"; // Plural form - matches the bucket name\r\n    const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000); // Add random milliseconds for extra uniqueness\r\n    const fullPath = getCustomerAvatarPath(userId, timestamp);\r\n\r\n    // Upload the processed image\r\n    const { error: uploadError } = await supabase.storage\r\n      .from(bucketName)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use original file type (already compressed)\r\n        upsert: true,\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Avatar Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload avatar: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // No need to add timestamp to URL as we already have it in the filename\r\n    const { data: urlData } = supabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n\r\n// Action to specifically update only the avatar URL\r\nexport async function updateAvatarUrl(\r\n  avatarUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"customer_profiles\")\r\n    .update({ avatar_url: avatarUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Avatar URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update avatar URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  // Revalidate paths to update the UI\r\n  revalidatePath(\"/dashboard/customer\");\r\n  revalidatePath(\"/dashboard/customer/profile\");\r\n\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Delete customer avatar from storage and update profile\r\n */\r\nexport async function deleteCustomerAvatar(\r\n  avatarUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Extract file path from the public URL\r\n    const filePath = extractFilePathFromUrl(avatarUrl);\r\n    if (!filePath) {\r\n      return { success: false, error: \"Invalid avatar URL provided.\" };\r\n    }\r\n\r\n    // Delete the image from storage\r\n    const { error: deleteError } = await supabase.storage\r\n      .from(\"customers\")\r\n      .remove([filePath]);\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting avatar from storage:\", deleteError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to delete avatar from storage: ${deleteError.message}`,\r\n      };\r\n    }\r\n\r\n    // Update the customer profile to remove the avatar URL\r\n    const { error: updateError } = await supabase\r\n      .from(\"customer_profiles\")\r\n      .update({ avatar_url: null, updated_at: new Date().toISOString() })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      console.error(\r\n        \"Error updating customer profile after avatar deletion:\",\r\n        updateError\r\n      );\r\n      return {\r\n        success: false,\r\n        error: `Failed to update profile after avatar deletion: ${updateError.message}`,\r\n      };\r\n    }\r\n\r\n    // Revalidate paths to update the UI\r\n    revalidatePath(\"/dashboard/customer\");\r\n    revalidatePath(\"/dashboard/customer/profile\");\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Error in deleteCustomerAvatar:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Failed to delete avatar\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Extract file path from URL for deletion\r\n */\r\nfunction extractFilePathFromUrl(url: string): string | null {\r\n  try {\r\n    const urlObj = new URL(url);\r\n    const pathParts = urlObj.pathname.split(\"/\");\r\n\r\n    // Find the bucket name and extract everything after it\r\n    const bucketIndex = pathParts.findIndex(\r\n      (part) => part === \"business\" || part === \"customers\"\r\n    );\r\n\r\n    if (bucketIndex === -1 || bucketIndex === pathParts.length - 1) {\r\n      return null;\r\n    }\r\n\r\n    return pathParts.slice(bucketIndex + 1).join(\"/\");\r\n  } catch (error) {\r\n    console.error(\"Error extracting file path from URL:\", error);\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAyHsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/client-image-compression.ts"], "sourcesContent": ["/**\r\n * Client-side image compression using Canvas API\r\n * This avoids memory issues in serverless environments like Google Cloud Run\r\n */\r\n\r\nexport interface CompressionOptions {\r\n  targetSizeKB?: number;\r\n  maxDimension?: number;\r\n  quality?: number;\r\n  format?: \"webp\" | \"jpeg\" | \"png\";\r\n}\r\n\r\nexport interface CompressionResult {\r\n  blob: Blob;\r\n  finalSizeKB: number;\r\n  compressionRatio: number;\r\n  dimensions: { width: number; height: number };\r\n}\r\n\r\n/**\r\n * Compress image on client-side using Canvas API\r\n */\r\nexport async function compressImageClientSide(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const {\r\n    format = \"webp\",\r\n    targetSizeKB = 100,\r\n    maxDimension = 800,\r\n    quality: initialQuality = 0.8,\r\n  } = options;\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      try {\r\n        const canvas = document.createElement(\"canvas\");\r\n        const ctx = canvas.getContext(\"2d\");\r\n\r\n        if (!ctx) {\r\n          reject(new Error(\"Could not get canvas context\"));\r\n          return;\r\n        }\r\n\r\n        // Calculate new dimensions\r\n        let { width, height } = img;\r\n\r\n        if (width > maxDimension || height > maxDimension) {\r\n          if (width > height) {\r\n            height = (height * maxDimension) / width;\r\n            width = maxDimension;\r\n          } else {\r\n            width = (width * maxDimension) / height;\r\n            height = maxDimension;\r\n          }\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Draw and compress\r\n        ctx.drawImage(img, 0, 0, width, height);\r\n\r\n        // Try different quality levels until we hit target size\r\n        let quality = initialQuality;\r\n        let attempts = 0;\r\n        const maxAttempts = 5;\r\n\r\n        const tryCompress = () => {\r\n          canvas.toBlob(\r\n            (blob) => {\r\n              if (!blob) {\r\n                reject(new Error(\"Failed to create blob\"));\r\n                return;\r\n              }\r\n\r\n              const sizeKB = blob.size / 1024;\r\n\r\n              if (\r\n                sizeKB <= targetSizeKB ||\r\n                attempts >= maxAttempts ||\r\n                quality <= 0.1\r\n              ) {\r\n                // Success or max attempts reached\r\n                const compressionRatio = file.size / blob.size;\r\n                resolve({\r\n                  blob,\r\n                  finalSizeKB: Math.round(sizeKB * 100) / 100,\r\n                  compressionRatio: Math.round(compressionRatio * 100) / 100,\r\n                  dimensions: { width, height },\r\n                });\r\n              } else {\r\n                // Try again with lower quality\r\n                attempts++;\r\n                quality = Math.max(0.1, quality - 0.15);\r\n                tryCompress();\r\n              }\r\n            },\r\n            `image/${format}`,\r\n            quality\r\n          );\r\n        };\r\n\r\n        tryCompress();\r\n      } catch (error) {\r\n        reject(error);\r\n      }\r\n    };\r\n\r\n    img.onerror = () => reject(new Error(\"Failed to load image\"));\r\n    img.src = URL.createObjectURL(file);\r\n  });\r\n}\r\n\r\n/**\r\n * Ultra-aggressive client-side compression\r\n */\r\nexport async function compressImageUltraAggressiveClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const originalSizeMB = file.size / (1024 * 1024);\r\n\r\n  // Auto-determine settings based on file size\r\n  let targetSizeKB = 100;\r\n  let maxDimension = 800;\r\n  let quality = 0.7;\r\n\r\n  if (originalSizeMB <= 2) {\r\n    quality = 0.7;\r\n    maxDimension = 800;\r\n    targetSizeKB = 90;\r\n  } else if (originalSizeMB <= 5) {\r\n    quality = 0.55;\r\n    maxDimension = 700;\r\n    targetSizeKB = 80;\r\n  } else if (originalSizeMB <= 10) {\r\n    quality = 0.45;\r\n    maxDimension = 600;\r\n    targetSizeKB = 70;\r\n  } else {\r\n    quality = 0.35;\r\n    maxDimension = 550;\r\n    targetSizeKB = 60;\r\n  }\r\n\r\n  return compressImageClientSide(file, {\r\n    ...options,\r\n    targetSizeKB: options.targetSizeKB || targetSizeKB,\r\n    maxDimension: options.maxDimension || maxDimension,\r\n    quality: options.quality || quality,\r\n  });\r\n}\r\n\r\n/**\r\n * Moderate client-side compression\r\n * Default targets 50KB for avatars, can be overridden via options\r\n */\r\nexport async function compressImageModerateClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  return compressImageClientSide(file, {\r\n    targetSizeKB: 50, // Default to 50KB for avatars\r\n    maxDimension: 400, // Smaller default for avatars\r\n    quality: 0.7, // More aggressive default quality\r\n    ...options,\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAmBM,eAAe,wBACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,EACJ,SAAS,MAAM,EACf,eAAe,GAAG,EAClB,eAAe,GAAG,EAClB,SAAS,iBAAiB,GAAG,EAC9B,GAAG;IAEJ,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,IAAI;gBACF,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,MAAM,MAAM,OAAO,UAAU,CAAC;gBAE9B,IAAI,CAAC,KAAK;oBACR,OAAO,IAAI,MAAM;oBACjB;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;gBAExB,IAAI,QAAQ,gBAAgB,SAAS,cAAc;oBACjD,IAAI,QAAQ,QAAQ;wBAClB,SAAS,AAAC,SAAS,eAAgB;wBACnC,QAAQ;oBACV,OAAO;wBACL,QAAQ,AAAC,QAAQ,eAAgB;wBACjC,SAAS;oBACX;gBACF;gBAEA,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAEhB,oBAAoB;gBACpB,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO;gBAEhC,wDAAwD;gBACxD,IAAI,UAAU;gBACd,IAAI,WAAW;gBACf,MAAM,cAAc;gBAEpB,MAAM,cAAc;oBAClB,OAAO,MAAM,CACX,CAAC;wBACC,IAAI,CAAC,MAAM;4BACT,OAAO,IAAI,MAAM;4BACjB;wBACF;wBAEA,MAAM,SAAS,KAAK,IAAI,GAAG;wBAE3B,IACE,UAAU,gBACV,YAAY,eACZ,WAAW,KACX;4BACA,kCAAkC;4BAClC,MAAM,mBAAmB,KAAK,IAAI,GAAG,KAAK,IAAI;4BAC9C,QAAQ;gCACN;gCACA,aAAa,KAAK,KAAK,CAAC,SAAS,OAAO;gCACxC,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;gCACvD,YAAY;oCAAE;oCAAO;gCAAO;4BAC9B;wBACF,OAAO;4BACL,+BAA+B;4BAC/B;4BACA,UAAU,KAAK,GAAG,CAAC,KAAK,UAAU;4BAClC;wBACF;oBACF,GACA,CAAC,MAAM,EAAE,QAAQ,EACjB;gBAEJ;gBAEA;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;QAEA,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;QACrC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAChC;AACF;AAKO,eAAe,mCACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,iBAAiB,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;IAE/C,6CAA6C;IAC7C,IAAI,eAAe;IACnB,IAAI,eAAe;IACnB,IAAI,UAAU;IAEd,IAAI,kBAAkB,GAAG;QACvB,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,GAAG;QAC9B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,IAAI;QAC/B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO;QACL,UAAU;QACV,eAAe;QACf,eAAe;IACjB;IAEA,OAAO,wBAAwB,MAAM;QACnC,GAAG,OAAO;QACV,cAAc,QAAQ,YAAY,IAAI;QACtC,cAAc,QAAQ,YAAY,IAAI;QACtC,SAAS,QAAQ,OAAO,IAAI;IAC9B;AACF;AAMO,eAAe,4BACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,OAAO,wBAAwB,MAAM;QACnC,cAAc;QACd,cAAc;QACd,SAAS;QACT,GAAG,OAAO;IACZ;AACF", "debugId": null}}, {"offset": {"line": 1865, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/hooks/useAvatarUpload.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useTransition } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport {\r\n  uploadAvatarAndGetUrl,\r\n  updateAvatarUrl,\r\n  deleteCustomerAvatar,\r\n} from \"../avatar-actions\";\r\nimport { compressImageModerateClient } from \"@/lib/utils/client-image-compression\";\r\n\r\nexport type AvatarUploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\ninterface UseAvatarUploadOptions {\r\n  initialAvatarUrl?: string;\r\n  onUpdateAvatar: (_url: string) => void;\r\n}\r\n\r\nexport function useAvatarUpload({ onUpdateAvatar }: UseAvatarUploadOptions) {\r\n  const [avatarUploadStatus, setAvatarUploadStatus] =\r\n    useState<AvatarUploadStatus>(\"idle\");\r\n  const [avatarUploadError, setAvatarUploadError] = useState<string | null>(\r\n    null\r\n  );\r\n  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(null);\r\n  const [isAvatarUploading, startAvatarUploadTransition] = useTransition();\r\n  const [imageToCrop, setImageToCrop] = useState<string | null>(null);\r\n  const [originalFile, setOriginalFile] = useState<File | null>(null);\r\n\r\n  // File selection handler\r\n  const onFileSelect = (file: File | null) => {\r\n    if (localPreviewUrl) {\r\n      URL.revokeObjectURL(localPreviewUrl);\r\n      setLocalPreviewUrl(null);\r\n    }\r\n\r\n    if (file) {\r\n      if (file.size > 15 * 1024 * 1024) {\r\n        toast.error(\"File size must be less than 15MB.\");\r\n        setAvatarUploadStatus(\"idle\");\r\n        setAvatarUploadError(\"File size must be less than 15MB.\");\r\n        setLocalPreviewUrl(null);\r\n        return;\r\n      }\r\n\r\n      // Prepare for cropping\r\n      setOriginalFile(file);\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setImageToCrop(reader.result as string);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    } else {\r\n      setAvatarUploadStatus(\"idle\");\r\n      setAvatarUploadError(null);\r\n      setLocalPreviewUrl(null);\r\n    }\r\n  };\r\n\r\n  // Avatar upload handler\r\n  const handleAvatarUpload = async (file: File) => {\r\n    setAvatarUploadStatus(\"uploading\");\r\n    setAvatarUploadError(null);\r\n\r\n    startAvatarUploadTransition(async () => {\r\n      const formData = new FormData();\r\n      formData.append(\"avatarFile\", file);\r\n\r\n      const result = await uploadAvatarAndGetUrl(formData);\r\n\r\n      if (result.success && result.url) {\r\n        const newAvatarUrl = result.url;\r\n\r\n        // Update preview\r\n        setAvatarUploadStatus(\"success\");\r\n\r\n        // Clean up preview URL\r\n        setLocalPreviewUrl(null);\r\n        if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);\r\n\r\n        toast.success(\"Avatar uploaded successfully!\");\r\n\r\n        // Save URL to DB immediately\r\n        try {\r\n          const updateResult = await updateAvatarUrl(newAvatarUrl);\r\n          if (!updateResult.success) {\r\n            toast.error(\r\n              `Avatar uploaded, but failed to save URL: ${updateResult.error}`\r\n            );\r\n          }\r\n\r\n          // Update parent component state after successful DB save\r\n          if (updateResult.success) {\r\n            onUpdateAvatar(newAvatarUrl);\r\n          }\r\n        } catch (err) {\r\n          console.error(\"Error saving avatar URL:\", err);\r\n          toast.error(\"Error saving avatar URL after upload.\");\r\n        }\r\n      } else {\r\n        setAvatarUploadStatus(\"error\");\r\n        const errorMessage = result.error || \"Failed to upload avatar.\";\r\n        setAvatarUploadError(errorMessage);\r\n        setLocalPreviewUrl(null);\r\n        if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);\r\n\r\n        // Show user-friendly error message\r\n        if (errorMessage.includes(\"File size must be less than 15MB\")) {\r\n          toast.error(\"Image too large\", {\r\n            description: \"Please select an image smaller than 15MB\",\r\n          });\r\n        } else if (errorMessage.includes(\"Invalid file type\")) {\r\n          toast.error(\"Invalid file type\", {\r\n            description: \"Please select a JPG, PNG, WebP, or GIF image\",\r\n          });\r\n        } else {\r\n          toast.error(\"Upload failed\", {\r\n            description: errorMessage,\r\n          });\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  // Handle crop completion\r\n  const handleCropComplete = async (croppedBlob: Blob | null) => {\r\n    setImageToCrop(null); // Close dialog\r\n\r\n    if (croppedBlob && originalFile) {\r\n      try {\r\n        // Convert blob to file for compression\r\n        const croppedFile = new File([croppedBlob], originalFile.name, {\r\n          type: \"image/png\", // Canvas outputs PNG\r\n        });\r\n\r\n        // Compress image on client-side first - targeting under 50KB for avatars\r\n        const compressionResult = await compressImageModerateClient(\r\n          croppedFile,\r\n          {\r\n            maxDimension: 400,\r\n            targetSizeKB: 45, // Target 45KB to ensure we stay under 50KB\r\n          }\r\n        );\r\n\r\n        // Convert compressed blob back to file\r\n        const compressedFile = new File(\r\n          [compressionResult.blob],\r\n          originalFile.name,\r\n          {\r\n            type: compressionResult.blob.type,\r\n          }\r\n        );\r\n\r\n        const previewUrl = URL.createObjectURL(compressedFile);\r\n        setLocalPreviewUrl(previewUrl);\r\n        handleAvatarUpload(compressedFile);\r\n      } catch (error) {\r\n        console.error(\"Image compression failed:\", error);\r\n        toast.error(\"Failed to process image. Please try a different image.\");\r\n        setOriginalFile(null);\r\n        const fileInput = document.querySelector(\r\n          'input[type=\"file\"]'\r\n        ) as HTMLInputElement;\r\n        if (fileInput) fileInput.value = \"\";\r\n      }\r\n    } else {\r\n      // Handle crop cancellation or error\r\n      setOriginalFile(null);\r\n      const fileInput = document.querySelector(\r\n        'input[type=\"file\"]'\r\n      ) as HTMLInputElement;\r\n      if (fileInput) fileInput.value = \"\";\r\n    }\r\n  };\r\n\r\n  // Handle crop dialog close\r\n  const handleCropDialogClose = () => {\r\n    setImageToCrop(null);\r\n    setOriginalFile(null);\r\n    // Clear the file input\r\n    const fileInput = document.querySelector(\r\n      'input[type=\"file\"]'\r\n    ) as HTMLInputElement;\r\n    if (fileInput) fileInput.value = \"\";\r\n  };\r\n\r\n  // Handle avatar deletion\r\n  const handleAvatarDelete = async (avatarUrl: string) => {\r\n    startAvatarUploadTransition(async () => {\r\n      try {\r\n        setAvatarUploadStatus(\"uploading\"); // Reuse uploading status for deletion\r\n        setAvatarUploadError(null);\r\n\r\n        const result = await deleteCustomerAvatar(avatarUrl);\r\n\r\n        if (result.success) {\r\n          setAvatarUploadStatus(\"success\");\r\n          setLocalPreviewUrl(null);\r\n          onUpdateAvatar(\"\"); // Clear the avatar URL\r\n          toast.success(\"Avatar deleted successfully!\");\r\n        } else {\r\n          setAvatarUploadStatus(\"error\");\r\n          setAvatarUploadError(result.error || \"Failed to delete avatar\");\r\n          toast.error(result.error || \"Failed to delete avatar\");\r\n        }\r\n      } catch (error) {\r\n        setAvatarUploadStatus(\"error\");\r\n        const errorMessage =\r\n          error instanceof Error ? error.message : \"Failed to delete avatar\";\r\n        setAvatarUploadError(errorMessage);\r\n        toast.error(errorMessage);\r\n      }\r\n    });\r\n  };\r\n\r\n  // Avatar error display component\r\n  const avatarErrorDisplay =\r\n    avatarUploadStatus === \"error\" && avatarUploadError\r\n      ? avatarUploadError\r\n      : null;\r\n\r\n  return {\r\n    avatarUploadStatus,\r\n    avatarUploadError,\r\n    localPreviewUrl,\r\n    isAvatarUploading,\r\n    imageToCrop,\r\n    onFileSelect,\r\n    handleAvatarUpload,\r\n    handleCropComplete,\r\n    handleCropDialogClose,\r\n    handleAvatarDelete,\r\n    avatarErrorDisplay,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAAA;AAAA;AAKA;;AATA;;;;;AAkBO,SAAS,gBAAgB,EAAE,cAAc,EAA0B;;IACxE,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,mBAAmB,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,IAAI,iBAAiB;YACnB,IAAI,eAAe,CAAC;YACpB,mBAAmB;QACrB;QAEA,IAAI,MAAM;YACR,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;gBAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,sBAAsB;gBACtB,qBAAqB;gBACrB,mBAAmB;gBACnB;YACF;YAEA,uBAAuB;YACvB,gBAAgB;YAChB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,eAAe,OAAO,MAAM;YAC9B;YACA,OAAO,aAAa,CAAC;QACvB,OAAO;YACL,sBAAsB;YACtB,qBAAqB;YACrB,mBAAmB;QACrB;IACF;IAEA,wBAAwB;IACxB,MAAM,qBAAqB,OAAO;QAChC,sBAAsB;QACtB,qBAAqB;QAErB,4BAA4B;YAC1B,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,cAAc;YAE9B,MAAM,SAAS,MAAM,CAAA,GAAA,mMAAA,CAAA,wBAAqB,AAAD,EAAE;YAE3C,IAAI,OAAO,OAAO,IAAI,OAAO,GAAG,EAAE;gBAChC,MAAM,eAAe,OAAO,GAAG;gBAE/B,iBAAiB;gBACjB,sBAAsB;gBAEtB,uBAAuB;gBACvB,mBAAmB;gBACnB,IAAI,iBAAiB,IAAI,eAAe,CAAC;gBAEzC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,6BAA6B;gBAC7B,IAAI;oBACF,MAAM,eAAe,MAAM,CAAA,GAAA,mMAAA,CAAA,kBAAe,AAAD,EAAE;oBAC3C,IAAI,CAAC,aAAa,OAAO,EAAE;wBACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,yCAAyC,EAAE,aAAa,KAAK,EAAE;oBAEpE;oBAEA,yDAAyD;oBACzD,IAAI,aAAa,OAAO,EAAE;wBACxB,eAAe;oBACjB;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF,OAAO;gBACL,sBAAsB;gBACtB,MAAM,eAAe,OAAO,KAAK,IAAI;gBACrC,qBAAqB;gBACrB,mBAAmB;gBACnB,IAAI,iBAAiB,IAAI,eAAe,CAAC;gBAEzC,mCAAmC;gBACnC,IAAI,aAAa,QAAQ,CAAC,qCAAqC;oBAC7D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,mBAAmB;wBAC7B,aAAa;oBACf;gBACF,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB;oBACrD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qBAAqB;wBAC/B,aAAa;oBACf;gBACF,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB;wBAC3B,aAAa;oBACf;gBACF;YACF;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,qBAAqB,OAAO;QAChC,eAAe,OAAO,eAAe;QAErC,IAAI,eAAe,cAAc;YAC/B,IAAI;gBACF,uCAAuC;gBACvC,MAAM,cAAc,IAAI,KAAK;oBAAC;iBAAY,EAAE,aAAa,IAAI,EAAE;oBAC7D,MAAM;gBACR;gBAEA,yEAAyE;gBACzE,MAAM,oBAAoB,MAAM,CAAA,GAAA,iJAAA,CAAA,8BAA2B,AAAD,EACxD,aACA;oBACE,cAAc;oBACd,cAAc;gBAChB;gBAGF,uCAAuC;gBACvC,MAAM,iBAAiB,IAAI,KACzB;oBAAC,kBAAkB,IAAI;iBAAC,EACxB,aAAa,IAAI,EACjB;oBACE,MAAM,kBAAkB,IAAI,CAAC,IAAI;gBACnC;gBAGF,MAAM,aAAa,IAAI,eAAe,CAAC;gBACvC,mBAAmB;gBACnB,mBAAmB;YACrB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB,MAAM,YAAY,SAAS,aAAa,CACtC;gBAEF,IAAI,WAAW,UAAU,KAAK,GAAG;YACnC;QACF,OAAO;YACL,oCAAoC;YACpC,gBAAgB;YAChB,MAAM,YAAY,SAAS,aAAa,CACtC;YAEF,IAAI,WAAW,UAAU,KAAK,GAAG;QACnC;IACF;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB;QAC5B,eAAe;QACf,gBAAgB;QAChB,uBAAuB;QACvB,MAAM,YAAY,SAAS,aAAa,CACtC;QAEF,IAAI,WAAW,UAAU,KAAK,GAAG;IACnC;IAEA,yBAAyB;IACzB,MAAM,qBAAqB,OAAO;QAChC,4BAA4B;YAC1B,IAAI;gBACF,sBAAsB,cAAc,sCAAsC;gBAC1E,qBAAqB;gBAErB,MAAM,SAAS,MAAM,CAAA,GAAA,mMAAA,CAAA,uBAAoB,AAAD,EAAE;gBAE1C,IAAI,OAAO,OAAO,EAAE;oBAClB,sBAAsB;oBACtB,mBAAmB;oBACnB,eAAe,KAAK,uBAAuB;oBAC3C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,OAAO;oBACL,sBAAsB;oBACtB,qBAAqB,OAAO,KAAK,IAAI;oBACrC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,sBAAsB;gBACtB,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC3C,qBAAqB;gBACrB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,iCAAiC;IACjC,MAAM,qBACJ,uBAAuB,WAAW,oBAC9B,oBACA;IAEN,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAxNgB;;QAO2C,6JAAA,CAAA,gBAAa", "debugId": null}}, {"offset": {"line": 2070, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Slider({\r\n  className,\r\n  defaultValue,\r\n  value,\r\n  min = 0,\r\n  max = 100,\r\n  ...props\r\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\r\n  const _values = React.useMemo(\r\n    () =>\r\n      Array.isArray(value)\r\n        ? value\r\n        : Array.isArray(defaultValue)\r\n          ? defaultValue\r\n          : [min, max],\r\n    [value, defaultValue, min, max]\r\n  )\r\n\r\n  return (\r\n    <SliderPrimitive.Root\r\n      data-slot=\"slider\"\r\n      defaultValue={defaultValue}\r\n      value={value}\r\n      min={min}\r\n      max={max}\r\n      className={cn(\r\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SliderPrimitive.Track\r\n        data-slot=\"slider-track\"\r\n        className={cn(\r\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\r\n        )}\r\n      >\r\n        <SliderPrimitive.Range\r\n          data-slot=\"slider-range\"\r\n          className={cn(\r\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\r\n          )}\r\n        />\r\n      </SliderPrimitive.Track>\r\n      {Array.from({ length: _values.length }, (_, index) => (\r\n        <SliderPrimitive.Thumb\r\n          data-slot=\"slider-thumb\"\r\n          key={index}\r\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\r\n        />\r\n      ))}\r\n    </SliderPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Slider }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;;IAClD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;mCAC1B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;gBAAC;gBAAK;aAAI;kCAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,6LAAC,qKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf;GArDS;KAAA", "debugId": null}}, {"offset": {"line": 2152, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/ImageCropDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback, useEffect } from \"react\"; // Added React and useEffect import\r\nimport <PERSON><PERSON><PERSON>, { Point, Area } from \"react-easy-crop\"; // Import types directly\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>alog<PERSON>ontent,\r\n  <PERSON><PERSON>Header,\r\n  <PERSON><PERSON><PERSON><PERSON>le,\r\n  DialogFooter,\r\n} from \"@/components/ui/dialog\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { Slider } from \"@/components/ui/slider\"; // Import Slider\r\n\r\n// Helper function to create an image element\r\nconst createImage = (url: string): Promise<HTMLImageElement> =>\r\n  new Promise((resolve, reject) => {\r\n    const image = new Image();\r\n    image.addEventListener(\"load\", () => resolve(image));\r\n    image.addEventListener(\"error\", (error) => reject(error));\r\n    image.setAttribute(\"crossOrigin\", \"anonymous\"); // needed to avoid cross-origin issues\r\n    image.src = url;\r\n  });\r\n\r\n// Helper function to get the cropped image blob\r\nasync function getCroppedImgBlob(\r\n  imageSrc: string,\r\n  pixelCrop: Area\r\n): Promise<Blob | null> {\r\n  const image = await createImage(imageSrc);\r\n  const canvas = document.createElement(\"canvas\");\r\n  const ctx = canvas.getContext(\"2d\");\r\n\r\n  if (!ctx) {\r\n    return null;\r\n  }\r\n\r\n  const scaleX = image.naturalWidth / image.width;\r\n  const scaleY = image.naturalHeight / image.height;\r\n  const pixelRatio = window.devicePixelRatio || 1;\r\n\r\n  canvas.width = pixelCrop.width * pixelRatio * scaleX;\r\n  canvas.height = pixelCrop.height * pixelRatio * scaleY;\r\n\r\n  ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\r\n  ctx.imageSmoothingQuality = \"high\";\r\n\r\n  ctx.drawImage(\r\n    image,\r\n    pixelCrop.x * scaleX,\r\n    pixelCrop.y * scaleY,\r\n    pixelCrop.width * scaleX,\r\n    pixelCrop.height * scaleY,\r\n    0,\r\n    0,\r\n    pixelCrop.width * scaleX,\r\n    pixelCrop.height * scaleY\r\n  );\r\n\r\n  return new Promise((resolve) => {\r\n    canvas.toBlob(\r\n      resolve, // Pass resolve directly as the callback\r\n       \"image/png\" // Output as PNG from canvas\r\n       // Quality parameter is not applicable for PNG\r\n     );\r\n  });\r\n}\r\n\r\ninterface ImageCropDialogProps {\r\n  imgSrc: string | null;\r\n  onCropComplete: (_blob: Blob | null) => void; // Disabled warning for unused type param\r\n  onClose: () => void;\r\n  isOpen: boolean;\r\n}\r\n\r\nexport default function ImageCropDialog({\r\n  imgSrc,\r\n  onCropComplete,\r\n  onClose,\r\n  isOpen,\r\n}: ImageCropDialogProps) {\r\n  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });\r\n  const [zoom, setZoom] = useState(1);\r\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);\r\n  const [isCropping, setIsCropping] = useState(false);\r\n\r\n  const onCropCompleteCallback = useCallback((_croppedArea: Area, croppedAreaPixels: Area) => {\r\n    setCroppedAreaPixels(croppedAreaPixels);\r\n  }, []);\r\n\r\n  const handleCrop = async () => {\r\n    if (!imgSrc || !croppedAreaPixels) {\r\n      console.warn(\"Image source or crop area not available.\");\r\n      onCropComplete(null);\r\n      return;\r\n    }\r\n    setIsCropping(true);\r\n    try {\r\n      const croppedBlob = await getCroppedImgBlob(imgSrc, croppedAreaPixels);\r\n      onCropComplete(croppedBlob);\r\n    } catch (e) {\r\n      console.error(\"Error cropping image:\", e);\r\n      onCropComplete(null); // Indicate error\r\n    } finally {\r\n      setIsCropping(false);\r\n    }\r\n  };\r\n\r\n  // Reset zoom when dialog opens using useEffect\r\n  useEffect(() => { // Use useEffect directly after importing React\r\n    if (isOpen) {\r\n      setZoom(1);\r\n    }\r\n  }, [isOpen]); // Add isOpen as a dependency\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"sm:max-w-[600px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Crop Your Logo</DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800\">\r\n          {imgSrc ? (\r\n            <Cropper\r\n              image={imgSrc}\r\n              crop={crop}\r\n              zoom={zoom}\r\n              aspect={1} // 1:1 aspect ratio\r\n              cropShape=\"round\" // Make the crop area round\r\n              showGrid={false}\r\n              onCropChange={setCrop}\r\n              onZoomChange={setZoom}\r\n              onCropComplete={onCropCompleteCallback}\r\n            />\r\n          ) : (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <p>Loading image...</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n         {/* Zoom Slider */}\r\n         <div className=\"px-4 pb-4\">\r\n           <Slider\r\n             min={1}\r\n              max={3}\r\n              step={0.1}\r\n              value={[zoom]}\r\n              onValueChange={(value: number[]) => setZoom(value[0])} // Added type for value\r\n              className=\"w-full\"\r\n              aria-label=\"Zoom slider\"\r\n           />\r\n         </div>\r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={onClose} disabled={isCropping}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleCrop}\r\n            disabled={isCropping}\r\n            className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]\"\r\n          >\r\n            {isCropping ? (\r\n              <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n            ) : null}\r\n            Crop Image\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  ); // Ensure the function closing brace and semicolon are correct\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,mRAAiE,mCAAmC;AACpG,+QAAwD,wBAAwB;AAChF;AACA;AAOA;AACA,8NAAiD,gBAAgB;;;AAbjE;;;;;;;AAeA,6CAA6C;AAC7C,MAAM,cAAc,CAAC,MACnB,IAAI,QAAQ,CAAC,SAAS;QACpB,MAAM,QAAQ,IAAI;QAClB,MAAM,gBAAgB,CAAC,QAAQ,IAAM,QAAQ;QAC7C,MAAM,gBAAgB,CAAC,SAAS,CAAC,QAAU,OAAO;QAClD,MAAM,YAAY,CAAC,eAAe,cAAc,sCAAsC;QACtF,MAAM,GAAG,GAAG;IACd;AAEF,gDAAgD;AAChD,eAAe,kBACb,QAAgB,EAChB,SAAe;IAEf,MAAM,QAAQ,MAAM,YAAY;IAChC,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,MAAM,MAAM,OAAO,UAAU,CAAC;IAE9B,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IAEA,MAAM,SAAS,MAAM,YAAY,GAAG,MAAM,KAAK;IAC/C,MAAM,SAAS,MAAM,aAAa,GAAG,MAAM,MAAM;IACjD,MAAM,aAAa,OAAO,gBAAgB,IAAI;IAE9C,OAAO,KAAK,GAAG,UAAU,KAAK,GAAG,aAAa;IAC9C,OAAO,MAAM,GAAG,UAAU,MAAM,GAAG,aAAa;IAEhD,IAAI,YAAY,CAAC,YAAY,GAAG,GAAG,YAAY,GAAG;IAClD,IAAI,qBAAqB,GAAG;IAE5B,IAAI,SAAS,CACX,OACA,UAAU,CAAC,GAAG,QACd,UAAU,CAAC,GAAG,QACd,UAAU,KAAK,GAAG,QAClB,UAAU,MAAM,GAAG,QACnB,GACA,GACA,UAAU,KAAK,GAAG,QAClB,UAAU,MAAM,GAAG;IAGrB,OAAO,IAAI,QAAQ,CAAC;QAClB,OAAO,MAAM,CACX,SACC,YAAY,4BAA4B;;IAG7C;AACF;AASe,SAAS,gBAAgB,EACtC,MAAM,EACN,cAAc,EACd,OAAO,EACP,MAAM,EACe;;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;QAAE,GAAG;QAAG,GAAG;IAAE;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC,cAAoB;YAC9D,qBAAqB;QACvB;8DAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,CAAC,mBAAmB;YACjC,QAAQ,IAAI,CAAC;YACb,eAAe;YACf;QACF;QACA,cAAc;QACd,IAAI;YACF,MAAM,cAAc,MAAM,kBAAkB,QAAQ;YACpD,eAAe;QACjB,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,yBAAyB;YACvC,eAAe,OAAO,iBAAiB;QACzC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,QAAQ;gBACV,QAAQ;YACV;QACF;oCAAG;QAAC;KAAO,GAAG,6BAA6B;IAE3C,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,8HAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,6LAAC;oBAAI,WAAU;8BACZ,uBACC,6LAAC,2JAAA,CAAA,UAAO;wBACN,OAAO;wBACP,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,WAAU,QAAQ,2BAA2B;;wBAC7C,UAAU;wBACV,cAAc;wBACd,cAAc;wBACd,gBAAgB;;;;;6CAGlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAE;;;;;;;;;;;;;;;;8BAKR,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,KAAK;wBACJ,KAAK;wBACL,MAAM;wBACN,OAAO;4BAAC;yBAAK;wBACb,eAAe,CAAC,QAAoB,QAAQ,KAAK,CAAC,EAAE;wBACpD,WAAU;wBACV,cAAW;;;;;;;;;;;8BAGjB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAY;;;;;;sCAGlE,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;;gCAET,2BACC,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;2CACjB;gCAAK;;;;;;;;;;;;;;;;;;;;;;;cAMhB,8DAA8D;AACnE;GA/FwB;KAAA", "debugId": null}}, {"offset": {"line": 2380, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,8KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,8KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 2566, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/AvatarDeleteDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\n\r\ninterface AvatarDeleteDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  isDeleting?: boolean;\r\n}\r\n\r\nexport default function AvatarDeleteDialog({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  isDeleting = false,\r\n}: AvatarDeleteDialogProps) {\r\n  return (\r\n    <AlertDialog open={isOpen} onOpenChange={onClose}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Remove Profile Picture</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            Are you sure you want to remove your profile picture? This action cannot be undone.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction\r\n            onClick={onConfirm}\r\n            disabled={isDeleting}\r\n            className=\"bg-destructive text-destructive-foreground hover:bg-destructive/90\"\r\n          >\r\n            {isDeleting ? 'Removing...' : 'Remove'}\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAqBe,SAAS,mBAAmB,EACzC,MAAM,EACN,OAAO,EACP,SAAS,EACT,aAAa,KAAK,EACM;IACxB,qBACE,6LAAC,uIAAA,CAAA,cAAW;QAAC,MAAM;QAAQ,cAAc;kBACvC,cAAA,6LAAC,uIAAA,CAAA,qBAAkB;;8BACjB,6LAAC,uIAAA,CAAA,oBAAiB;;sCAChB,6LAAC,uIAAA,CAAA,mBAAgB;sCAAC;;;;;;sCAClB,6LAAC,uIAAA,CAAA,yBAAsB;sCAAC;;;;;;;;;;;;8BAI1B,6LAAC,uIAAA,CAAA,oBAAiB;;sCAChB,6LAAC,uIAAA,CAAA,oBAAiB;4BAAC,UAAU;sCAAY;;;;;;sCACzC,6LAAC,uIAAA,CAAA,oBAAiB;4BAChB,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAM1C;KA5BwB", "debugId": null}}, {"offset": {"line": 2652, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/AvatarUpload.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Camera, Loader2, X } from \"lucide-react\";\r\nimport { useAvatarUpload } from \"../hooks/useAvatarUpload\";\r\nimport ImageCropDialog from \"@/app/(dashboard)/dashboard/business/card/components/ImageCropDialog\";\r\nimport AvatarDeleteDialog from \"./AvatarDeleteDialog\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface AvatarUploadProps {\r\n  initialAvatarUrl?: string;\r\n  userName?: string | null;\r\n  onUpdateAvatar: (_url: string) => void;\r\n}\r\n\r\nexport default function AvatarUpload({\r\n  initialAvatarUrl,\r\n  userName,\r\n  onUpdateAvatar,\r\n}: AvatarUploadProps) {\r\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);\r\n\r\n  const {\r\n    localPreviewUrl,\r\n    isAvatarUploading,\r\n    imageToCrop,\r\n    onFileSelect,\r\n    handleCropComplete,\r\n    handleCropDialogClose,\r\n    handleAvatarDelete,\r\n    avatarErrorDisplay,\r\n  } = useAvatarUpload({\r\n    initialAvatarUrl,\r\n    onUpdateAvatar,\r\n  });\r\n\r\n  // Generate initials from name\r\n  const getInitials = (name?: string | null) => {\r\n    if (!name) return \"U\";\r\n\r\n    const parts = name.split(/\\s+/);\r\n    if (parts.length === 1) {\r\n      return name.substring(0, 2).toUpperCase();\r\n    }\r\n\r\n    return (\r\n      parts[0].charAt(0) + parts[parts.length - 1].charAt(0)\r\n    ).toUpperCase();\r\n  };\r\n\r\n  const initials = getInitials(userName);\r\n\r\n  // Handle delete with confirmation\r\n  const handleDeleteClick = () => {\r\n    setIsDeleteDialogOpen(true);\r\n  };\r\n\r\n  const handleConfirmDelete = () => {\r\n    const currentAvatarUrl = localPreviewUrl || initialAvatarUrl;\r\n    if (currentAvatarUrl) {\r\n      handleAvatarDelete(currentAvatarUrl);\r\n    }\r\n    setIsDeleteDialogOpen(false);\r\n  };\r\n\r\n  const handleCancelDelete = () => {\r\n    setIsDeleteDialogOpen(false);\r\n  };\r\n\r\n  // Check if avatar exists\r\n  const hasAvatar = !!(localPreviewUrl || initialAvatarUrl);\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-4\">\r\n      <motion.div\r\n        className=\"relative\"\r\n        whileHover={{ scale: 1.05 }}\r\n        transition={{ type: \"spring\", stiffness: 300, damping: 15 }}\r\n      >\r\n        <Avatar\r\n          className={cn(\r\n            \"h-32 w-32\",\r\n            \"border-4 border-primary/20\",\r\n            \"shadow-2xl\",\r\n            \"ring-4 ring-primary/10\",\r\n            \"transition-all duration-300\",\r\n            \"hover:shadow-3xl hover:ring-primary/20\"\r\n          )}\r\n        >\r\n          {localPreviewUrl || initialAvatarUrl ? (\r\n            <AvatarImage\r\n              src={localPreviewUrl || initialAvatarUrl}\r\n              alt={userName || \"User\"}\r\n            />\r\n          ) : null}\r\n          <AvatarFallback\r\n            className={cn(\r\n              \"bg-gradient-to-br from-primary/10 to-primary/20 dark:from-primary/20 dark:to-primary/30\",\r\n              \"text-primary dark:text-primary text-2xl font-semibold\",\r\n              \"border border-primary/20\"\r\n            )}\r\n          >\r\n            {initials}\r\n          </AvatarFallback>\r\n        </Avatar>\r\n\r\n        <motion.label\r\n          htmlFor=\"avatar-upload\"\r\n          className={cn(\r\n            \"absolute bottom-0 right-0 p-2 rounded-full\",\r\n            \"bg-primary text-primary-foreground cursor-pointer\",\r\n            \"hover:bg-primary/90 transition-colors\",\r\n            \"shadow-lg hover:shadow-xl\",\r\n            \"border-2 border-background\"\r\n          )}\r\n          whileHover={{ scale: 1.1 }}\r\n          whileTap={{ scale: 0.95 }}\r\n        >\r\n          <Camera className=\"h-5 w-5\" />\r\n          <span className=\"sr-only\">Upload avatar</span>\r\n        </motion.label>\r\n\r\n        {/* Delete button - only show when avatar exists */}\r\n        {hasAvatar && (\r\n          <motion.button\r\n            onClick={handleDeleteClick}\r\n            className={cn(\r\n              \"absolute top-0 right-0 p-2 rounded-full\",\r\n              \"bg-destructive text-destructive-foreground cursor-pointer\",\r\n              \"hover:bg-destructive/90 transition-colors\",\r\n              \"shadow-lg hover:shadow-xl\",\r\n              \"border-2 border-background\"\r\n            )}\r\n            whileHover={{ scale: 1.1 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            disabled={isAvatarUploading}\r\n          >\r\n            <X className=\"h-4 w-4\" />\r\n            <span className=\"sr-only\">Remove avatar</span>\r\n          </motion.button>\r\n        )}\r\n\r\n        <Input\r\n          id=\"avatar-upload\"\r\n          type=\"file\"\r\n          accept=\"image/png, image/jpeg, image/gif, image/webp\"\r\n          className=\"hidden\"\r\n          onChange={(e) => onFileSelect(e.target.files?.[0] || null)}\r\n          disabled={isAvatarUploading}\r\n        />\r\n      </motion.div>\r\n\r\n      {isAvatarUploading && (\r\n        <div className=\"flex items-center text-sm text-neutral-500 dark:text-neutral-400\">\r\n          <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n          Uploading...\r\n        </div>\r\n      )}\r\n\r\n      {avatarErrorDisplay && (\r\n        <div className=\"text-sm text-red-500\">{avatarErrorDisplay}</div>\r\n      )}\r\n\r\n      {/* Image Crop Dialog */}\r\n      <ImageCropDialog\r\n        isOpen={!!imageToCrop}\r\n        imgSrc={imageToCrop}\r\n        onCropComplete={handleCropComplete}\r\n        onClose={handleCropDialogClose}\r\n      />\r\n\r\n      {/* Avatar Delete Dialog */}\r\n      <AvatarDeleteDialog\r\n        isOpen={isDeleteDialogOpen}\r\n        onClose={handleCancelDelete}\r\n        onConfirm={handleConfirmDelete}\r\n        isDeleting={isAvatarUploading}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAkBe,SAAS,aAAa,EACnC,gBAAgB,EAChB,QAAQ,EACR,cAAc,EACI;;IAClB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,EACJ,eAAe,EACf,iBAAiB,EACjB,WAAW,EACX,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,uLAAA,CAAA,kBAAe,AAAD,EAAE;QAClB;QACA;IACF;IAEA,8BAA8B;IAC9B,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,KAAK,SAAS,CAAC,GAAG,GAAG,WAAW;QACzC;QAEA,OAAO,CACL,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,EACtD,EAAE,WAAW;IACf;IAEA,MAAM,WAAW,YAAY;IAE7B,kCAAkC;IAClC,MAAM,oBAAoB;QACxB,sBAAsB;IACxB;IAEA,MAAM,sBAAsB;QAC1B,MAAM,mBAAmB,mBAAmB;QAC5C,IAAI,kBAAkB;YACpB,mBAAmB;QACrB;QACA,sBAAsB;IACxB;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,yBAAyB;IACzB,MAAM,YAAY,CAAC,CAAC,CAAC,mBAAmB,gBAAgB;IAExD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;;kCAE1D,6LAAC,8HAAA,CAAA,SAAM;wBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,aACA,8BACA,cACA,0BACA,+BACA;;4BAGD,mBAAmB,iCAClB,6LAAC,8HAAA,CAAA,cAAW;gCACV,KAAK,mBAAmB;gCACxB,KAAK,YAAY;;;;;uCAEjB;0CACJ,6LAAC,8HAAA,CAAA,iBAAc;gCACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2FACA,yDACA;0CAGD;;;;;;;;;;;;kCAIL,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wBACX,SAAQ;wBACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8CACA,qDACA,yCACA,6BACA;wBAEF,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAK;;0CAExB,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;oBAI3B,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2CACA,6DACA,6CACA,6BACA;wBAEF,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAK;wBACxB,UAAU;;0CAEV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAI9B,6LAAC,6HAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,QAAO;wBACP,WAAU;wBACV,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;wBACrD,UAAU;;;;;;;;;;;;YAIb,mCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA8B;;;;;;;YAKpD,oCACC,6LAAC;gBAAI,WAAU;0BAAwB;;;;;;0BAIzC,6LAAC,0LAAA,CAAA,UAAe;gBACd,QAAQ,CAAC,CAAC;gBACV,QAAQ;gBACR,gBAAgB;gBAChB,SAAS;;;;;;0BAIX,6LAAC,gMAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS;gBACT,WAAW;gBACX,YAAY;;;;;;;;;;;;AAIpB;GArKwB;;QAgBlB,uLAAA,CAAA,kBAAe;;;KAhBG", "debugId": null}}, {"offset": {"line": 2901, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/ProfileRequirementDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useSearchParams, useRouter } from \"next/navigation\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { AlertCircle, Mail, Phone, MapPin, CheckCircle } from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface ProfileRequirementDialogProps {\r\n  hasCompleteAddress?: boolean;\r\n}\r\n\r\nexport default function ProfileRequirementDialog({\r\n  hasCompleteAddress = false,\r\n}: ProfileRequirementDialogProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [missingFields, setMissingFields] = useState<string[]>([]);\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    // Check URL parameters for missing fields\r\n    const missingParam = searchParams.get(\"missing\");\r\n    const messageParam = searchParams.get(\"message\");\r\n\r\n    if (missingParam || messageParam) {\r\n      const fields = missingParam ? missingParam.split(\",\") : [];\r\n\r\n      // Also check current state to determine what's actually missing\r\n      const actuallyMissing: string[] = [];\r\n      if (!hasCompleteAddress) actuallyMissing.push(\"address\");\r\n\r\n      // Use the more comprehensive list\r\n      const finalMissing = actuallyMissing.length > 0 ? actuallyMissing : fields;\r\n\r\n      setMissingFields(finalMissing);\r\n      setIsOpen(true);\r\n\r\n      // Clean up URL parameters\r\n      const newUrl = window.location.pathname;\r\n      router.replace(newUrl, { scroll: false });\r\n    }\r\n  }, [searchParams, hasCompleteAddress, router]);\r\n\r\n  const getFieldInfo = (field: string) => {\r\n    switch (field) {\r\n      case \"email\":\r\n        return {\r\n          icon: <Mail className=\"w-5 h-5\" />,\r\n          label: \"Email Address\",\r\n          description: \"Required for account notifications and password reset\",\r\n          color: \"text-[#C29D5B]\",\r\n          bgColor: \"bg-[#C29D5B]/10\",\r\n          borderColor: \"border-[#C29D5B]/20\",\r\n        };\r\n      case \"phone\":\r\n        return {\r\n          icon: <Phone className=\"w-5 h-5\" />,\r\n          label: \"Mobile Number\",\r\n          description: \"Required for account access and verification\",\r\n          color: \"text-[#C29D5B]\",\r\n          bgColor: \"bg-[#C29D5B]/10\",\r\n          borderColor: \"border-[#C29D5B]/20\",\r\n        };\r\n      case \"address\":\r\n        return {\r\n          icon: <MapPin className=\"w-5 h-5\" />,\r\n          label: \"Address Information\",\r\n          description: \"Required for location-based services\",\r\n          color: \"text-[#C29D5B]\",\r\n          bgColor: \"bg-[#C29D5B]/10\",\r\n          borderColor: \"border-[#C29D5B]/20\",\r\n        };\r\n      default:\r\n        return {\r\n          icon: <AlertCircle className=\"w-5 h-5\" />,\r\n          label: field,\r\n          description: \"Required information\",\r\n          color: \"text-[#C29D5B]\",\r\n          bgColor: \"bg-[#C29D5B]/10\",\r\n          borderColor: \"border-[#C29D5B]/20\",\r\n        };\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setIsOpen(false);\r\n  };\r\n\r\n  if (missingFields.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\r\n      <DialogContent className=\"sm:max-w-lg max-w-[calc(100vw-2rem)] mx-auto p-0 gap-0 overflow-hidden border-0 shadow-2xl\">\r\n        {/* Header Section */}\r\n        <div className=\"relative bg-gradient-to-br from-[#C29D5B] to-[#B08A4A] px-6 py-8 text-white\">\r\n          <div className=\"absolute inset-0 bg-black/5\"></div>\r\n          <div className=\"relative\">\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"p-3 rounded-xl bg-white/20 backdrop-blur-sm\">\r\n                  <AlertCircle className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <DialogTitle className=\"text-xl font-bold text-white mb-1\">\r\n                    Complete Your Profile\r\n                  </DialogTitle>\r\n                  <DialogDescription className=\"text-white/80 text-sm\">\r\n                    Just a few more details to get started\r\n                  </DialogDescription>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content Section */}\r\n        <div className=\"px-6 py-6\">\r\n          <div className=\"mb-6\">\r\n            <p className=\"text-sm text-muted-foreground leading-relaxed\">\r\n              Please add the following required information to unlock all dashboard features and ensure the best experience.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"space-y-4 mb-8\">\r\n            {missingFields.map((field, index) => {\r\n              const fieldInfo = getFieldInfo(field);\r\n              return (\r\n                <motion.div\r\n                  key={field}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.4, delay: index * 0.1 }}\r\n                  className={`group relative flex items-start gap-4 p-4 rounded-xl border-2 ${fieldInfo.borderColor} ${fieldInfo.bgColor} hover:shadow-md transition-all duration-200`}\r\n                >\r\n                  <div className={`flex-shrink-0 p-3 rounded-lg ${fieldInfo.color} bg-white shadow-sm`}>\r\n                    {fieldInfo.icon}\r\n                  </div>\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <h4 className=\"text-base font-semibold text-foreground mb-1\">\r\n                      {fieldInfo.label}\r\n                    </h4>\r\n                    <p className=\"text-sm text-muted-foreground leading-relaxed\">\r\n                      {fieldInfo.description}\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"flex-shrink-0 w-6 h-6 rounded-full border-2 border-[#C29D5B]/30 bg-white group-hover:border-[#C29D5B] transition-colors duration-200\"></div>\r\n                </motion.div>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* Action Section */}\r\n          <div className=\"space-y-4\">\r\n            <Button\r\n              onClick={handleClose}\r\n              className=\"w-full h-12 bg-gradient-to-r from-[#C29D5B] to-[#B08A4A] hover:from-[#B08A4A] hover:to-[#9A7A3A] text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 border-0\"\r\n            >\r\n              <CheckCircle className=\"w-5 h-5 mr-2\" />\r\n              Got it, let me complete my profile\r\n            </Button>\r\n            <p className=\"text-xs text-center text-muted-foreground px-4 leading-relaxed\">\r\n              You can update these details using the forms below. All information is securely stored and protected.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAZA;;;;;;;AAkBe,SAAS,yBAAyB,EAC/C,qBAAqB,KAAK,EACI;;IAC9B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,0CAA0C;YAC1C,MAAM,eAAe,aAAa,GAAG,CAAC;YACtC,MAAM,eAAe,aAAa,GAAG,CAAC;YAEtC,IAAI,gBAAgB,cAAc;gBAChC,MAAM,SAAS,eAAe,aAAa,KAAK,CAAC,OAAO,EAAE;gBAE1D,gEAAgE;gBAChE,MAAM,kBAA4B,EAAE;gBACpC,IAAI,CAAC,oBAAoB,gBAAgB,IAAI,CAAC;gBAE9C,kCAAkC;gBAClC,MAAM,eAAe,gBAAgB,MAAM,GAAG,IAAI,kBAAkB;gBAEpE,iBAAiB;gBACjB,UAAU;gBAEV,0BAA0B;gBAC1B,MAAM,SAAS,OAAO,QAAQ,CAAC,QAAQ;gBACvC,OAAO,OAAO,CAAC,QAAQ;oBAAE,QAAQ;gBAAM;YACzC;QACF;6CAAG;QAAC;QAAc;QAAoB;KAAO;IAE7C,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,SAAS;oBACT,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,SAAS;oBACT,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,6MAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACxB,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,SAAS;oBACT,aAAa;gBACf;YACF;gBACE,OAAO;oBACL,oBAAM,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAC7B,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,SAAS;oBACT,aAAa;gBACf;QACJ;IACF;IAEA,MAAM,cAAc;QAClB,UAAU;IACZ;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BAEvB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,6LAAC;;8DACC,6LAAC,8HAAA,CAAA,cAAW;oDAAC,WAAU;8DAAoC;;;;;;8DAG3D,6LAAC,8HAAA,CAAA,oBAAiB;oDAAC,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU/D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,OAAO;gCACzB,MAAM,YAAY,aAAa;gCAC/B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAW,CAAC,8DAA8D,EAAE,UAAU,WAAW,CAAC,CAAC,EAAE,UAAU,OAAO,CAAC,4CAA4C,CAAC;;sDAEpK,6LAAC;4CAAI,WAAW,CAAC,6BAA6B,EAAE,UAAU,KAAK,CAAC,mBAAmB,CAAC;sDACjF,UAAU,IAAI;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,UAAU,KAAK;;;;;;8DAElB,6LAAC;oDAAE,WAAU;8DACV,UAAU,WAAW;;;;;;;;;;;;sDAG1B,6LAAC;4CAAI,WAAU;;;;;;;mCAjBV;;;;;4BAoBX;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAG1C,6LAAC;oCAAE,WAAU;8CAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1F;GA/JwB;;QAKD,qIAAA,CAAA,kBAAe;QACrB,qIAAA,CAAA,YAAS;;;KANF", "debugId": null}}, {"offset": {"line": 3269, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 3384, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/actions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { Database } from \"@/types/supabase\";\r\n\r\nimport { z } from 'zod';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { IndianMobileSchema } from '@/lib/schemas/authSchemas';\r\n\r\n// Define the schema for profile updates\r\nconst ProfileSchema = z.object({\r\n  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),\r\n  // Add other fields here if needed in the future\r\n});\r\n\r\n// Define the schema for phone updates\r\nconst PhoneSchema = z.object({\r\n  phone: IndianMobileSchema,\r\n});\r\n\r\n// Define the schema for address updates with proper validation\r\nconst AddressSchema = z.object({\r\n  address: z\r\n    .string()\r\n    .max(100, { message: \"Address cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  pincode: z\r\n    .string()\r\n    .min(1, { message: \"Pincode is required\" })\r\n    .regex(/^\\d{6}$/, { message: \"Must be a valid 6-digit pincode\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"City cannot be empty\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"State cannot be empty\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality is required\" })\r\n    .refine((val) => val.trim().length > 0, { message: \"Locality cannot be empty\" }),\r\n});\r\n\r\n// Define the schema for email updates\r\nconst EmailSchema = z.object({\r\n  email: z.string().email({ message: \"Please enter a valid email address\" }),\r\n});\r\n\r\n// Define the schema for mobile updates\r\nconst MobileSchema = z.object({\r\n  mobile: IndianMobileSchema,\r\n});\r\n\r\n// Define the unified schema for profile and address updates\r\nconst UnifiedProfileSchema = z.object({\r\n  name: z.string().min(1, 'Name cannot be empty').max(100, 'Name is too long'),\r\n  address: z\r\n    .string()\r\n    .max(100, { message: \"Address cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  pincode: z\r\n    .string()\r\n    .min(1, { message: \"Pincode is required\" })\r\n    .regex(/^\\d{6}$/, { message: \"Must be a valid 6-digit pincode\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  city: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  state: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  locality: z\r\n    .string()\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n});\r\n\r\nexport type ProfileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    name?: string[];\r\n    // Add other fields here\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type PhoneFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    phone?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type AddressFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    address?: string[];\r\n    pincode?: string[];\r\n    city?: string[];\r\n    state?: string[];\r\n    locality?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type EmailFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    email?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type MobileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    mobile?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport type UnifiedProfileFormState = {\r\n  message: string | null;\r\n  errors?: {\r\n    name?: string[];\r\n    address?: string[];\r\n    pincode?: string[];\r\n    city?: string[];\r\n    state?: string[];\r\n    locality?: string[];\r\n  };\r\n  success: boolean;\r\n};\r\n\r\nexport async function updateCustomerProfile(\r\n  prevState: ProfileFormState,\r\n  formData: FormData\r\n): Promise<ProfileFormState> {\r\n  const supabase = await createClient(); // Added await\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = ProfileSchema.safeParse({\r\n    name: formData.get('name'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { name } = validatedFields.data;\r\n\r\n  try {\r\n    // Update name in auth.users table (full_name in user_metadata)\r\n    // The database trigger will automatically sync this to customer_profiles table\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      data: { full_name: name }\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth user metadata:', authUpdateError);\r\n      return { message: `Auth Error: ${authUpdateError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate the profile page and potentially the layout to reflect name change\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer/layout'); // To update sidebar/header name\r\n\r\n    return { message: 'Profile updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating profile:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerAddress(\r\n  prevState: AddressFormState,\r\n  formData: FormData\r\n): Promise<AddressFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = AddressSchema.safeParse({\r\n    address: formData.get('address'),\r\n    pincode: formData.get('pincode'),\r\n    city: formData.get('city'),\r\n    state: formData.get('state'),\r\n    locality: formData.get('locality'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { address, pincode, city, state, locality } = validatedFields.data;\r\n\r\n  try {\r\n    const { error: updateError } = await supabase\r\n      .from('customer_profiles')\r\n      .update({\r\n        address: address || null,\r\n        pincode,\r\n        city,\r\n        state,\r\n        locality\r\n      })\r\n      .eq('id', user.id);\r\n\r\n    if (updateError) {\r\n      console.error('Error updating customer address:', updateError);\r\n      return { message: `Database Error: ${updateError.message}`, success: false };\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return { message: 'Address updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating address:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerPhone(\r\n  prevState: PhoneFormState,\r\n  formData: FormData\r\n): Promise<PhoneFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = PhoneSchema.safeParse({\r\n    phone: formData.get('phone'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { phone } = validatedFields.data;\r\n\r\n  try {\r\n    // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n    // Update phone in customer_profiles table\r\n    const { error: updateError } = await supabase\r\n      .from('customer_profiles')\r\n      .update({ phone: phone })\r\n      .eq('id', user.id);\r\n\r\n    if (updateError) {\r\n      console.error('Error updating customer phone:', updateError);\r\n      return { message: `Database Error: ${updateError.message}`, success: false };\r\n    }\r\n\r\n    // Update phone in Supabase auth.users table to maintain user ID consistency\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${phone}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.warn('Failed to update auth phone field:', authUpdateError.message);\r\n      // Don't fail the operation for this, just log the warning\r\n      // The customer_profiles table is updated successfully\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return { message: 'Phone number updated successfully!', success: true };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating phone:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerEmail(\r\n  prevState: EmailFormState,\r\n  formData: FormData\r\n): Promise<EmailFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = EmailSchema.safeParse({\r\n    email: formData.get('email'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { email } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if user registered with Google OAuth (matching settings page logic)\r\n    const isGoogleLogin = user.app_metadata?.provider === 'google';\r\n\r\n    // Check if the user has email/password authentication\r\n    let hasEmailAuth = false;\r\n\r\n    if (isGoogleLogin && user.email) {\r\n      try {\r\n        // Use admin client to check user identities\r\n        const supabase = await createClient();\r\n        const { data: authData } = await supabase.auth.admin.getUserById(user.id);\r\n\r\n        // Check if the user has email/password authentication\r\n        if (authData?.user?.identities) {\r\n          hasEmailAuth = authData.user.identities.some(\r\n            (identity: { provider: string; }) => identity.provider === \"email\"\r\n          );\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking user auth methods:\", error);\r\n        hasEmailAuth = false;\r\n      }\r\n    }\r\n\r\n    // Only disable email changes if they're using Google and don't have email auth\r\n    const shouldDisableEmailChange = isGoogleLogin && !hasEmailAuth;\r\n\r\n    if (shouldDisableEmailChange) {\r\n      return {\r\n        message: 'Email cannot be changed for Google accounts. Your email is linked to your Google account.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Check if email is the same as current\r\n    if (user.email === email) {\r\n      return {\r\n        message: 'Email address is the same as current.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Update email in Supabase auth.users table\r\n    // This is the primary source of truth for email\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      email: email,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth email:', authUpdateError);\r\n\r\n      // Provide user-friendly error messages\r\n      let errorMessage = 'Failed to update email address.';\r\n      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {\r\n        errorMessage = 'This email address is already in use by another account.';\r\n      } else if (authUpdateError.message.includes('check constraint')) {\r\n        errorMessage = 'Invalid email format provided.';\r\n      } else if (authUpdateError.message.includes('rate limit')) {\r\n        errorMessage = 'Too many requests. Please try again later.';\r\n      }\r\n\r\n      return {\r\n        message: errorMessage,\r\n        success: false\r\n      };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: 'Email address updated successfully! You may need to verify the new email address.',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating email:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerMobile(\r\n  prevState: MobileFormState,\r\n  formData: FormData\r\n): Promise<MobileFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = MobileSchema.safeParse({\r\n    mobile: formData.get('mobile'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { mobile } = validatedFields.data;\r\n\r\n  try {\r\n    // Check if mobile is the same as current\r\n    const currentMobile = user.phone ? user.phone.replace(/^\\+91/, '') : '';\r\n    if (mobile === currentMobile) {\r\n      return {\r\n        message: 'Mobile number is the same as current.',\r\n        success: false,\r\n      };\r\n    }\r\n\r\n    // Note: Mobile uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n    // Update mobile in Supabase auth.users table\r\n    // This is the primary source of truth for mobile\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${mobile}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth mobile:', authUpdateError);\r\n\r\n      // Provide user-friendly error messages\r\n      let errorMessage = 'Failed to update mobile number.';\r\n      if (authUpdateError.message.includes('duplicate key value violates unique constraint')) {\r\n        errorMessage = 'This mobile number is already in use by another account.';\r\n      } else if (authUpdateError.message.includes('check constraint')) {\r\n        errorMessage = 'Invalid mobile number format provided.';\r\n      } else if (authUpdateError.message.includes('rate limit')) {\r\n        errorMessage = 'Too many requests. Please try again later.';\r\n      }\r\n\r\n      return {\r\n        message: errorMessage,\r\n        success: false\r\n      };\r\n    }\r\n\r\n    // Note: customer_profiles table will be automatically updated via database trigger\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: 'Mobile number updated successfully!',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating mobile:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n\r\nexport async function updateCustomerProfileAndAddress(\r\n  prevState: UnifiedProfileFormState,\r\n  formData: FormData\r\n): Promise<UnifiedProfileFormState> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return { message: 'Not authenticated', success: false };\r\n  }\r\n\r\n  const validatedFields = UnifiedProfileSchema.safeParse({\r\n    name: formData.get('name'),\r\n    address: formData.get('address'),\r\n    pincode: formData.get('pincode'),\r\n    city: formData.get('city'),\r\n    state: formData.get('state'),\r\n    locality: formData.get('locality'),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      message: 'Invalid data provided.',\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      success: false,\r\n    };\r\n  }\r\n\r\n  const { name, address, pincode, city, state, locality } = validatedFields.data;\r\n\r\n  // Validate that if any address field is provided, required fields are present\r\n  const hasAnyAddressField = pincode || city || state || locality;\r\n  if (hasAnyAddressField) {\r\n    if (!pincode || !city || !state || !locality) {\r\n      return {\r\n        message: 'If providing address information, pincode, city, state, and locality are required.',\r\n        success: false,\r\n      };\r\n    }\r\n  }\r\n\r\n  try {\r\n    // Update name in auth.users table (full_name in user_metadata)\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      data: { full_name: name }\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.error('Error updating auth user metadata:', authUpdateError);\r\n      return { message: `Auth Error: ${authUpdateError.message}`, success: false };\r\n    }\r\n\r\n    // Update address in customer_profiles table if address data is provided\r\n    if (hasAnyAddressField) {\r\n      const { error: updateError } = await supabase\r\n        .from('customer_profiles')\r\n        .update({\r\n          address: address || null,\r\n          pincode,\r\n          city,\r\n          state,\r\n          locality\r\n        })\r\n        .eq('id', user.id);\r\n\r\n      if (updateError) {\r\n        console.error('Error updating customer address:', updateError);\r\n        return { message: `Database Error: ${updateError.message}`, success: false };\r\n      }\r\n    }\r\n\r\n    // Revalidate relevant pages\r\n    revalidatePath('/dashboard/customer/profile');\r\n    revalidatePath('/dashboard/customer/layout');\r\n    revalidatePath('/dashboard/customer');\r\n\r\n    return {\r\n      message: hasAnyAddressField\r\n        ? 'Profile and address updated successfully!'\r\n        : 'Profile updated successfully!',\r\n      success: true\r\n    };\r\n  } catch (error) {\r\n    console.error('Unexpected error updating profile and address:', error);\r\n    return { message: 'An unexpected error occurred.', success: false };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAmgBsB,kCAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3400, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/profile/components/ProfilePageClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useTransition } from \"react\";\r\nimport { User, Save, Loader2, MapPin } from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { ProfileForm, type ProfileFormRef } from \"../ProfileForm\";\r\nimport AddressForm, { type AddressFormRef } from \"./AddressForm\";\r\nimport AvatarUpload from \"./AvatarUpload\";\r\nimport ProfileRequirementDialog from \"./ProfileRequirementDialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader } from \"@/components/ui/card\";\r\nimport { toast } from \"sonner\";\r\nimport { updateCustomerProfileAndAddress, type UnifiedProfileFormState } from \"../actions\";\r\n\r\ninterface ProfilePageClientProps {\r\n  initialName: string | null;\r\n  initialAvatarUrl?: string | null;\r\n  initialAddressData?: {\r\n    address?: string | null;\r\n    pincode?: string | null;\r\n    city?: string | null;\r\n    state?: string | null;\r\n    locality?: string | null;\r\n  } | null;\r\n  hasCompleteAddress?: boolean;\r\n}\r\n\r\nexport default function ProfilePageClient({\r\n  initialName,\r\n  initialAvatarUrl,\r\n  initialAddressData,\r\n  hasCompleteAddress = false,\r\n}: ProfilePageClientProps) {\r\n  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(initialAvatarUrl || undefined);\r\n  const profileFormRef = useRef<ProfileFormRef>(null);\r\n  const addressFormRef = useRef<AddressFormRef>(null);\r\n  const [isPending, startTransition] = useTransition();\r\n  console.log('ProfilePageClient - current isPending state:', isPending);\r\n  const [formState, setFormState] = useState<UnifiedProfileFormState>({\r\n    message: null,\r\n    errors: {},\r\n    success: false\r\n  });\r\n\r\n  const handleUnifiedSubmit = async () => {\r\n    // Get data from both forms\r\n    const profileData = profileFormRef.current?.getFormData();\r\n    const addressData = addressFormRef.current?.getFormData();\r\n\r\n    // Validate profile form (required)\r\n    const isProfileValid = profileFormRef.current?.validateForm() ?? false;\r\n    if (!isProfileValid) {\r\n      toast.error('Please check your profile information. Name is required.');\r\n      return;\r\n    }\r\n\r\n    // Validate address form (optional)\r\n    const isAddressValid = addressFormRef.current?.validateForm() ?? true; // Address is optional\r\n\r\n    // Double-check if name is provided (required) - this should now work correctly\r\n    if (!profileData?.name?.trim()) {\r\n      toast.error('Name is required');\r\n      return;\r\n    }\r\n\r\n    // If address data is partially filled, validate that required fields are present\r\n    const hasAnyAddressField = addressData && (\r\n      addressData.pincode || addressData.city || addressData.state || addressData.locality\r\n    );\r\n\r\n    if (hasAnyAddressField && !isAddressValid) {\r\n      toast.error('Please complete all required address fields or leave them empty');\r\n      return;\r\n    }\r\n\r\n    // Create FormData for submission\r\n    const formData = new FormData();\r\n    formData.append('name', profileData.name);\r\n\r\n    // Add address data if provided\r\n    if (addressData) {\r\n      formData.append('address', addressData.address || '');\r\n      formData.append('pincode', addressData.pincode || '');\r\n      formData.append('city', addressData.city || '');\r\n      formData.append('state', addressData.state || '');\r\n      formData.append('locality', addressData.locality || '');\r\n    }\r\n\r\n    // Submit the unified form\r\n    startTransition(async () => {\r\n      try {\r\n        const initialState: UnifiedProfileFormState = {\r\n          message: null,\r\n          errors: {},\r\n          success: false\r\n        };\r\n\r\n        const result = await updateCustomerProfileAndAddress(initialState, formData);\r\n        setFormState(result);\r\n        console.log('ProfilePageClient - formState after server action:', result);\r\n\r\n        if (result.success) {\r\n          toast.success(result.message || 'Profile updated successfully!');\r\n        } else {\r\n          toast.error(result.message || 'Failed to update profile');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error submitting unified form:', error);\r\n        const errorState: UnifiedProfileFormState = {\r\n          message: 'An unexpected error occurred. Please try again.',\r\n          success: false,\r\n          errors: {}\r\n        };\r\n        setFormState(errorState);\r\n        toast.error('An unexpected error occurred. Please try again.');\r\n      }\r\n    });\r\n  };\r\n\r\n  // Animation variants for modern SaaS feel\r\n  const containerVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.6,\r\n        staggerChildren: 0.1\r\n      }\r\n    }\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: { duration: 0.5 }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Profile Requirement Dialog */}\r\n      <ProfileRequirementDialog\r\n        hasCompleteAddress={hasCompleteAddress}\r\n      />\r\n\r\n      <motion.div\r\n        initial=\"hidden\"\r\n        animate=\"visible\"\r\n        variants={containerVariants}\r\n        className=\"space-y-8\"\r\n      >\r\n        {/* Modern SaaS Header Section */}\r\n        <motion.div variants={itemVariants} className=\"space-y-6\">\r\n          <div className=\"flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60\">\r\n            <div className=\"space-y-1\">\r\n              <div className=\"flex items-center gap-3 mb-2\">\r\n                <div className=\"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20\">\r\n                  <User className=\"w-5 h-5 text-primary\" />\r\n                </div>\r\n                <div className=\"h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700\" />\r\n                <div className=\"text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase\">\r\n                  Customer Profile\r\n                </div>\r\n              </div>\r\n              <h1 className=\"text-3xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-600 dark:from-neutral-100 dark:to-neutral-400 bg-clip-text text-transparent\">\r\n                Profile Management\r\n              </h1>\r\n              <p className=\"text-neutral-600 dark:text-neutral-400 text-lg\">\r\n                Manage your personal information and preferences\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Full Width Two-Column Layout - Matching Business Pages */}\r\n        <div className=\"grid gap-8 grid-cols-1 lg:grid-cols-2\">\r\n          {/* Left Column - Avatar and Personal Information */}\r\n          <motion.div variants={itemVariants} className=\"space-y-8\">\r\n            {/* Avatar and Profile Overview Card */}\r\n            <Card className=\"bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300\">\r\n              <CardContent className=\"p-8\">\r\n                <div className=\"text-center space-y-6\">\r\n                  {/* Enhanced Avatar Section */}\r\n                  <div className=\"relative\">\r\n                    <AvatarUpload\r\n                      initialAvatarUrl={avatarUrl}\r\n                      userName={initialName}\r\n                      onUpdateAvatar={(url) => setAvatarUrl(url)}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* User Info */}\r\n                  <div className=\"space-y-2\">\r\n                    <h3 className=\"text-xl font-semibold text-foreground\">\r\n                      {initialName || \"Customer\"}\r\n                    </h3>\r\n                    <p className=\"text-sm text-muted-foreground\">\r\n                      Dukancard Customer\r\n                    </p>\r\n                  </div>\r\n\r\n                  {/* Profile Completion Stats */}\r\n                  <div className=\"grid grid-cols-2 gap-4 pt-4 border-t border-neutral-200/60 dark:border-neutral-800/60\">\r\n                    <div className=\"text-center\">\r\n                      <div className=\"text-2xl font-bold text-primary\">\r\n                        {hasCompleteAddress ? \"✓\" : \"○\"}\r\n                      </div>\r\n                      <div className=\"text-xs text-muted-foreground\">\r\n                        Address\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"text-center\">\r\n                      <div className=\"text-2xl font-bold text-primary\">\r\n                        {avatarUrl ? \"✓\" : \"○\"}\r\n                      </div>\r\n                      <div className=\"text-xs text-muted-foreground\">\r\n                        Avatar\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n\r\n            {/* Personal Information Form Card */}\r\n            <Card className=\"bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300\">\r\n              <CardHeader className=\"border-b border-neutral-200/60 dark:border-neutral-800/60\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/10 to-blue-500/5 border border-blue-500/20\">\r\n                    <User className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\r\n                  </div>\r\n                  <div>\r\n                    <h2 className=\"text-xl font-semibold text-foreground\">\r\n                      Personal Information\r\n                    </h2>\r\n                    <p className=\"text-sm text-muted-foreground\">\r\n                      Update your name and personal details\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className=\"p-8\">\r\n                <ProfileForm\r\n                  ref={profileFormRef}\r\n                  initialName={initialName}\r\n                  hideSubmitButton={true}\r\n                />\r\n              </CardContent>\r\n            </Card>\r\n          </motion.div>\r\n\r\n          {/* Right Column - Address Information */}\r\n          <motion.div variants={itemVariants} className=\"space-y-8\">\r\n\r\n            {/* Address Information Card - Full Height */}\r\n            <Card className=\"bg-gradient-to-br from-card to-card/50 border-neutral-200/60 dark:border-neutral-800/60 shadow-lg hover:shadow-xl transition-all duration-300 h-fit\">\r\n              <CardHeader className=\"border-b border-neutral-200/60 dark:border-neutral-800/60\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/10 to-emerald-500/5 border border-emerald-500/20\">\r\n                    <MapPin className=\"w-5 h-5 text-emerald-600 dark:text-emerald-400\" />\r\n                  </div>\r\n                  <div>\r\n                    <h2 className=\"text-xl font-semibold text-foreground\">\r\n                      Address Information\r\n                    </h2>\r\n                    <p className=\"text-sm text-muted-foreground\">\r\n                      Update your address details (optional)\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className=\"p-8\">\r\n                <AddressForm\r\n                  ref={addressFormRef}\r\n                  initialData={initialAddressData || undefined}\r\n                  hideSubmitButton={true}\r\n                />\r\n              </CardContent>\r\n            </Card>\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* Full Width Action Section - Matching Business Pages */}\r\n        <motion.div variants={itemVariants} className=\"space-y-6\">\r\n          {/* Save Button Section - Full Width */}\r\n          <Card className=\"bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20 shadow-lg\">\r\n            <CardContent className=\"p-8\">\r\n              <div className=\"flex flex-col lg:flex-row items-center justify-between gap-6\">\r\n                <div className=\"text-center lg:text-left\">\r\n                  <h3 className=\"text-lg font-semibold text-foreground mb-2\">\r\n                    Ready to save your changes?\r\n                  </h3>\r\n                  <p className=\"text-sm text-muted-foreground\">\r\n                    Make sure all information is correct before saving\r\n                  </p>\r\n                </div>\r\n                <Button\r\n                  onClick={handleUnifiedSubmit}\r\n                  disabled={isPending}\r\n                  size=\"lg\"\r\n                  className=\"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-200 min-w-[160px]\"\r\n                >\r\n                  {isPending ? (\r\n                    <>\r\n                      <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\r\n                      Saving...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Save className=\"w-5 h-5 mr-2\" />\r\n                      Save Profile\r\n                    </>\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Error Display */}\r\n          {formState.message && !formState.success && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"p-4 bg-destructive/10 border border-destructive/20 rounded-lg\"\r\n            >\r\n              <p className=\"text-sm text-destructive\">{formState.message}</p>\r\n            </motion.div>\r\n          )}\r\n        </motion.div>\r\n      </motion.div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AA2Be,SAAS,kBAAkB,EACxC,WAAW,EACX,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,KAAK,EACH;;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,oBAAoB;IACnF,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IACjD,QAAQ,GAAG,CAAC,gDAAgD;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;QAClE,SAAS;QACT,QAAQ,CAAC;QACT,SAAS;IACX;IAEA,MAAM,sBAAsB;QAC1B,2BAA2B;QAC3B,MAAM,cAAc,eAAe,OAAO,EAAE;QAC5C,MAAM,cAAc,eAAe,OAAO,EAAE;QAE5C,mCAAmC;QACnC,MAAM,iBAAiB,eAAe,OAAO,EAAE,kBAAkB;QACjE,IAAI,CAAC,gBAAgB;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,mCAAmC;QACnC,MAAM,iBAAiB,eAAe,OAAO,EAAE,kBAAkB,MAAM,sBAAsB;QAE7F,+EAA+E;QAC/E,IAAI,CAAC,aAAa,MAAM,QAAQ;YAC9B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,iFAAiF;QACjF,MAAM,qBAAqB,eAAe,CACxC,YAAY,OAAO,IAAI,YAAY,IAAI,IAAI,YAAY,KAAK,IAAI,YAAY,QAAQ,AACtF;QAEA,IAAI,sBAAsB,CAAC,gBAAgB;YACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,iCAAiC;QACjC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,YAAY,IAAI;QAExC,+BAA+B;QAC/B,IAAI,aAAa;YACf,SAAS,MAAM,CAAC,WAAW,YAAY,OAAO,IAAI;YAClD,SAAS,MAAM,CAAC,WAAW,YAAY,OAAO,IAAI;YAClD,SAAS,MAAM,CAAC,QAAQ,YAAY,IAAI,IAAI;YAC5C,SAAS,MAAM,CAAC,SAAS,YAAY,KAAK,IAAI;YAC9C,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ,IAAI;QACtD;QAEA,0BAA0B;QAC1B,gBAAgB;YACd,IAAI;gBACF,MAAM,eAAwC;oBAC5C,SAAS;oBACT,QAAQ,CAAC;oBACT,SAAS;gBACX;gBAEA,MAAM,SAAS,MAAM,CAAA,GAAA,mMAAA,CAAA,kCAA+B,AAAD,EAAE,cAAc;gBACnE,aAAa;gBACb,QAAQ,GAAG,CAAC,sDAAsD;gBAElE,IAAI,OAAO,OAAO,EAAE;oBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI;gBAClC,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;gBAChC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,MAAM,aAAsC;oBAC1C,SAAS;oBACT,SAAS;oBACT,QAAQ,CAAC;gBACX;gBACA,aAAa;gBACb,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE;;0BAEE,6LAAC,sMAAA,CAAA,UAAwB;gBACvB,oBAAoB;;;;;;0BAGtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,SAAQ;gBACR,UAAU;gBACV,WAAU;;kCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;kCAC5C,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;0DAAqF;;;;;;;;;;;;kDAItG,6LAAC;wCAAG,WAAU;kDAA8I;;;;;;kDAG5J,6LAAC;wCAAE,WAAU;kDAAiD;;;;;;;;;;;;;;;;;;;;;;kCAQpE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;gCAAc,WAAU;;kDAE5C,6LAAC,4HAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,0LAAA,CAAA,UAAY;4DACX,kBAAkB;4DAClB,UAAU;4DACV,gBAAgB,CAAC,MAAQ,aAAa;;;;;;;;;;;kEAK1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,eAAe;;;;;;0EAElB,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAM/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,qBAAqB,MAAM;;;;;;kFAE9B,6LAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;0EAIjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,YAAY,MAAM;;;;;;kFAErB,6LAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAUzD,6LAAC,4HAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,4HAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EAGtD,6LAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;;;;;;0DAMnD,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC,2KAAA,CAAA,cAAW;oDACV,KAAK;oDACL,aAAa;oDACb,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0CAO1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;gCAAc,WAAU;0CAG5C,cAAA,6LAAC,4HAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAwC;;;;;;0EAGtD,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;sDAMnD,6LAAC,4HAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC,yLAAA,CAAA,UAAW;gDACV,KAAK;gDACL,aAAa,sBAAsB;gDACnC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAE5C,6LAAC,4HAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,6LAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAI/C,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU;gDACV,MAAK;gDACL,WAAU;0DAET,0BACC;;sEACE,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA8B;;iFAInD;;sEACE,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;4BAU5C,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,kBACtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAE,WAAU;8CAA4B,UAAU,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;AAOxE;GApTwB;;QASe,6JAAA,CAAA,gBAAa;;;KAT5B", "debugId": null}}]}