'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Heart, Loader2, ExternalLink, User, Building2 } from 'lucide-react';
import { unlikeBusiness } from '@/lib/actions/interactions';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Shared types for like data
export interface ProfileData {
  id: string;
  name: string | null;
  slug: string | null;
  logo_url?: string | null;
  avatar_url?: string | null;
  locality: string | null;
  city: string | null;
  state: string | null;
  type: 'business' | 'customer';
}

export interface LikeData {
  id: string;
  profile: ProfileData | null;
}

interface LikeCardProps {
  likeId: string;
  profile: ProfileData;
  onUnlikeSuccess?: (_likeId: string) => void;
  showUnlike?: boolean;
  variant?: 'default' | 'compact';
  showVisitButton?: boolean;
  showAddress?: boolean;
  showRedirectIcon?: boolean;
}

export default function LikeCard({
  likeId,
  profile,
  onUnlikeSuccess,
  showUnlike = true,
  variant = 'default',
  showVisitButton = true,
  showAddress = true,
  showRedirectIcon = false
}: LikeCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Format location string exactly like subscription cards
  const formatLocation = (profile: ProfileData) => {
    if (!showAddress) return null;

    const addressParts = [
      profile.locality,
      profile.city,
      profile.state,
    ].filter(Boolean);

    return addressParts.length > 0 ? addressParts.join(', ') : 'Location not specified';
  };

  // Handle unlike action
  const handleUnlike = async () => {
    if (!showUnlike || !onUnlikeSuccess) return;

    setIsLoading(true);
    try {
      const result = await unlikeBusiness(profile.id);
      if (result.success) {
        toast.success(`${profile.type === 'business' ? 'Business' : 'Profile'} unliked successfully`);
        onUnlikeSuccess(likeId);
      } else {
        toast.error(result.error || `Failed to unlike ${profile.type}`);
      }
    } catch (error) {
      console.error('Error unliking:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const profileUrl = profile.slug ? `/${profile.slug}` : '#';
  const avatarUrl = profile.logo_url || profile.avatar_url;
  const displayName = profile.name || 'Unknown';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      className={cn(
        "rounded-lg border p-0 overflow-hidden transition-all duration-300",
        "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",
        "shadow-sm hover:shadow-md",
        variant === 'compact' && "max-w-sm"
      )}
    >
      {/* Card background with subtle pattern */}
      <div
        className="absolute inset-0 pointer-events-none opacity-5 dark:opacity-10"
        style={{
          backgroundImage: `url("/decorative/card-texture.svg")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      ></div>

      <div className="relative z-10 p-4">
        <div className="flex items-start gap-3">
          {/* Avatar - only clickable for businesses with slugs */}
          {profile.type === 'business' && profile.slug ? (
            <Link
              href={profileUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="hover:opacity-80 transition-opacity"
            >
              <Avatar className="h-12 w-12 border border-neutral-200 dark:border-neutral-800">
                {avatarUrl ? (
                  <AvatarImage
                    src={avatarUrl}
                    alt={displayName}
                  />
                ) : null}
                <AvatarFallback className="bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-300">
                  {displayName[0]?.toUpperCase() || "?"}
                </AvatarFallback>
              </Avatar>
            </Link>
          ) : (
            <Avatar className="h-12 w-12 border border-neutral-200 dark:border-neutral-800">
              {avatarUrl ? (
                <AvatarImage
                  src={avatarUrl}
                  alt={displayName}
                />
              ) : null}
              <AvatarFallback className="bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-300">
                {displayName[0]?.toUpperCase() || "?"}
              </AvatarFallback>
            </Avatar>
          )}

          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  {/* Name - only clickable for businesses with slugs */}
                  {profile.type === 'business' && profile.slug ? (
                    <Link
                      href={profileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center gap-1"
                    >
                      <h3 className="font-medium text-neutral-800 dark:text-neutral-100 truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {displayName}
                      </h3>
                      {showRedirectIcon && (
                        <ExternalLink className="w-3 h-3 text-neutral-400 group-hover:text-blue-500 transition-colors flex-shrink-0" />
                      )}
                    </Link>
                  ) : (
                    <h3 className="font-medium text-neutral-800 dark:text-neutral-100 truncate">
                      {displayName}
                    </h3>
                  )}
                </div>

                {/* Badge below name - exact design from review component */}
                <div className="flex items-center gap-1 mt-1">
                  {profile.type === 'business' ? (
                    <div className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium">
                      <Building2 className="h-3 w-3" />
                      Business
                    </div>
                  ) : (
                    <div className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium">
                      <User className="h-3 w-3" />
                      Customer
                    </div>
                  )}
                </div>

              </div>
            </div>
          </div>
        </div>

        {/* Address - moved outside to align properly */}
        {showAddress && (
          <div className="mt-2 ml-15"> {/* ml-15 to align with the text content */}
            <p className="text-xs text-neutral-500 dark:text-neutral-400 flex items-center">
              <span className="inline-block h-1 w-1 rounded-full bg-neutral-300 dark:bg-neutral-700 mr-2"></span>
              {formatLocation(profile)}
            </p>
          </div>
        )}

        <div className="flex items-center justify-between mt-4">
          {/* Visit button - only show if enabled and for businesses with slugs */}
          {showVisitButton && profile.type === 'business' && profile.slug ? (
            <Button
              asChild
              variant="outline"
              size="sm"
              className="text-xs h-8"
            >
              <Link href={profileUrl} target="_blank" rel="noopener noreferrer">
                Visit Card
              </Link>
            </Button>
          ) : (
            <div></div> // Empty div to maintain layout
          )}

          {showUnlike && onUnlikeSuccess && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-8 text-rose-500 hover:text-rose-600 hover:bg-rose-50 dark:hover:bg-rose-900/20"
              onClick={handleUnlike}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin" />
              ) : (
                <Heart className="h-3.5 w-3.5 mr-1.5 fill-current" />
              )}
              Unlike
            </Button>
          )}
        </div>
      </div>
    </motion.div>
  );
}
