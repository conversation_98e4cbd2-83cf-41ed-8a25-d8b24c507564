"use client";

import { motion } from "framer-motion";
import { Users } from "lucide-react";
import { formatIndianNumberShort } from "@/lib/utils";
import EnhancedMetricCard from "./EnhancedMetricCard";

interface VisitMetricsSectionProps {
  totalUniqueVisits: number;
  todayUniqueVisits: number;
  yesterdayUniqueVisits: number;
  currentMonthUniqueVisits: number;
  isVisitUpdated: boolean;
}

export default function EnhancedVisitMetricsSection({
  totalUniqueVisits,
  todayUniqueVisits,
  yesterdayUniqueVisits,
  isVisitUpdated,
  currentMonthUniqueVisits,
}: VisitMetricsSectionProps) {
  // Calculate trend percentage for today vs yesterday
  const calculateTrend = () => {
    if (yesterdayUniqueVisits === 0) return { value: 0, isPositive: true };

    const difference = todayUniqueVisits - yesterdayUniqueVisits;
    const percentage = Math.round((difference / yesterdayUniqueVisits) * 100);

    return {
      value: percentage,
      isPositive: percentage >= 0,
    };
  };

  const todayTrend = calculateTrend();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      }
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="space-y-6">
        {/* Section Header */}
        <motion.div variants={headerVariants}>
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
                <Users className="w-5 h-5 text-primary" />
              </div>
              <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
              <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
                Traffic Analytics
              </div>
            </div>
            <div className="space-y-1">
              <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
                Visitor Metrics
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400 text-sm leading-relaxed">
                Monitor your business card traffic patterns and visitor engagement over time.
              </p>
            </div>
          </div>
        </motion.div>

        {/* All Visitor Metrics in One Grid */}
        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-4">
          {/* Total Unique Visits */}
          <EnhancedMetricCard
            title="Total Unique Visits"
            value={formatIndianNumberShort(totalUniqueVisits)}
            icon={Users}
            description="All-time unique visitors"
            color="blue"
            isUpdated={isVisitUpdated}
          />

          {/* Today's Unique Visits */}
          <EnhancedMetricCard
            title="Today's Visits"
            value={formatIndianNumberShort(todayUniqueVisits)}
            icon={Users}
            description="Unique visitors today"
            color="green"
            isUpdated={isVisitUpdated}
            trend={todayTrend}
          />

          {/* Yesterday's Unique Visits */}
          <EnhancedMetricCard
            title="Yesterday's Visits"
            value={formatIndianNumberShort(yesterdayUniqueVisits)}
            icon={Users}
            description="Unique visitors yesterday"
            color="purple"
            isUpdated={isVisitUpdated}
          />

          {/* Current Month's Unique Visits */}
          <EnhancedMetricCard
            title="This Month"
            value={formatIndianNumberShort(currentMonthUniqueVisits)}
            icon={Users}
            description="Unique visitors this month"
            color="indigo"
            isUpdated={isVisitUpdated}
          />
        </div>
      </div>
    </motion.div>
  );
}
