"use client";

import { useState } from "react";
import { motion } from "framer-motion";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Mail, Edit3, Info } from "lucide-react";
import EmailUpdateDialog from "./EmailUpdateDialog";

interface LinkEmailSectionProps {
  currentEmail?: string | null | undefined;
  currentPhone?: string | null | undefined;
  registrationType: 'google' | 'email' | 'phone';
}

export default function LinkEmailSection({
  currentEmail,
  registrationType
}: LinkEmailSectionProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleEmailUpdated = () => {
    // Refresh the page to show updated email
    window.location.reload();
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        {/* Enhanced Section Header */}
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20">
              <Mail className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-50">
                Email Address
              </h3>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                {currentEmail ? "Manage your account email address" : "Link an email address for better security"}
              </p>
            </div>
          </div>
        </div>

        {/* Email Display and Update Section */}
        <div className="bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 p-6 space-y-4">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 block">
                Current Email Address
              </label>
              <Input
                type="email"
                value={currentEmail || ''}
                placeholder={currentEmail ? currentEmail : "No email address linked"}
                readOnly
                className="bg-neutral-50 dark:bg-neutral-800 cursor-not-allowed"
              />
              <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                {currentEmail ? "This is your current email address" : "No email address is currently linked to your account"}
              </p>
            </div>

            <Button
              onClick={() => setIsDialogOpen(true)}
              className="w-full sm:w-auto"
              variant="default"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              {currentEmail ? "Update Email Address" : "Link Email Address"}
            </Button>
          </div>
        </div>

        {/* Registration Type Info */}
        {registrationType === 'google' && (
          <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start gap-3">
              <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                  Google Account Integration
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  You signed up with Google. You can still link or update your email address for additional security options.
                </p>
              </div>
            </div>
          </div>
        )}
      </motion.div>

      {/* Email Update Dialog */}
      <EmailUpdateDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        currentEmail={currentEmail}
        onEmailUpdated={handleEmailUpdated}
      />
    </>
  );
}

