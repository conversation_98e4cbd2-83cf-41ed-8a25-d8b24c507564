"use server";

import { createClient } from "@/utils/supabase/server";

// revalidatePath is imported but not used in this file
// import { revalidatePath } from 'next/cache';
import { getUserProfilesForReviews } from "./secureCustomerProfiles";
import { Tables } from "@/types/supabase";

type ReviewWithUser = Tables<'ratings_reviews'> & {
  user_profile?: {
    id: string;
    name: string | null;
    avatar_url: string | null;
    is_business: boolean;
    business_slug: string | null;
  };
};
export type { ReviewWithUser };

export type ReviewSortBy =
  | "newest"
  | "oldest"
  | "highest_rating"
  | "lowest_rating";

// Fetch reviews for a business with pagination and sorting
export async function fetchBusinessReviews(
  businessProfileId: string,
  page: number = 1,
  limit: number = 5,
  sortBy: ReviewSortBy = "newest"
): Promise<{
  data: ReviewWithUser[];
  totalCount: number;
  error?: string;
}> {
  const supabase = await createClient();

  try {
    // Create admin client for secure operations
    const supabase = await createClient();

    // First, get the reviews with pagination and sorting
    let query = supabase
      .from("ratings_reviews")
      .select("*", { count: "exact" })
      .eq("business_profile_id", businessProfileId)
      // Don't show reviews where the user is reviewing their own business
      .neq("user_id", businessProfileId);

    // Apply sorting
    switch (sortBy) {
      case "oldest":
        query = query.order("created_at", { ascending: true });
        break;
      case "highest_rating":
        query = query.order("rating", { ascending: false });
        break;
      case "lowest_rating":
        query = query.order("rating", { ascending: true });
        break;
      case "newest":
      default:
        query = query.order("created_at", { ascending: false });
        break;
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Execute the query
    const { data: reviewsData, error: reviewsError, count } = await query;

    if (reviewsError) {
      console.error("Error fetching reviews:", reviewsError);
      return { data: [], totalCount: 0, error: reviewsError.message };
    }

    // If no reviews, return empty array
    if (!reviewsData || reviewsData.length === 0) {
      return { data: [], totalCount: count || 0 };
    }

    // Get all user IDs from the reviews
    const userIds = [...new Set(reviewsData.map((review: { user_id: string }) => review.user_id))] as string[];

    // Use the secure method to fetch user profiles (both customer and business)
    const { data: profilesMap, error: profilesError } =
      await getUserProfilesForReviews(userIds);

    if (profilesError) {
      console.error("Error fetching user profiles:", profilesError);
      // Continue without profiles
    }

    // Get business user IDs from the profiles
    const businessUserIds = userIds.filter(
      (id: string) => profilesMap?.[id]?.is_business
    );

    // Create a map of business IDs to their slugs
    let businessSlugMap: Record<string, string | null> = {};

    // Fetch business slugs for all business reviewers at once
    if (businessUserIds.length > 0) {
      const { data: businessSlugs } = await supabase
        .from("business_profiles")
        .select("id, business_slug")
        .in("id", businessUserIds);

      // Create a map of business IDs to their slugs
      if (businessSlugs) {
        businessSlugMap = businessSlugs.reduce((acc: Record<string, string | null>, business: { id: string; business_slug: string | null }) => {
          acc[business.id] = business.business_slug;
          return acc;
        }, {} as Record<string, string | null>);
      }
    }

    // Process the reviews data with profile information
    const processedData: ReviewWithUser[] = reviewsData.map((review: any) => {
      const profile = profilesMap?.[review.user_id];
      const userProfile = profile
        ? {
            ...profile,
            business_slug: profile.is_business
              ? businessSlugMap[review.user_id] || null
              : null,
          }
        : undefined;

      return {
        ...review,
        user_profile: userProfile,
      };
    });

    return {
      data: processedData,
      totalCount: count || 0,
    };
  } catch (error) {
    console.error("Unexpected error in fetchBusinessReviews:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { data: [], totalCount: 0, error: errorMessage };
  }
}
