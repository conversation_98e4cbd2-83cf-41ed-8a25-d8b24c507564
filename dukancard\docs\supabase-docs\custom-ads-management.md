# Custom Ads Management System

This document provides a comprehensive guide to the custom ads system in Dukancard, including how to manage, add, edit, and delete ads, as well as an overview of the database structure and implementation details.

## Table of Contents

1. [Overview](#overview)
2. [Database Structure](#database-structure)
3. [Managing Custom Ads](#managing-custom-ads)
   - [Adding a New Ad](#adding-a-new-ad)
   - [Updating an Existing Ad](#updating-an-existing-ad)
   - [Deleting an Ad](#deleting-an-ad)
   - [Viewing Ad Statistics](#viewing-ad-statistics)
4. [Ad Targeting Logic](#ad-targeting-logic)
5. [Implementation Details](#implementation-details)
   - [Database Functions](#database-functions)
   - [Database Views](#database-views)
   - [Scheduled Jobs](#scheduled-jobs)
   - [Client-Side Components](#client-side-components)
   - [Server-Side Code](#server-side-code)

## Overview

The custom ads system allows for targeted advertising based on pincodes. Ads can be either:

- **Pincode-specific**: Shown only to users in specific pincodes
- **Global**: Shown to all users as a fallback when no pincode-specific ads are available

Ads also have an expiry date, after which they are automatically deactivated. The system follows this logic for displaying ads:

1. First try to find a pincode-specific ad that hasn't expired
2. If none found, fallback to global ads that haven't expired
3. If no global ads, fallback to Google AdSense

## Database Structure

### Tables

#### custom_ads

Stores the main ad information:

| Column       | Type        | Description                                          |
| ------------ | ----------- | ---------------------------------------------------- |
| id           | UUID        | Primary key                                          |
| ad_image_url | TEXT        | URL of the ad image                                  |
| ad_link_url  | TEXT        | URL to redirect to when the ad is clicked (optional) |
| is_active    | BOOLEAN     | Whether the ad is active                             |
| expiry_date  | TIMESTAMPTZ | When the ad expires (optional)                       |
| created_at   | TIMESTAMPTZ | When the ad was created                              |

#### custom_ad_targets

Junction table that stores targeting information:

| Column     | Type        | Description                                    |
| ---------- | ----------- | ---------------------------------------------- |
| id         | UUID        | Primary key                                    |
| ad_id      | UUID        | Foreign key to custom_ads.id                   |
| pincode    | VARCHAR(6)  | Pincode to target (or '000000' for global ads) |
| is_global  | BOOLEAN     | Whether this is a global ad                    |
| created_at | TIMESTAMPTZ | When the target was created                    |

### Views

#### ad_targets_view

Shows all ads and their targets:

```sql
SELECT
  c.id as ad_id,
  c.ad_image_url,
  c.ad_link_url,
  c.is_active,
  c.expiry_date,
  CASE WHEN c.expiry_date IS NULL THEN NULL
       WHEN c.expiry_date <= NOW() THEN 'Expired'
       WHEN c.expiry_date <= NOW() + INTERVAL '7 days' THEN 'Expiring Soon'
       ELSE 'Active'
  END as expiry_status,
  t.id as target_id,
  t.pincode,
  t.is_global,
  c.created_at as ad_created_at,
  t.created_at as target_created_at
FROM custom_ads c
JOIN custom_ad_targets t ON c.id = t.ad_id
ORDER BY c.created_at DESC, t.created_at DESC;
```

#### expired_ads_view

Shows expired ads:

```sql
SELECT
  c.id as ad_id,
  c.ad_image_url,
  c.ad_link_url,
  c.is_active,
  c.expiry_date,
  c.created_at,
  CASE
    WHEN c.expiry_date IS NULL THEN 'No Expiry'
    WHEN c.expiry_date <= NOW() THEN 'Expired'
    WHEN c.expiry_date <= NOW() + INTERVAL '7 days' THEN 'Expiring Soon'
    ELSE 'Active'
  END as status,
  NOW() - c.expiry_date as expired_for
FROM custom_ads c
WHERE c.expiry_date IS NOT NULL AND c.expiry_date <= NOW()
ORDER BY c.expiry_date DESC;
```

## Managing Custom Ads

### Adding a New Ad

To add a new ad, use the `add_custom_ad` function:

```sql
-- For a global ad
SELECT add_custom_ad(
  'https://example.com/ad-image.jpg',  -- ad_image_url
  'https://example.com/landing-page',  -- ad_link_url
  TRUE,                                -- is_active
  TRUE,                                -- is_global
  NOW() + INTERVAL '1 year'            -- expiry_date (optional)
);

-- For a pincode-specific ad
SELECT add_custom_ad(
  'https://example.com/ad-image.jpg',  -- ad_image_url
  'https://example.com/landing-page',  -- ad_link_url
  TRUE,                                -- is_active
  FALSE,                               -- is_global
  NOW() + INTERVAL '6 months',         -- expiry_date (optional)
  ARRAY['110001', '110002', '110003']  -- pincodes to target
);
```

### Updating an Existing Ad

To update an existing ad, use the `update_custom_ad` function:

```sql
-- Update ad image and expiry date
SELECT update_custom_ad(
  'ad-uuid-here',                      -- ad_id
  'https://example.com/new-image.jpg', -- ad_image_url (optional)
  NULL,                                -- ad_link_url (no change)
  NULL,                                -- is_active (no change)
  NOW() + INTERVAL '1 year',           -- expiry_date (optional)
  NULL,                                -- is_global (no change)
  NULL                                 -- pincodes (no change)
);

-- Change targeting from pincode-specific to global
SELECT update_custom_ad(
  'ad-uuid-here',                      -- ad_id
  NULL,                                -- ad_image_url (no change)
  NULL,                                -- ad_link_url (no change)
  NULL,                                -- is_active (no change)
  NULL,                                -- expiry_date (no change)
  TRUE,                                -- is_global
  NULL                                 -- pincodes (ignored for global ads)
);

-- Change targeting from global to pincode-specific
SELECT update_custom_ad(
  'ad-uuid-here',                      -- ad_id
  NULL,                                -- ad_image_url (no change)
  NULL,                                -- ad_link_url (no change)
  NULL,                                -- is_active (no change)
  NULL,                                -- expiry_date (no change)
  FALSE,                               -- is_global
  ARRAY['400001', '400002', '400003']  -- pincodes to target
);
```

### Deleting an Ad

To delete an ad, use the `delete_custom_ad` function:

```sql
SELECT delete_custom_ad('ad-uuid-here');
```

### Viewing Ad Statistics

To view statistics about custom ads, use the `get_custom_ads_stats` function:

```sql
SELECT * FROM get_custom_ads_stats();
```

This returns:

- Total number of ads
- Number of active ads
- Number of expired ads
- Number of global ads
- Number of pincode-targeted ads
- Number of unique pincodes targeted
- Number of ads expiring soon (within 7 days)

To view the most targeted pincodes, use the `get_most_targeted_pincodes` function:

```sql
SELECT * FROM get_most_targeted_pincodes(10); -- Top 10 most targeted pincodes
```

To view all ads for a specific pincode, use the `get_all_ads_for_pincode` function:

```sql
SELECT * FROM get_all_ads_for_pincode('110001');
```

## Ad Targeting Logic

The system uses the following logic to determine which ad to show:

1. First, it checks if there's a pincode-specific ad for the user's pincode that hasn't expired
2. If no pincode-specific ad is found, it checks for global ads that haven't expired
3. If no global ads are found, it shows a placeholder

This logic is implemented in the `get_ad_for_pincode` function:

```sql
SELECT * FROM get_ad_for_pincode('110001');
```

## Implementation Details

### Database Functions

#### get_ad_for_pincode

Returns the appropriate ad for a given pincode, following the targeting logic described above.

#### add_custom_ad

Adds a new ad with targeting information.

#### update_custom_ad

Updates an existing ad, including its targeting information.

#### delete_custom_ad

Deletes an ad and its targeting information.

#### get_all_ads_for_pincode

Gets all ads (both pincode-specific and global) for a specific pincode.

#### get_custom_ads_stats

Gets statistics about custom ads.

#### get_most_targeted_pincodes

Gets the most targeted pincodes.

#### cleanup_expired_ads

Automatically deactivates expired ads.

### Database Views

#### ad_targets_view

Shows all ads and their targets.

#### expired_ads_view

Shows expired ads.

### Scheduled Jobs

A daily job runs the `cleanup_expired_ads` function to automatically deactivate expired ads:

```sql
SELECT cron.schedule(
  'cleanup-expired-ads',
  '0 0 * * *',  -- Run at midnight every day
  'SELECT cleanup_expired_ads();'
);
```

### Client-Side Components

#### GoogleAdScript

A React component that handles the rendering of Google AdSense ads:

```tsx
<GoogleAdScript
  adClient="ca-pub-3570662930292642"
  adSlot="7675098009"
  adFormat="auto"
  fullWidthResponsive={true}
  className="w-full h-full"
/>
```

#### AdSlot

A wrapper component that uses GoogleAdScript to render ads:

```tsx
<AdSlot adKey="public-card-top-right" />
```

### Server-Side Code

The server-side code in `app/[cardSlug]/page.tsx` handles the fetching of ads:

```tsx
// Use the get_ad_for_pincode function to find the appropriate ad
const pincode = businessProfile.pincode || "999999"; // Use a dummy pincode if none provided
const { data: adData, error: adError } = await supabase.rpc(
  "get_ad_for_pincode",
  { target_pincode: pincode }
);

if (adData && adData.length > 0) {
  // Found an ad (either pincode-specific or global)
  topAdData = {
    type: "custom",
    imageUrl: adData[0].ad_image_url,
    linkUrl: adData[0].ad_link_url,
  };
} else {
  // No custom ads found or error occurred, fallback to Google Ads
  if (adError) {
    console.error(`Error fetching ad for pincode ${pincode}:`, adError);
  }
  topAdData = { type: "google" };
}
```

This data is then passed to the client component, which renders either a custom ad or a Google AdSense ad based on the `topAdData` value.
