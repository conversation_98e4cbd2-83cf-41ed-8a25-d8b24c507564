"use server";

import { createClient } from "@/utils/supabase/server";
import { unstable_noStore as noStore } from "next/cache";
import * as z from "zod";

/**
 * Shared utility function to check if a business slug is available
 * Used by both onboarding and dashboard business card edit
 */
export async function checkBusinessSlugAvailability(
  slug: string,
  userId?: string | null
): Promise<{ available: boolean; error?: string }> {
  noStore(); // Ensure this check always hits the database

  // Basic validation
  if (!slug || slug.length < 3) {
    return { available: false, error: "Slug must be at least 3 characters." };
  }

  // Format validation
  const slugFormatCheck = z
    .string()
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .safeParse(slug);

  if (!slugFormatCheck.success) {
    return {
      available: false,
      error: "Invalid format (lowercase, numbers, hyphens only).",
    };
  }

  // Create a fresh Supabase client for each check to avoid any caching issues
  const supabase = await createClient();

  // Get current user if not provided
  let currentUserId = userId;
  if (!currentUserId) {
    const { data: { user } } = await supabase.auth.getUser();
    currentUserId = user?.id;
  }

  try {
    // Use regular client - business_profiles has public read access
    const { data: existingProfile, error } = await supabase
      .from("business_profiles")
      .select("id, business_slug")
      .ilike("business_slug", slug)
      .neq("id", currentUserId ?? "")
      .maybeSingle();

    if (error) {
      return { available: false, error: "Database error checking slug." };
    }

    const isAvailable = !existingProfile;
    return { available: isAvailable };
  } catch (_e) {
    return { available: false, error: "An unexpected error occurred." };
  }
}
