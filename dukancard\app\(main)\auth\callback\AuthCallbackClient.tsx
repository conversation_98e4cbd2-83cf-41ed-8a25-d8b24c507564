"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useState } from "react";

export default function AuthCallbackClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createClient();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogTitle, setDialogTitle] = useState("");
  const [dialogDescription, setDialogDescription] = useState("");

  useEffect(() => {
    const handleAuth = async () => {
      // Check if this window should close itself after auth (from popup flow)
      const shouldCloseWindow = searchParams.get('closeWindow') === 'true';

      const { data: authData, error: authError } = await supabase.auth.getUser();

      // Handle auth errors or no user
      if (authError || !authData?.user) {
        if (shouldCloseWindow) {
          // If this is a popup window and auth failed, close it and let the opener handle it
          window.close();
          return;
        }

        router.push("/login"); // Redirect to login on auth error
        return;
      }

      const userId = authData.user.id;
      // Check if we have redirect and message parameters
      const redirectSlug = searchParams.get('redirect');
      const messageParam = searchParams.get('message');
      const nextParam = searchParams.get('next');
      const emailChangeStatus = searchParams.get('email_change_status');
      const redirectTo = searchParams.get('redirect_to');

      if (emailChangeStatus) {
        setDialogTitle("Email Updated Successfully!");
        setDialogDescription("Your email address has been successfully updated. You will be redirected to your settings page.");
        setDialogOpen(true);

        // Store the redirect information for after dialog close
        if (typeof window !== 'undefined') {
          localStorage.setItem('emailChangeRedirect', redirectTo || 'settings');
        }
        return; // Prevent further useEffect logic from running immediately
      }

      let redirectPath;

      try {
        const { getPostLoginRedirectPath } = await import("@/lib/actions/redirectAfterLogin");
        const defaultRedirectPath = await getPostLoginRedirectPath(supabase, userId);

        const isNewUser = defaultRedirectPath === "/choose-role" || defaultRedirectPath === "/onboarding";
        const isBusinessUser = userId.startsWith("business_") || defaultRedirectPath.includes("/business");

        if (redirectSlug) {
          if (isNewUser) {
            const effectiveRedirectSlug = redirectSlug || (nextParam && nextParam !== '/onboarding' && nextParam !== '/choose-role' ? nextParam.replace(/^\//, '') : null);
            if (isBusinessUser) {
              if (typeof window !== 'undefined' && effectiveRedirectSlug) {
                localStorage.setItem('postOnboardingRedirect', effectiveRedirectSlug);
                if (messageParam) {
                  localStorage.setItem('postOnboardingMessage', messageParam);
                }
              }
              redirectPath = defaultRedirectPath;
            } else {
              let chooseRolePath = defaultRedirectPath;
              if (typeof window !== 'undefined' && effectiveRedirectSlug) {
                localStorage.setItem('chooseRoleRedirect', effectiveRedirectSlug);
                if (messageParam) {
                  localStorage.setItem('chooseRoleMessage', messageParam);
                }
              }
              if (effectiveRedirectSlug) {
                chooseRolePath += `?redirect=${encodeURIComponent(effectiveRedirectSlug)}`;
                if (messageParam) {
                  chooseRolePath += `&message=${encodeURIComponent(messageParam)}`;
                }
              }
              redirectPath = chooseRolePath;
            }
          } else {
            if (messageParam) {
              redirectPath = `/${redirectSlug}?message=${encodeURIComponent(messageParam)}`;
            } else {
              redirectPath = `/${redirectSlug}`;
            }
          }
        } else {
          const effectiveNextParam = nextParam && nextParam !== '/onboarding' && nextParam !== '/choose-role' ? nextParam : null;
          if (effectiveNextParam && isNewUser) {
            const nextParamWithoutSlash = effectiveNextParam.replace(/^\//, '');
            if (isBusinessUser) {
              if (typeof window !== 'undefined') {
                localStorage.setItem('postOnboardingRedirect', nextParamWithoutSlash);
                if (messageParam) {
                  localStorage.setItem('postOnboardingMessage', messageParam);
                }
              }
              redirectPath = defaultRedirectPath;
            } else {
              if (typeof window !== 'undefined') {
                localStorage.setItem('chooseRoleRedirect', nextParamWithoutSlash);
                if (messageParam) {
                  localStorage.setItem('chooseRoleMessage', messageParam);
                }
              }
              let chooseRolePath = defaultRedirectPath;
              chooseRolePath += `?redirect=${encodeURIComponent(nextParamWithoutSlash)}`;
              if (messageParam) {
                chooseRolePath += `&message=${encodeURIComponent(messageParam)}`;
              }
              redirectPath = chooseRolePath;
            }
          } else {
            redirectPath = defaultRedirectPath;
            if (redirectPath === "/") {
              console.warn(
                "Auth callback: couldn't determine dashboard. Redirecting to home."
              );
            }
          }
        }
      } catch (error) {
        console.error("Unexpected error in shared redirect logic:", error);
        redirectPath = "/?view=home";
      }

      if (shouldCloseWindow && window.opener) {
        try {
          if (window.opener.location.origin === window.location.origin) {
            window.opener.location.href = redirectPath;
          }
        } catch (e) {
          console.warn("Could not redirect opener window:", e);
        }
        setTimeout(() => {
          window.close();
        }, 500);
      } else {
        router.push(redirectPath || '/?view=home');
      }



      };
    handleAuth();
  }, [router, supabase, searchParams]);

  // Check if this is a popup window that will close itself
  const isPopupWindow = searchParams.get('closeWindow') === 'true';

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="mx-auto h-8 w-8 text-[var(--brand-gold)] animate-spin" />
        <p className="mt-4 text-lg font-medium">Authenticating...</p>
        {isPopupWindow && (
          <p className="mt-2 text-sm text-muted-foreground">
            This window will close automatically after sign-in is complete.
          </p>
        )}
      </div>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[425px]" onEscapeKeyDown={(e) => e.preventDefault()} onPointerDownOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{dialogTitle}</DialogTitle>
            <DialogDescription>{dialogDescription}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" onClick={async () => {
                setDialogOpen(false); // Close the dialog immediately

                // Check if this is an email change redirect
                const emailChangeRedirect = typeof window !== 'undefined' ? localStorage.getItem('emailChangeRedirect') : null;

                const { data: authData, error: authError } = await supabase.auth.getUser();
                if (authError || !authData?.user) {
                  // User is not logged in
                  router.push("/login");
                } else {
                  // User is logged in
                  const userId = authData.user.id;

                  if (emailChangeRedirect === 'settings') {
                    // Clean up localStorage
                    if (typeof window !== 'undefined') {
                      localStorage.removeItem('emailChangeRedirect');
                    }

                    // Determine if business or customer user to redirect to correct settings
                    const { getPostLoginRedirectPath } = await import("@/lib/actions/redirectAfterLogin");
                    const defaultPath = await getPostLoginRedirectPath(supabase, userId);

                    if (defaultPath.includes('/business/')) {
                      router.push('/dashboard/business/settings?email_change_success=true');
                    } else {
                      router.push('/dashboard/customer/settings?email_change_success=true');
                    }
                  } else {
                    // Default redirect behavior
                    const { getPostLoginRedirectPath } = await import("@/lib/actions/redirectAfterLogin");
                    const redirectPath = await getPostLoginRedirectPath(supabase, userId);
                    router.push(redirectPath);
                  }
                }
              }}>
                Continue to Settings
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
