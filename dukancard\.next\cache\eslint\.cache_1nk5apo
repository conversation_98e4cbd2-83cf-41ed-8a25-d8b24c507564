[{"C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts": "1", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx": "2", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx": "3", "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx": "4", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\actions.ts": "5", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\DailyVisitTrendChart.tsx": "6", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedAnalyticsPageClient.tsx": "7", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedChartCard.tsx": "8", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedEngagementMetricsSection.tsx": "9", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedMetricCard.tsx": "10", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedVisitMetricsSection.tsx": "11", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\HourlyVisitTrendChart.tsx": "12", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\MonthlyVisitTrendChart.tsx": "13", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\PremiumFeatureLock.tsx": "14", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\page.tsx": "15", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customAdUpload.ts": "16", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customHeaderUpload.ts": "17", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts": "18", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeSpecificHeaderUpload.ts": "19", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts": "20", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts": "21", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts": "22", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx": "23", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx": "24", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx": "25", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx": "26", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx": "27", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx": "28", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx": "29", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx": "30", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx": "31", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx": "32", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx": "33", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx": "34", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomAdUpload.tsx": "35", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomBrandingSection.tsx": "36", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts": "37", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx": "38", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx": "39", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx": "40", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ThemeSpecificHeaderUpload.tsx": "41", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx": "42", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx": "43", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx": "44", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx": "45", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx": "46", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx": "47", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts": "48", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx": "49", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx": "50", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts": "51", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts": "52", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts": "53", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx": "54", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx": "55", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx": "56", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts": "57", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts": "58", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts": "59", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts": "60", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx": "61", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts": "62", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts": "63", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts": "64", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts": "65", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts": "66", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts": "67", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts": "68", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts": "69", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx": "70", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedSubscriptionStatus.tsx": "71", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx": "72", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx": "73", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx": "74", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx": "75", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx": "76", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedSubscriptionStatus.tsx": "77", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx": "78", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts": "79", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx": "80", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx": "81", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx": "82", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx": "83", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx": "84", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx": "85", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx": "86", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx": "87", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx": "88", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx": "89", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx": "90", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts": "91", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts": "92", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts": "93", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx": "94", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts": "95", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts": "96", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts": "97", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts": "98", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx": "99", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts": "100", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx": "101", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx": "102", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx": "103", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx": "104", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx": "105", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx": "106", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx": "107", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx": "108", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\animations.ts": "109", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BillingToggle.tsx": "110", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BusinessPlanSkeleton.tsx": "111", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\ConfirmationDialog.tsx": "112", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogBackground.tsx": "113", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogManager.tsx": "114", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedActionButtons.tsx": "115", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedCurrentPlanSection.tsx": "116", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedGlowButton.tsx": "117", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedInvoiceHistoryCard.tsx": "118", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanPageWithManager.tsx": "119", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanSelectionSection.tsx": "120", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedSubscriptionDetailsCard.tsx": "121", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedTrialAlert.tsx": "122", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\FirstTimePaidPlanDialog.tsx": "123", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PaymentMethodLimitationsDialog.tsx": "124", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanBackground.tsx": "125", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanPageContainer.tsx": "126", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\RealtimePlanPageClient.tsx": "127", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SimplifiedPlanActionDialog.tsx": "128", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\DialogComponents.tsx": "129", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EligiblePaymentMethodsCard.tsx": "130", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedPaymentHistoryCard.tsx": "131", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedSubscriptionActionCard.tsx": "132", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\hooks.ts": "133", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\index.ts": "134", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernCancellationDialog.tsx": "135", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernRefundDialog.tsx": "136", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernSubscriptionStatusCard.tsx": "137", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernTabs.tsx": "138", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionActions.ts": "139", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionButton.tsx": "140", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionManager.tsx": "141", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionProcessingIndicator.tsx": "142", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionTabsToggle.tsx": "143", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\types.ts": "144", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusBadge.tsx": "145", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusIndicator.tsx": "146", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionTabContent.tsx": "147", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialManagement.tsx": "148", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialSubscriptionWarningDialog.tsx": "149", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\UpiPaymentMethodWarning.tsx": "150", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\WebhookWaitingIndicator.tsx": "151", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\context\\SubscriptionProcessingContext.tsx": "152", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionHandler.ts": "153", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionLogic.ts": "154", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useUrlParameterHandler.ts": "155", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\page.tsx": "156", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\PlanPageWrapper.tsx": "157", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts": "158", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts": "159", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts": "160", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts": "161", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts": "162", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts": "163", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts": "164", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts": "165", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts": "166", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts": "167", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts": "168", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts": "169", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts": "170", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts": "171", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts": "172", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx": "173", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx": "174", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx": "175", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts": "176", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts": "177", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx": "178", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts": "179", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx": "180", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx": "181", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx": "182", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx": "183", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx": "184", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx": "185", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx": "186", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx": "187", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx": "188", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx": "189", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx": "190", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx": "191", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx": "192", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx": "193", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx": "194", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx": "195", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx": "196", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx": "197", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx": "198", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx": "199", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx": "200", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx": "201", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx": "202", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx": "203", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts": "204", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\actions.ts": "205", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx": "206", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx": "207", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx": "208", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx": "209", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts": "210", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx": "211", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx": "212", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx": "213", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx": "214", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx": "215", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx": "216", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx": "217", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts": "218", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts": "219", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx": "220", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx": "221", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx": "222", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx": "223", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx": "224", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx": "225", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx": "226", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts": "227", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx": "228", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx": "229", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx": "230", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx": "231", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx": "232", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts": "233", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts": "234", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx": "235", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx": "236", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx": "237", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx": "238", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts": "239", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx": "240", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx": "241", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx": "242", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "243", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts": "244", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx": "245", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx": "246", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\actions.ts": "247", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx": "248", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx": "249", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx": "250", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx": "251", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx": "252", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts": "253", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx": "254", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx": "255", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx": "256", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx": "257", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx": "258", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx": "259", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx": "260", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts": "261", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx": "262", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts": "263", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx": "264", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx": "265", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx": "266", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx": "267", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx": "268", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx": "269", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx": "270", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx": "271", "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx": "272", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx": "273", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx": "274", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx": "275", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx": "276", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx": "277", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx": "278", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx": "279", "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx": "280", "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts": "281", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx": "282", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx": "283", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx": "284", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx": "285", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx": "286", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx": "287", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx": "288", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx": "289", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx": "290", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts": "291", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx": "292", "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx": "293", "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx": "294", "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts": "295", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx": "296", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx": "297", "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx": "298", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx": "299", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx": "300", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx": "301", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx": "302", "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx": "303", "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx": "304", "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx": "305", "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx": "306", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx": "307", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts": "308", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\BillingToggle.tsx": "309", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx": "310", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx": "311", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx": "312", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx": "313", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx": "314", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx": "315", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx": "316", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx": "317", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts": "318", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx": "319", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx": "320", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx": "321", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx": "322", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx": "323", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx": "324", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx": "325", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx": "326", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx": "327", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx": "328", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx": "329", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PricingSection.tsx": "330", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx": "331", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx": "332", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx": "333", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx": "334", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx": "335", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts": "336", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx": "337", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx": "338", "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx": "339", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx": "340", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx": "341", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx": "342", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx": "343", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx": "344", "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx": "345", "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\FeatureComparisonTable.tsx": "346", "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx": "347", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx": "348", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx": "349", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx": "350", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx": "351", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx": "352", "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx": "353", "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx": "354", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx": "355", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx": "356", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts": "357", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts": "358", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts": "359", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts": "360", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts": "361", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts": "362", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx": "363", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx": "364", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx": "365", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx": "366", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx": "367", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx": "368", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx": "369", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx": "370", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx": "371", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx": "372", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx": "373", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx": "374", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx": "375", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx": "376", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx": "377", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx": "378", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx": "379", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx": "380", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx": "381", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx": "382", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx": "383", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx": "384", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx": "385", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx": "386", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx": "387", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx": "388", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx": "389", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx": "390", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx": "391", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx": "392", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx": "393", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx": "394", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx": "395", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx": "396", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts": "397", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts": "398", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts": "399", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts": "400", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx": "401", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts": "402", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts": "403", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx": "404", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx": "405", "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx": "406", "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts": "407", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx": "408", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx": "409", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx": "410", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx": "411", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx": "412", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx": "413", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\PlanComparisonSection.tsx": "414", "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx": "415", "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx": "416", "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts": "417", "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx": "418", "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx": "419", "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts": "420", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx": "421", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx": "422", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx": "423", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx": "424", "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx": "425", "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx": "426", "C:\\web-app\\dukancard\\app\\(main)\\page.tsx": "427", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\AnimatedTitle.tsx": "428", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\PricingAnimatedBackground.tsx": "429", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCards.tsx": "430", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCTA.tsx": "431", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingFAQ.tsx": "432", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingHero.tsx": "433", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingToggle.tsx": "434", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\FeatureComparisonTable.tsx": "435", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\EnhancedPricingPageClient.tsx": "436", "C:\\web-app\\dukancard\\app\\(main)\\pricing\\page.tsx": "437", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx": "438", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx": "439", "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx": "440", "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx": "441", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx": "442", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx": "443", "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\AnalyticsClient.tsx": "444", "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\page.tsx": "445", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx": "446", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx": "447", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx": "448", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx": "449", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx": "450", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx": "451", "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx": "452", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx": "453", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx": "454", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx": "455", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx": "456", "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx": "457", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx": "458", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx": "459", "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx": "460", "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx": "461", "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx": "462", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts": "463", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx": "464", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx": "465", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx": "466", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx": "467", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx": "468", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx": "469", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\PlanSelectionStep.tsx": "470", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts": "471", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts": "472", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts": "473", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts": "474", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts": "475", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts": "476", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx": "477", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx": "478", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx": "479", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts": "480", "C:\\web-app\\dukancard\\app\\api\\admin\\fix-subscription-inconsistency\\route.ts": "481", "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts": "482", "C:\\web-app\\dukancard\\app\\api\\health\\subscription\\route.ts": "483", "C:\\web-app\\dukancard\\app\\api\\razorpay\\key\\route.ts": "484", "C:\\web-app\\dukancard\\app\\api\\subscription\\[id]\\payments\\route.ts": "485", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\centralized\\route.ts": "486", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\list\\route.ts": "487", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\my\\route.ts": "488", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\cancel\\route.ts": "489", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\invoices\\route.ts": "490", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pause\\route.ts": "491", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\route.ts": "492", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\[paymentId]\\route.ts": "493", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pending-update\\route.ts": "494", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\databaseOperations.ts": "495", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\errorHandlers.ts": "496", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\index.ts": "497", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\paymentHelpers.ts": "498", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\razorpayApi.ts": "499", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\types.ts": "500", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\validators.ts": "501", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\resume\\route.ts": "502", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\route.ts": "503", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\scheduled-changes\\route.ts": "504", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch\\route.ts": "505", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch-with-new-payment\\route.ts": "506", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\update\\route.ts": "507", "C:\\web-app\\dukancard\\app\\api\\test\\subscription-flow\\route.ts": "508", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\create\\route.ts": "509", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\delete\\route.ts": "510", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\get\\route.ts": "511", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\list\\route.ts": "512", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\retry\\route.ts": "513", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\route.ts": "514", "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\update\\route.ts": "515", "C:\\web-app\\dukancard\\app\\auth\\actions.ts": "516", "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts": "517", "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx": "518", "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx": "519", "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx": "520", "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx": "521", "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx": "522", "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx": "523", "C:\\web-app\\dukancard\\app\\components\\Footer.tsx": "524", "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx": "525", "C:\\web-app\\dukancard\\app\\components\\Header.tsx": "526", "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx": "527", "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx": "528", "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx": "529", "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx": "530", "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx": "531", "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx": "532", "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx": "533", "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx": "534", "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx": "535", "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx": "536", "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx": "537", "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx": "538", "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx": "539", "C:\\web-app\\dukancard\\app\\components\\PricingCard.tsx": "540", "C:\\web-app\\dukancard\\app\\components\\PricingCardContext.tsx": "541", "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx": "542", "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx": "543", "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx": "544", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx": "545", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx": "546", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx": "547", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx": "548", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx": "549", "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx": "550", "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx": "551", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts": "552", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx": "553", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx": "554", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx": "555", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx": "556", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx": "557", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx": "558", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx": "559", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx": "560", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts": "561", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx": "562", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx": "563", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx": "564", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx": "565", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx": "566", "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx": "567", "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx": "568", "C:\\web-app\\dukancard\\app\\context\\PaymentMethodLimitationsContext.tsx": "569", "C:\\web-app\\dukancard\\app\\layout.tsx": "570", "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts": "571", "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts": "572", "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts": "573", "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts": "574", "C:\\web-app\\dukancard\\app\\locality\\actions.ts": "575", "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx": "576", "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx": "577", "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx": "578", "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx": "579", "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx": "580", "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts": "581", "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts": "582", "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts": "583", "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx": "584", "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts": "585", "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts": "586", "C:\\web-app\\dukancard\\app\\locality\\layout.tsx": "587", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx": "588", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx": "589", "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx": "590", "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx": "591", "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx": "592", "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx": "593", "C:\\web-app\\dukancard\\app\\products\\sitemap.ts": "594", "C:\\web-app\\dukancard\\app\\robots.ts": "595", "C:\\web-app\\dukancard\\app\\sitemap.ts": "596", "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts": "597", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts": "598", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx": "599", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx": "600", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx": "601", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx": "602", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx": "603", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx": "604", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx": "605", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx": "606", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx": "607", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx": "608", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx": "609", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx": "610", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\VisitTracker.tsx": "611", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx": "612", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx": "613", "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx": "614", "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx": "615", "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx": "616", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts": "617", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx": "618", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx": "619", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx": "620", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx": "621", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx": "622", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx": "623", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx": "624", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx": "625", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx": "626", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts": "627", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx": "628", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx": "629", "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx": "630", "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx": "631", "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx": "632", "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx": "633", "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx": "634", "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx": "635", "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx": "636", "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx": "637", "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx": "638", "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx": "639", "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx": "640", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx": "641", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx": "642", "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx": "643", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx": "644", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx": "645", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx": "646", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts": "647", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts": "648", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts": "649", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx": "650", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx": "651", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx": "652", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx": "653", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx": "654", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx": "655", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx": "656", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx": "657", "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx": "658", "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx": "659", "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx": "660", "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx": "661", "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx": "662", "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx": "663", "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx": "664", "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx": "665", "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx": "666", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx": "667", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx": "668", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx": "669", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx": "670", "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx": "671", "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx": "672", "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx": "673", "C:\\web-app\\dukancard\\components\\ui\\alert.tsx": "674", "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx": "675", "C:\\web-app\\dukancard\\components\\ui\\badge.tsx": "676", "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx": "677", "C:\\web-app\\dukancard\\components\\ui\\button.tsx": "678", "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx": "679", "C:\\web-app\\dukancard\\components\\ui\\card.tsx": "680", "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx": "681", "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx": "682", "C:\\web-app\\dukancard\\components\\ui\\chart.tsx": "683", "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx": "684", "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx": "685", "C:\\web-app\\dukancard\\components\\ui\\command.tsx": "686", "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx": "687", "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx": "688", "C:\\web-app\\dukancard\\components\\ui\\form.tsx": "689", "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx": "690", "C:\\web-app\\dukancard\\components\\ui\\input.tsx": "691", "C:\\web-app\\dukancard\\components\\ui\\label.tsx": "692", "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx": "693", "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx": "694", "C:\\web-app\\dukancard\\components\\ui\\popover.tsx": "695", "C:\\web-app\\dukancard\\components\\ui\\progress.tsx": "696", "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx": "697", "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx": "698", "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx": "699", "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx": "700", "C:\\web-app\\dukancard\\components\\ui\\select.tsx": "701", "C:\\web-app\\dukancard\\components\\ui\\separator.tsx": "702", "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx": "703", "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx": "704", "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx": "705", "C:\\web-app\\dukancard\\components\\ui\\slider.tsx": "706", "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx": "707", "C:\\web-app\\dukancard\\components\\ui\\switch.tsx": "708", "C:\\web-app\\dukancard\\components\\ui\\table.tsx": "709", "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx": "710", "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx": "711", "C:\\web-app\\dukancard\\components\\ui\\toast.tsx": "712", "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx": "713", "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts": "714", "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx": "715", "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts": "716", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts": "717", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts": "718", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts": "719", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts": "720", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts": "721", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts": "722", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts": "723", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts": "724", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts": "725", "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts": "726", "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts": "727", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts": "728", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts": "729", "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts": "730", "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts": "731", "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts": "732", "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts": "733", "C:\\web-app\\dukancard\\lib\\actions\\location.ts": "734", "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts": "735", "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts": "736", "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts": "737", "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts": "738", "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts": "739", "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts": "740", "C:\\web-app\\dukancard\\lib\\actions\\posts.ts": "741", "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts": "742", "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts": "743", "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts": "744", "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts": "745", "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts": "746", "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts": "747", "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts": "748", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts": "749", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts": "750", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts": "751", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\activateTrial.ts": "752", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\centralized.ts": "753", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\confirm.ts": "754", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\create.ts": "755", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\index.ts": "756", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\cancel.ts": "757", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\change.ts": "758", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\index.ts": "759", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\manage.ts": "760", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\schedule.ts": "761", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\switch.ts": "762", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage.ts": "763", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\payment.ts": "764", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\status.ts": "765", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\types.ts": "766", "C:\\web-app\\dukancard\\lib\\actions\\subscription\\utils.ts": "767", "C:\\web-app\\dukancard\\lib\\actions\\subscription.ts": "768", "C:\\web-app\\dukancard\\lib\\actions\\user\\getUserAndProfile.ts": "769", "C:\\web-app\\dukancard\\lib\\api\\response.ts": "770", "C:\\web-app\\dukancard\\lib\\cardDownloader.ts": "771", "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts": "772", "C:\\web-app\\dukancard\\lib\\config\\categories.ts": "773", "C:\\web-app\\dukancard\\lib\\config\\plans.ts": "774", "C:\\web-app\\dukancard\\lib\\config\\states.ts": "775", "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts": "776", "C:\\web-app\\dukancard\\lib\\csrf.ts": "777", "C:\\web-app\\dukancard\\lib\\errorHandling.ts": "778", "C:\\web-app\\dukancard\\lib\\PricingPlans.ts": "779", "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts": "780", "C:\\web-app\\dukancard\\lib\\rateLimiter.ts": "781", "C:\\web-app\\dukancard\\lib\\razorpay\\razorpayClient.ts": "782", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\create.ts": "783", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\get.ts": "784", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\index.ts": "785", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\types.ts": "786", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\update.ts": "787", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\invoice.ts": "788", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\getPayment.ts": "789", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\index.ts": "790", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\types.ts": "791", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment.ts": "792", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\plan.ts": "793", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\cancel.ts": "794", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\create.ts": "795", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\get.ts": "796", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\index.ts": "797", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\scheduled.ts": "798", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\types.ts": "799", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\update.ts": "800", "C:\\web-app\\dukancard\\lib\\razorpay\\services\\webhook.ts": "801", "C:\\web-app\\dukancard\\lib\\razorpay\\types\\api.ts": "802", "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\auth.ts": "803", "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\loadRazorpaySDK.ts": "804", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\errorTracking.ts": "805", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\eventManager.ts": "806", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\subscriptionManager.ts": "807", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\types.ts": "808", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\eventOrderValidator.ts": "809", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\stateTransitionValidator.ts": "810", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\index.ts": "811", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\mainHandler.ts": "812", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\moreSubscriptionHandlers.ts": "813", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\paymentHandlers.ts": "814", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\refundHandlers.ts": "815", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-constants.ts": "816", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-db-updater.ts": "817", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-manager.ts": "818", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-validator.ts": "819", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionActivated.ts": "820", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionAuthenticated.ts": "821", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCancelled.ts": "822", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCharged.ts": "823", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCompleted.ts": "824", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionExpired.ts": "825", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionHalted.ts": "826", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionPending.ts": "827", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionUpdated.ts": "828", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\index.ts": "829", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionHandlers.ts": "830", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\transactionUtils.ts": "831", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\utils.ts": "832", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhook-utils.ts": "833", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhookProcessor.ts": "834", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handleWebhook.ts": "835", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\idempotency.ts": "836", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\monitoring.ts": "837", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\types.ts": "838", "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\validation.ts": "839", "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts": "840", "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts": "841", "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts": "842", "C:\\web-app\\dukancard\\lib\\services\\socialService.ts": "843", "C:\\web-app\\dukancard\\lib\\services\\subscription.ts": "844", "C:\\web-app\\dukancard\\lib\\site-config.ts": "845", "C:\\web-app\\dukancard\\lib\\siteContent.ts": "846", "C:\\web-app\\dukancard\\lib\\subscription\\edge-validation.ts": "847", "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowManager.ts": "848", "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowTester.ts": "849", "C:\\web-app\\dukancard\\lib\\subscription\\types.ts": "850", "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts": "851", "C:\\web-app\\dukancard\\lib\\testing\\database.ts": "852", "C:\\web-app\\dukancard\\lib\\testing\\mswHandlers.ts": "853", "C:\\web-app\\dukancard\\lib\\testing\\testDataFactories.ts": "854", "C:\\web-app\\dukancard\\lib\\testing\\testUtils.ts": "855", "C:\\web-app\\dukancard\\lib\\testing\\types.ts": "856", "C:\\web-app\\dukancard\\lib\\types\\activities.ts": "857", "C:\\web-app\\dukancard\\lib\\types\\api.ts": "858", "C:\\web-app\\dukancard\\lib\\types\\blog.ts": "859", "C:\\web-app\\dukancard\\lib\\types\\posts.ts": "860", "C:\\web-app\\dukancard\\lib\\types\\subscription.ts": "861", "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts": "862", "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts": "863", "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts": "864", "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts": "865", "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts": "866", "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts": "867", "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts": "868", "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts": "869", "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "870", "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts": "871", "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts": "872", "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts": "873", "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts": "874", "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts": "875", "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts": "876", "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts": "877", "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts": "878", "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts": "879", "C:\\web-app\\dukancard\\lib\\utils\\seo.ts": "880", "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts": "881", "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts": "882", "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts": "883", "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts": "884", "C:\\web-app\\dukancard\\lib\\utils.ts": "885"}, {"size": 3778, "mtime": 1753771415050, "results": "886", "hashOfConfig": "887"}, {"size": 11790, "mtime": 1753771415049, "results": "888", "hashOfConfig": "887"}, {"size": 4230, "mtime": 1753771415051, "results": "889", "hashOfConfig": "887"}, {"size": 2328, "mtime": 1753771415052, "results": "890", "hashOfConfig": "887"}, {"size": 21331, "mtime": 1753771415053, "results": "891", "hashOfConfig": "887"}, {"size": 8688, "mtime": 1753771415054, "results": "892", "hashOfConfig": "887"}, {"size": 15287, "mtime": 1753771415055, "results": "893", "hashOfConfig": "887"}, {"size": 2217, "mtime": 1753769462718, "results": "894", "hashOfConfig": "887"}, {"size": 3625, "mtime": 1753771415056, "results": "895", "hashOfConfig": "887"}, {"size": 5348, "mtime": 1753771415058, "results": "896", "hashOfConfig": "887"}, {"size": 4425, "mtime": 1753771415058, "results": "897", "hashOfConfig": "887"}, {"size": 7274, "mtime": 1753771415059, "results": "898", "hashOfConfig": "887"}, {"size": 9690, "mtime": 1753771415060, "results": "899", "hashOfConfig": "887"}, {"size": 1912, "mtime": 1753769462722, "results": "900", "hashOfConfig": "887"}, {"size": 2679, "mtime": 1753769462723, "results": "901", "hashOfConfig": "887"}, {"size": 9656, "mtime": 1753771415062, "results": "902", "hashOfConfig": "887"}, {"size": 6750, "mtime": 1753771415062, "results": "903", "hashOfConfig": "887"}, {"size": 7333, "mtime": 1753771415063, "results": "904", "hashOfConfig": "887"}, {"size": 8003, "mtime": 1753771415064, "results": "905", "hashOfConfig": "887"}, {"size": 273, "mtime": 1753769462726, "results": "906", "hashOfConfig": "887"}, {"size": 3199, "mtime": 1753771415065, "results": "907", "hashOfConfig": "887"}, {"size": 10302, "mtime": 1753771415066, "results": "908", "hashOfConfig": "887"}, {"size": 22168, "mtime": 1753769462724, "results": "909", "hashOfConfig": "887"}, {"size": 20755, "mtime": 1753769462733, "results": "910", "hashOfConfig": "887"}, {"size": 2220, "mtime": 1753769462734, "results": "911", "hashOfConfig": "887"}, {"size": 10914, "mtime": 1753769462735, "results": "912", "hashOfConfig": "887"}, {"size": 1630, "mtime": 1753769462736, "results": "913", "hashOfConfig": "887"}, {"size": 721, "mtime": 1753769462736, "results": "914", "hashOfConfig": "887"}, {"size": 8255, "mtime": 1753769462738, "results": "915", "hashOfConfig": "887"}, {"size": 21052, "mtime": 1753769462738, "results": "916", "hashOfConfig": "887"}, {"size": 4489, "mtime": 1753769462740, "results": "917", "hashOfConfig": "887"}, {"size": 13138, "mtime": 1753769462741, "results": "918", "hashOfConfig": "887"}, {"size": 2814, "mtime": 1753769462741, "results": "919", "hashOfConfig": "887"}, {"size": 14802, "mtime": 1753771415067, "results": "920", "hashOfConfig": "887"}, {"size": 15549, "mtime": 1753769462743, "results": "921", "hashOfConfig": "887"}, {"size": 13946, "mtime": 1753769462744, "results": "922", "hashOfConfig": "887"}, {"size": 1088, "mtime": 1753769462748, "results": "923", "hashOfConfig": "887"}, {"size": 2708, "mtime": 1753769462745, "results": "924", "hashOfConfig": "887"}, {"size": 9768, "mtime": 1753769462746, "results": "925", "hashOfConfig": "887"}, {"size": 23953, "mtime": 1753769462746, "results": "926", "hashOfConfig": "887"}, {"size": 5243, "mtime": 1753769462747, "results": "927", "hashOfConfig": "887"}, {"size": 3361, "mtime": 1753769462748, "results": "928", "hashOfConfig": "887"}, {"size": 3637, "mtime": 1753769462749, "results": "929", "hashOfConfig": "887"}, {"size": 6688, "mtime": 1753769462750, "results": "930", "hashOfConfig": "887"}, {"size": 2107, "mtime": 1753769462752, "results": "931", "hashOfConfig": "887"}, {"size": 3868, "mtime": 1753769462751, "results": "932", "hashOfConfig": "887"}, {"size": 4167, "mtime": 1753769462752, "results": "933", "hashOfConfig": "887"}, {"size": 3004, "mtime": 1753769462753, "results": "934", "hashOfConfig": "887"}, {"size": 6921, "mtime": 1753769462754, "results": "935", "hashOfConfig": "887"}, {"size": 8503, "mtime": 1753769462755, "results": "936", "hashOfConfig": "887"}, {"size": 9562, "mtime": 1753769462759, "results": "937", "hashOfConfig": "887"}, {"size": 4802, "mtime": 1753769462759, "results": "938", "hashOfConfig": "887"}, {"size": 1794, "mtime": 1753769462760, "results": "939", "hashOfConfig": "887"}, {"size": 5533, "mtime": 1753769462756, "results": "940", "hashOfConfig": "887"}, {"size": 2140, "mtime": 1753769462757, "results": "941", "hashOfConfig": "887"}, {"size": 5309, "mtime": 1753769462758, "results": "942", "hashOfConfig": "887"}, {"size": 3245, "mtime": 1753769462761, "results": "943", "hashOfConfig": "887"}, {"size": 8242, "mtime": 1753771415068, "results": "944", "hashOfConfig": "887"}, {"size": 2762, "mtime": 1753769462762, "results": "945", "hashOfConfig": "887"}, {"size": 7583, "mtime": 1753771415069, "results": "946", "hashOfConfig": "887"}, {"size": 2031, "mtime": 1753771415070, "results": "947", "hashOfConfig": "887"}, {"size": 1907, "mtime": 1753771415090, "results": "948", "hashOfConfig": "887"}, {"size": 9011, "mtime": 1753771415093, "results": "949", "hashOfConfig": "887"}, {"size": 2435, "mtime": 1753769462766, "results": "950", "hashOfConfig": "887"}, {"size": 1001, "mtime": 1753769462767, "results": "951", "hashOfConfig": "887"}, {"size": 559, "mtime": 1753771415098, "results": "952", "hashOfConfig": "887"}, {"size": 2573, "mtime": 1753769462768, "results": "953", "hashOfConfig": "887"}, {"size": 404, "mtime": 1753769462769, "results": "954", "hashOfConfig": "887"}, {"size": 1825, "mtime": 1753769462770, "results": "955", "hashOfConfig": "887"}, {"size": 4687, "mtime": 1753771415101, "results": "956", "hashOfConfig": "887"}, {"size": 12464, "mtime": 1753771415104, "results": "957", "hashOfConfig": "887"}, {"size": 6082, "mtime": 1753771415110, "results": "958", "hashOfConfig": "887"}, {"size": 2522, "mtime": 1753771415111, "results": "959", "hashOfConfig": "887"}, {"size": 2722, "mtime": 1753771415112, "results": "960", "hashOfConfig": "887"}, {"size": 6550, "mtime": 1753771415113, "results": "961", "hashOfConfig": "887"}, {"size": 5057, "mtime": 1753771415114, "results": "962", "hashOfConfig": "887"}, {"size": 6735, "mtime": 1753769462777, "results": "963", "hashOfConfig": "887"}, {"size": 8561, "mtime": 1753769462778, "results": "964", "hashOfConfig": "887"}, {"size": 10556, "mtime": 1753771415116, "results": "965", "hashOfConfig": "887"}, {"size": 1993, "mtime": 1753769462781, "results": "966", "hashOfConfig": "887"}, {"size": 3314, "mtime": 1753771415117, "results": "967", "hashOfConfig": "887"}, {"size": 6883, "mtime": 1753771415118, "results": "968", "hashOfConfig": "887"}, {"size": 2180, "mtime": 1753771415120, "results": "969", "hashOfConfig": "887"}, {"size": 1799, "mtime": 1753769462784, "results": "970", "hashOfConfig": "887"}, {"size": 2794, "mtime": 1753769462785, "results": "971", "hashOfConfig": "887"}, {"size": 3181, "mtime": 1753771415121, "results": "972", "hashOfConfig": "887"}, {"size": 2096, "mtime": 1753771415122, "results": "973", "hashOfConfig": "887"}, {"size": 2833, "mtime": 1753769462787, "results": "974", "hashOfConfig": "887"}, {"size": 6615, "mtime": 1753771415122, "results": "975", "hashOfConfig": "887"}, {"size": 8681, "mtime": 1753771415115, "results": "976", "hashOfConfig": "887"}, {"size": 2305, "mtime": 1753769462789, "results": "977", "hashOfConfig": "887"}, {"size": 2046, "mtime": 1753769462789, "results": "978", "hashOfConfig": "887"}, {"size": 3165, "mtime": 1753769462790, "results": "979", "hashOfConfig": "887"}, {"size": 2135, "mtime": 1753771415123, "results": "980", "hashOfConfig": "887"}, {"size": 767, "mtime": 1753769462793, "results": "981", "hashOfConfig": "887"}, {"size": 555, "mtime": 1753771415124, "results": "982", "hashOfConfig": "887"}, {"size": 1142, "mtime": 1753769462794, "results": "983", "hashOfConfig": "887"}, {"size": 1089, "mtime": 1753769462793, "results": "984", "hashOfConfig": "887"}, {"size": 3242, "mtime": 1753769462795, "results": "985", "hashOfConfig": "887"}, {"size": 2011, "mtime": 1753771415125, "results": "986", "hashOfConfig": "887"}, {"size": 10378, "mtime": 1753771415126, "results": "987", "hashOfConfig": "887"}, {"size": 4483, "mtime": 1753771415127, "results": "988", "hashOfConfig": "887"}, {"size": 3675, "mtime": 1753771415127, "results": "989", "hashOfConfig": "887"}, {"size": 4030, "mtime": 1753769462796, "results": "990", "hashOfConfig": "887"}, {"size": 1698, "mtime": 1753769462796, "results": "991", "hashOfConfig": "887"}, {"size": 3845, "mtime": 1753771415128, "results": "992", "hashOfConfig": "887"}, {"size": 5729, "mtime": 1753769462802, "results": "993", "hashOfConfig": "887"}, {"size": 2348, "mtime": 1753771415129, "results": "994", "hashOfConfig": "887"}, {"size": 1439, "mtime": 1753769462833, "results": "995", "hashOfConfig": "887"}, {"size": 2558, "mtime": 1753769462804, "results": "996", "hashOfConfig": "887"}, {"size": 4637, "mtime": 1753769462805, "results": "997", "hashOfConfig": "887"}, {"size": 2863, "mtime": 1753769462806, "results": "998", "hashOfConfig": "887"}, {"size": 3966, "mtime": 1753769462807, "results": "999", "hashOfConfig": "887"}, {"size": 3136, "mtime": 1753769462808, "results": "1000", "hashOfConfig": "887"}, {"size": 4782, "mtime": 1753769462808, "results": "1001", "hashOfConfig": "887"}, {"size": 8607, "mtime": 1753769462809, "results": "1002", "hashOfConfig": "887"}, {"size": 4656, "mtime": 1753769462810, "results": "1003", "hashOfConfig": "887"}, {"size": 23325, "mtime": 1753769462811, "results": "1004", "hashOfConfig": "887"}, {"size": 1229, "mtime": 1753769462812, "results": "1005", "hashOfConfig": "887"}, {"size": 6213, "mtime": 1753769462812, "results": "1006", "hashOfConfig": "887"}, {"size": 15610, "mtime": 1753769462813, "results": "1007", "hashOfConfig": "887"}, {"size": 3571, "mtime": 1753769462814, "results": "1008", "hashOfConfig": "887"}, {"size": 10880, "mtime": 1753769462815, "results": "1009", "hashOfConfig": "887"}, {"size": 2393, "mtime": 1753769462818, "results": "1010", "hashOfConfig": "887"}, {"size": 5434, "mtime": 1753769462819, "results": "1011", "hashOfConfig": "887"}, {"size": 9221, "mtime": 1753769462820, "results": "1012", "hashOfConfig": "887"}, {"size": 6955, "mtime": 1753769462822, "results": "1013", "hashOfConfig": "887"}, {"size": 21017, "mtime": 1753769462823, "results": "1014", "hashOfConfig": "887"}, {"size": 1762, "mtime": 1753769462834, "results": "1015", "hashOfConfig": "887"}, {"size": 8288, "mtime": 1753769462835, "results": "1016", "hashOfConfig": "887"}, {"size": 16465, "mtime": 1753769462835, "results": "1017", "hashOfConfig": "887"}, {"size": 12988, "mtime": 1753769462836, "results": "1018", "hashOfConfig": "887"}, {"size": 2432, "mtime": 1753769462844, "results": "1019", "hashOfConfig": "887"}, {"size": 770, "mtime": 1753769462845, "results": "1020", "hashOfConfig": "887"}, {"size": 12287, "mtime": 1753769462837, "results": "1021", "hashOfConfig": "887"}, {"size": 8702, "mtime": 1753769462838, "results": "1022", "hashOfConfig": "887"}, {"size": 27687, "mtime": 1753769462839, "results": "1023", "hashOfConfig": "887"}, {"size": 6021, "mtime": 1753769462840, "results": "1024", "hashOfConfig": "887"}, {"size": 5206, "mtime": 1753769462840, "results": "1025", "hashOfConfig": "887"}, {"size": 3546, "mtime": 1753769462841, "results": "1026", "hashOfConfig": "887"}, {"size": 14866, "mtime": 1753769462842, "results": "1027", "hashOfConfig": "887"}, {"size": 7461, "mtime": 1753769462843, "results": "1028", "hashOfConfig": "887"}, {"size": 2679, "mtime": 1753769462843, "results": "1029", "hashOfConfig": "887"}, {"size": 3078, "mtime": 1753769462845, "results": "1030", "hashOfConfig": "887"}, {"size": 8231, "mtime": 1753769462824, "results": "1031", "hashOfConfig": "887"}, {"size": 14159, "mtime": 1753769462824, "results": "1032", "hashOfConfig": "887"}, {"size": 4456, "mtime": 1753769462827, "results": "1033", "hashOfConfig": "887"}, {"size": 3114, "mtime": 1753769462827, "results": "1034", "hashOfConfig": "887"}, {"size": 4235, "mtime": 1753769462829, "results": "1035", "hashOfConfig": "887"}, {"size": 6620, "mtime": 1753769462830, "results": "1036", "hashOfConfig": "887"}, {"size": 776, "mtime": 1753769462832, "results": "1037", "hashOfConfig": "887"}, {"size": 11893, "mtime": 1753769462846, "results": "1038", "hashOfConfig": "887"}, {"size": 11640, "mtime": 1753769462847, "results": "1039", "hashOfConfig": "887"}, {"size": 6587, "mtime": 1753769462848, "results": "1040", "hashOfConfig": "887"}, {"size": 3524, "mtime": 1753769462848, "results": "1041", "hashOfConfig": "887"}, {"size": 12782, "mtime": 1753771415130, "results": "1042", "hashOfConfig": "887"}, {"size": 1654, "mtime": 1753769462803, "results": "1043", "hashOfConfig": "887"}, {"size": 7929, "mtime": 1753771415132, "results": "1044", "hashOfConfig": "887"}, {"size": 12970, "mtime": 1753771415132, "results": "1045", "hashOfConfig": "887"}, {"size": 8366, "mtime": 1753771415133, "results": "1046", "hashOfConfig": "887"}, {"size": 5098, "mtime": 1753771415134, "results": "1047", "hashOfConfig": "887"}, {"size": 15095, "mtime": 1753771415135, "results": "1048", "hashOfConfig": "887"}, {"size": 5428, "mtime": 1753771415137, "results": "1049", "hashOfConfig": "887"}, {"size": 10699, "mtime": 1753771415136, "results": "1050", "hashOfConfig": "887"}, {"size": 7346, "mtime": 1753771415138, "results": "1051", "hashOfConfig": "887"}, {"size": 9106, "mtime": 1753771415138, "results": "1052", "hashOfConfig": "887"}, {"size": 774, "mtime": 1753769462858, "results": "1053", "hashOfConfig": "887"}, {"size": 5527, "mtime": 1753771415158, "results": "1054", "hashOfConfig": "887"}, {"size": 585, "mtime": 1753771415159, "results": "1055", "hashOfConfig": "887"}, {"size": 8360, "mtime": 1753771415160, "results": "1056", "hashOfConfig": "887"}, {"size": 12397, "mtime": 1753771415161, "results": "1057", "hashOfConfig": "887"}, {"size": 99, "mtime": 1753769462851, "results": "1058", "hashOfConfig": "887"}, {"size": 10730, "mtime": 1753771415162, "results": "1059", "hashOfConfig": "887"}, {"size": 2570, "mtime": 1753769462863, "results": "1060", "hashOfConfig": "887"}, {"size": 16581, "mtime": 1753771415163, "results": "1061", "hashOfConfig": "887"}, {"size": 6920, "mtime": 1753769462872, "results": "1062", "hashOfConfig": "887"}, {"size": 8800, "mtime": 1753769462873, "results": "1063", "hashOfConfig": "887"}, {"size": 7997, "mtime": 1753769462865, "results": "1064", "hashOfConfig": "887"}, {"size": 743, "mtime": 1753771415175, "results": "1065", "hashOfConfig": "887"}, {"size": 2089, "mtime": 1753769462874, "results": "1066", "hashOfConfig": "887"}, {"size": 2029, "mtime": 1753771415168, "results": "1067", "hashOfConfig": "887"}, {"size": 5665, "mtime": 1753771415169, "results": "1068", "hashOfConfig": "887"}, {"size": 9160, "mtime": 1753771415170, "results": "1069", "hashOfConfig": "887"}, {"size": 2212, "mtime": 1753771415170, "results": "1070", "hashOfConfig": "887"}, {"size": 2762, "mtime": 1753771415171, "results": "1071", "hashOfConfig": "887"}, {"size": 4770, "mtime": 1753771415172, "results": "1072", "hashOfConfig": "887"}, {"size": 7051, "mtime": 1753771415172, "results": "1073", "hashOfConfig": "887"}, {"size": 5095, "mtime": 1753771415173, "results": "1074", "hashOfConfig": "887"}, {"size": 18791, "mtime": 1753771415174, "results": "1075", "hashOfConfig": "887"}, {"size": 4427, "mtime": 1753771415175, "results": "1076", "hashOfConfig": "887"}, {"size": 5264, "mtime": 1753769462865, "results": "1077", "hashOfConfig": "887"}, {"size": 16573, "mtime": 1753769462866, "results": "1078", "hashOfConfig": "887"}, {"size": 1339, "mtime": 1753769462867, "results": "1079", "hashOfConfig": "887"}, {"size": 30215, "mtime": 1753771415165, "results": "1080", "hashOfConfig": "887"}, {"size": 17692, "mtime": 1753769462868, "results": "1081", "hashOfConfig": "887"}, {"size": 31435, "mtime": 1753771415166, "results": "1082", "hashOfConfig": "887"}, {"size": 25674, "mtime": 1753771415167, "results": "1083", "hashOfConfig": "887"}, {"size": 10885, "mtime": 1753769462871, "results": "1084", "hashOfConfig": "887"}, {"size": 7850, "mtime": 1753771415176, "results": "1085", "hashOfConfig": "887"}, {"size": 12815, "mtime": 1753771415177, "results": "1086", "hashOfConfig": "887"}, {"size": 4213, "mtime": 1753771415178, "results": "1087", "hashOfConfig": "887"}, {"size": 1796, "mtime": 1753769462885, "results": "1088", "hashOfConfig": "887"}, {"size": 3085, "mtime": 1753771415131, "results": "1089", "hashOfConfig": "887"}, {"size": 2564, "mtime": 1753771415178, "results": "1090", "hashOfConfig": "887"}, {"size": 1114, "mtime": 1753771415179, "results": "1091", "hashOfConfig": "887"}, {"size": 10681, "mtime": 1753771415180, "results": "1092", "hashOfConfig": "887"}, {"size": 12362, "mtime": 1753771415181, "results": "1093", "hashOfConfig": "887"}, {"size": 5373, "mtime": 1753771415182, "results": "1094", "hashOfConfig": "887"}, {"size": 2075, "mtime": 1753771415182, "results": "1095", "hashOfConfig": "887"}, {"size": 26734, "mtime": 1753771415184, "results": "1096", "hashOfConfig": "887"}, {"size": 23396, "mtime": 1753771415185, "results": "1097", "hashOfConfig": "887"}, {"size": 1822, "mtime": 1753771415186, "results": "1098", "hashOfConfig": "887"}, {"size": 16031, "mtime": 1753771415187, "results": "1099", "hashOfConfig": "887"}, {"size": 4171, "mtime": 1753771415188, "results": "1100", "hashOfConfig": "887"}, {"size": 3182, "mtime": 1753771415188, "results": "1101", "hashOfConfig": "887"}, {"size": 4559, "mtime": 1753771415189, "results": "1102", "hashOfConfig": "887"}, {"size": 2104, "mtime": 1753769462898, "results": "1103", "hashOfConfig": "887"}, {"size": 547, "mtime": 1753771415190, "results": "1104", "hashOfConfig": "887"}, {"size": 3191, "mtime": 1753771415191, "results": "1105", "hashOfConfig": "887"}, {"size": 11679, "mtime": 1753771415192, "results": "1106", "hashOfConfig": "887"}, {"size": 4330, "mtime": 1753769462901, "results": "1107", "hashOfConfig": "887"}, {"size": 2300, "mtime": 1753769462903, "results": "1108", "hashOfConfig": "887"}, {"size": 1807, "mtime": 1753769462904, "results": "1109", "hashOfConfig": "887"}, {"size": 2559, "mtime": 1753769462905, "results": "1110", "hashOfConfig": "887"}, {"size": 2308, "mtime": 1753771415192, "results": "1111", "hashOfConfig": "887"}, {"size": 2136, "mtime": 1753769462906, "results": "1112", "hashOfConfig": "887"}, {"size": 612, "mtime": 1753771415218, "results": "1113", "hashOfConfig": "887"}, {"size": 6530, "mtime": 1753769462909, "results": "1114", "hashOfConfig": "887"}, {"size": 1372, "mtime": 1753771415217, "results": "1115", "hashOfConfig": "887"}, {"size": 3511, "mtime": 1753769462910, "results": "1116", "hashOfConfig": "887"}, {"size": 2499, "mtime": 1753769462912, "results": "1117", "hashOfConfig": "887"}, {"size": 2793, "mtime": 1753769462912, "results": "1118", "hashOfConfig": "887"}, {"size": 18035, "mtime": 1753771415220, "results": "1119", "hashOfConfig": "887"}, {"size": 6372, "mtime": 1753769462915, "results": "1120", "hashOfConfig": "887"}, {"size": 12766, "mtime": 1753771415221, "results": "1121", "hashOfConfig": "887"}, {"size": 1426, "mtime": 1753771415222, "results": "1122", "hashOfConfig": "887"}, {"size": 5647, "mtime": 1753771415223, "results": "1123", "hashOfConfig": "887"}, {"size": 7356, "mtime": 1753769462919, "results": "1124", "hashOfConfig": "887"}, {"size": 4583, "mtime": 1753769462923, "results": "1125", "hashOfConfig": "887"}, {"size": 6598, "mtime": 1753769462919, "results": "1126", "hashOfConfig": "887"}, {"size": 5978, "mtime": 1753769462920, "results": "1127", "hashOfConfig": "887"}, {"size": 13921, "mtime": 1753771415224, "results": "1128", "hashOfConfig": "887"}, {"size": 6909, "mtime": 1753769462922, "results": "1129", "hashOfConfig": "887"}, {"size": 7677, "mtime": 1753769462924, "results": "1130", "hashOfConfig": "887"}, {"size": 2322, "mtime": 1753769462925, "results": "1131", "hashOfConfig": "887"}, {"size": 7412, "mtime": 1753771415219, "results": "1132", "hashOfConfig": "887"}, {"size": 576, "mtime": 1753771415226, "results": "1133", "hashOfConfig": "887"}, {"size": 10872, "mtime": 1753771415227, "results": "1134", "hashOfConfig": "887"}, {"size": 733, "mtime": 1753769462927, "results": "1135", "hashOfConfig": "887"}, {"size": 1203, "mtime": 1753769462928, "results": "1136", "hashOfConfig": "887"}, {"size": 1827, "mtime": 1753769462929, "results": "1137", "hashOfConfig": "887"}, {"size": 2950, "mtime": 1753771415225, "results": "1138", "hashOfConfig": "887"}, {"size": 17615, "mtime": 1753771415231, "results": "1139", "hashOfConfig": "887"}, {"size": 16038, "mtime": 1753771415232, "results": "1140", "hashOfConfig": "887"}, {"size": 4194, "mtime": 1753771415233, "results": "1141", "hashOfConfig": "887"}, {"size": 2929, "mtime": 1753771415234, "results": "1142", "hashOfConfig": "887"}, {"size": 4305, "mtime": 1753771415235, "results": "1143", "hashOfConfig": "887"}, {"size": 5476, "mtime": 1753771415228, "results": "1144", "hashOfConfig": "887"}, {"size": 23276, "mtime": 1753771415229, "results": "1145", "hashOfConfig": "887"}, {"size": 1602, "mtime": 1753769462940, "results": "1146", "hashOfConfig": "887"}, {"size": 1380, "mtime": 1753771415236, "results": "1147", "hashOfConfig": "887"}, {"size": 3464, "mtime": 1753771415230, "results": "1148", "hashOfConfig": "887"}, {"size": 684, "mtime": 1753771415237, "results": "1149", "hashOfConfig": "887"}, {"size": 2007, "mtime": 1753769462944, "results": "1150", "hashOfConfig": "887"}, {"size": 3579, "mtime": 1753769462945, "results": "1151", "hashOfConfig": "887"}, {"size": 1181, "mtime": 1753769462947, "results": "1152", "hashOfConfig": "887"}, {"size": 2665, "mtime": 1753769462946, "results": "1153", "hashOfConfig": "887"}, {"size": 6679, "mtime": 1753771415238, "results": "1154", "hashOfConfig": "887"}, {"size": 3744, "mtime": 1753769462948, "results": "1155", "hashOfConfig": "887"}, {"size": 6038, "mtime": 1753769462941, "results": "1156", "hashOfConfig": "887"}, {"size": 1270, "mtime": 1753771415237, "results": "1157", "hashOfConfig": "887"}, {"size": 2007, "mtime": 1753769462951, "results": "1158", "hashOfConfig": "887"}, {"size": 5704, "mtime": 1753769462952, "results": "1159", "hashOfConfig": "887"}, {"size": 4915, "mtime": 1753769462953, "results": "1160", "hashOfConfig": "887"}, {"size": 4615, "mtime": 1753769462957, "results": "1161", "hashOfConfig": "887"}, {"size": 5211, "mtime": 1753769462954, "results": "1162", "hashOfConfig": "887"}, {"size": 5725, "mtime": 1753769462954, "results": "1163", "hashOfConfig": "887"}, {"size": 4799, "mtime": 1753769462955, "results": "1164", "hashOfConfig": "887"}, {"size": 7774, "mtime": 1753769462956, "results": "1165", "hashOfConfig": "887"}, {"size": 2147, "mtime": 1753769462958, "results": "1166", "hashOfConfig": "887"}, {"size": 5677, "mtime": 1753771415240, "results": "1167", "hashOfConfig": "887"}, {"size": 737, "mtime": 1753769462960, "results": "1168", "hashOfConfig": "887"}, {"size": 5712, "mtime": 1753769462961, "results": "1169", "hashOfConfig": "887"}, {"size": 8862, "mtime": 1753769462962, "results": "1170", "hashOfConfig": "887"}, {"size": 3996, "mtime": 1753769462962, "results": "1171", "hashOfConfig": "887"}, {"size": 6753, "mtime": 1753769462963, "results": "1172", "hashOfConfig": "887"}, {"size": 6562, "mtime": 1753769462964, "results": "1173", "hashOfConfig": "887"}, {"size": 1779, "mtime": 1753769462965, "results": "1174", "hashOfConfig": "887"}, {"size": 9940, "mtime": 1753771415241, "results": "1175", "hashOfConfig": "887"}, {"size": 161, "mtime": 1753769462967, "results": "1176", "hashOfConfig": "887"}, {"size": 189, "mtime": 1753769462968, "results": "1177", "hashOfConfig": "887"}, {"size": 565, "mtime": 1753769462969, "results": "1178", "hashOfConfig": "887"}, {"size": 8610, "mtime": 1753769462973, "results": "1179", "hashOfConfig": "887"}, {"size": 1870, "mtime": 1753769462974, "results": "1180", "hashOfConfig": "887"}, {"size": 2516, "mtime": 1753769462975, "results": "1181", "hashOfConfig": "887"}, {"size": 14853, "mtime": 1753771415243, "results": "1182", "hashOfConfig": "887"}, {"size": 4727, "mtime": 1753771415244, "results": "1183", "hashOfConfig": "887"}, {"size": 2890, "mtime": 1753769462976, "results": "1184", "hashOfConfig": "887"}, {"size": 1924, "mtime": 1753769462982, "results": "1185", "hashOfConfig": "887"}, {"size": 2239, "mtime": 1753769462983, "results": "1186", "hashOfConfig": "887"}, {"size": 1646, "mtime": 1753769462983, "results": "1187", "hashOfConfig": "887"}, {"size": 5334, "mtime": 1753769462984, "results": "1188", "hashOfConfig": "887"}, {"size": 1048, "mtime": 1753769462976, "results": "1189", "hashOfConfig": "887"}, {"size": 3451, "mtime": 1753769462977, "results": "1190", "hashOfConfig": "887"}, {"size": 5179, "mtime": 1753769462978, "results": "1191", "hashOfConfig": "887"}, {"size": 4986, "mtime": 1753769462979, "results": "1192", "hashOfConfig": "887"}, {"size": 4326, "mtime": 1753769462985, "results": "1193", "hashOfConfig": "887"}, {"size": 411, "mtime": 1753769463006, "results": "1194", "hashOfConfig": "887"}, {"size": 2337, "mtime": 1753769462986, "results": "1195", "hashOfConfig": "887"}, {"size": 4822, "mtime": 1753769462987, "results": "1196", "hashOfConfig": "887"}, {"size": 4706, "mtime": 1753769462987, "results": "1197", "hashOfConfig": "887"}, {"size": 4957, "mtime": 1753769462988, "results": "1198", "hashOfConfig": "887"}, {"size": 3841, "mtime": 1753769462988, "results": "1199", "hashOfConfig": "887"}, {"size": 18438, "mtime": 1753769462989, "results": "1200", "hashOfConfig": "887"}, {"size": 1266, "mtime": 1753769462990, "results": "1201", "hashOfConfig": "887"}, {"size": 8177, "mtime": 1753769462991, "results": "1202", "hashOfConfig": "887"}, {"size": 1581, "mtime": 1753769462991, "results": "1203", "hashOfConfig": "887"}, {"size": 1343, "mtime": 1753771415245, "results": "1204", "hashOfConfig": "887"}, {"size": 1799, "mtime": 1753769462992, "results": "1205", "hashOfConfig": "887"}, {"size": 6966, "mtime": 1753769462993, "results": "1206", "hashOfConfig": "887"}, {"size": 295, "mtime": 1753769462994, "results": "1207", "hashOfConfig": "887"}, {"size": 3916, "mtime": 1753769462994, "results": "1208", "hashOfConfig": "887"}, {"size": 5877, "mtime": 1753769462995, "results": "1209", "hashOfConfig": "887"}, {"size": 18346, "mtime": 1753769462996, "results": "1210", "hashOfConfig": "887"}, {"size": 897, "mtime": 1753769462997, "results": "1211", "hashOfConfig": "887"}, {"size": 3936, "mtime": 1753769462998, "results": "1212", "hashOfConfig": "887"}, {"size": 23617, "mtime": 1753769462999, "results": "1213", "hashOfConfig": "887"}, {"size": 3305, "mtime": 1753769463000, "results": "1214", "hashOfConfig": "887"}, {"size": 3489, "mtime": 1753769463000, "results": "1215", "hashOfConfig": "887"}, {"size": 5456, "mtime": 1753769463001, "results": "1216", "hashOfConfig": "887"}, {"size": 1739, "mtime": 1753769463001, "results": "1217", "hashOfConfig": "887"}, {"size": 6529, "mtime": 1753769463002, "results": "1218", "hashOfConfig": "887"}, {"size": 3222, "mtime": 1753769463003, "results": "1219", "hashOfConfig": "887"}, {"size": 5762, "mtime": 1753769463004, "results": "1220", "hashOfConfig": "887"}, {"size": 3984, "mtime": 1753769463005, "results": "1221", "hashOfConfig": "887"}, {"size": 134, "mtime": 1753769463009, "results": "1222", "hashOfConfig": "887"}, {"size": 848, "mtime": 1753769463008, "results": "1223", "hashOfConfig": "887"}, {"size": 2993, "mtime": 1753769463008, "results": "1224", "hashOfConfig": "887"}, {"size": 5827, "mtime": 1753769462980, "results": "1225", "hashOfConfig": "887"}, {"size": 4054, "mtime": 1753769463010, "results": "1226", "hashOfConfig": "887"}, {"size": 5287, "mtime": 1753769463011, "results": "1227", "hashOfConfig": "887"}, {"size": 3121, "mtime": 1753769463011, "results": "1228", "hashOfConfig": "887"}, {"size": 3264, "mtime": 1753769463012, "results": "1229", "hashOfConfig": "887"}, {"size": 2619, "mtime": 1753769463013, "results": "1230", "hashOfConfig": "887"}, {"size": 3681, "mtime": 1753769462981, "results": "1231", "hashOfConfig": "887"}, {"size": 13610, "mtime": 1753769463014, "results": "1232", "hashOfConfig": "887"}, {"size": 5286, "mtime": 1753771415246, "results": "1233", "hashOfConfig": "887"}, {"size": 4433, "mtime": 1753769463022, "results": "1234", "hashOfConfig": "887"}, {"size": 5822, "mtime": 1753769463018, "results": "1235", "hashOfConfig": "887"}, {"size": 5776, "mtime": 1753769463019, "results": "1236", "hashOfConfig": "887"}, {"size": 13788, "mtime": 1753769463020, "results": "1237", "hashOfConfig": "887"}, {"size": 9337, "mtime": 1753769463021, "results": "1238", "hashOfConfig": "887"}, {"size": 3219, "mtime": 1753769463016, "results": "1239", "hashOfConfig": "887"}, {"size": 3306, "mtime": 1753769463023, "results": "1240", "hashOfConfig": "887"}, {"size": 14293, "mtime": 1753769463024, "results": "1241", "hashOfConfig": "887"}, {"size": 827, "mtime": 1753769463025, "results": "1242", "hashOfConfig": "887"}, {"size": 5416, "mtime": 1753771415247, "results": "1243", "hashOfConfig": "887"}, {"size": 6730, "mtime": 1753771415248, "results": "1244", "hashOfConfig": "887"}, {"size": 20267, "mtime": 1753771415249, "results": "1245", "hashOfConfig": "887"}, {"size": 14049, "mtime": 1753771415250, "results": "1246", "hashOfConfig": "887"}, {"size": 2735, "mtime": 1753769463032, "results": "1247", "hashOfConfig": "887"}, {"size": 925, "mtime": 1753769463028, "results": "1248", "hashOfConfig": "887"}, {"size": 1213, "mtime": 1753769463034, "results": "1249", "hashOfConfig": "887"}, {"size": 8134, "mtime": 1753769463035, "results": "1250", "hashOfConfig": "887"}, {"size": 957, "mtime": 1753769463036, "results": "1251", "hashOfConfig": "887"}, {"size": 2264, "mtime": 1753769463037, "results": "1252", "hashOfConfig": "887"}, {"size": 1677, "mtime": 1753769463037, "results": "1253", "hashOfConfig": "887"}, {"size": 1034, "mtime": 1753769463038, "results": "1254", "hashOfConfig": "887"}, {"size": 5544, "mtime": 1753769463039, "results": "1255", "hashOfConfig": "887"}, {"size": 2483, "mtime": 1753769463040, "results": "1256", "hashOfConfig": "887"}, {"size": 1092, "mtime": 1753769463041, "results": "1257", "hashOfConfig": "887"}, {"size": 4532, "mtime": 1753769463042, "results": "1258", "hashOfConfig": "887"}, {"size": 6920, "mtime": 1753771415251, "results": "1259", "hashOfConfig": "887"}, {"size": 3574, "mtime": 1753771415261, "results": "1260", "hashOfConfig": "887"}, {"size": 794, "mtime": 1753769463043, "results": "1261", "hashOfConfig": "887"}, {"size": 1902, "mtime": 1753769463044, "results": "1262", "hashOfConfig": "887"}, {"size": 1420, "mtime": 1753769463045, "results": "1263", "hashOfConfig": "887"}, {"size": 24194, "mtime": 1753771415262, "results": "1264", "hashOfConfig": "887"}, {"size": 555, "mtime": 1753769463046, "results": "1265", "hashOfConfig": "887"}, {"size": 4100, "mtime": 1753769463047, "results": "1266", "hashOfConfig": "887"}, {"size": 15578, "mtime": 1753769463048, "results": "1267", "hashOfConfig": "887"}, {"size": 3228, "mtime": 1753769463048, "results": "1268", "hashOfConfig": "887"}, {"size": 3514, "mtime": 1753769463049, "results": "1269", "hashOfConfig": "887"}, {"size": 23175, "mtime": 1753769463050, "results": "1270", "hashOfConfig": "887"}, {"size": 2060, "mtime": 1753769463051, "results": "1271", "hashOfConfig": "887"}, {"size": 16492, "mtime": 1753769463052, "results": "1272", "hashOfConfig": "887"}, {"size": 1149, "mtime": 1753769463052, "results": "1273", "hashOfConfig": "887"}, {"size": 3631, "mtime": 1753769463053, "results": "1274", "hashOfConfig": "887"}, {"size": 1859, "mtime": 1753769463054, "results": "1275", "hashOfConfig": "887"}, {"size": 4207, "mtime": 1753769463055, "results": "1276", "hashOfConfig": "887"}, {"size": 5060, "mtime": 1753769463056, "results": "1277", "hashOfConfig": "887"}, {"size": 3993, "mtime": 1753769463056, "results": "1278", "hashOfConfig": "887"}, {"size": 3872, "mtime": 1753769463057, "results": "1279", "hashOfConfig": "887"}, {"size": 1420, "mtime": 1753769463058, "results": "1280", "hashOfConfig": "887"}, {"size": 4730, "mtime": 1753769463059, "results": "1281", "hashOfConfig": "887"}, {"size": 5956, "mtime": 1753769463059, "results": "1282", "hashOfConfig": "887"}, {"size": 244, "mtime": 1753769463060, "results": "1283", "hashOfConfig": "887"}, {"size": 671, "mtime": 1753771415263, "results": "1284", "hashOfConfig": "887"}, {"size": 9220, "mtime": 1753769463063, "results": "1285", "hashOfConfig": "887"}, {"size": 9703, "mtime": 1753771415265, "results": "1286", "hashOfConfig": "887"}, {"size": 10536, "mtime": 1753771415264, "results": "1287", "hashOfConfig": "887"}, {"size": 12559, "mtime": 1753769463066, "results": "1288", "hashOfConfig": "887"}, {"size": 2402, "mtime": 1753771415266, "results": "1289", "hashOfConfig": "887"}, {"size": 1517, "mtime": 1753769463026, "results": "1290", "hashOfConfig": "887"}, {"size": 1951, "mtime": 1753769463027, "results": "1291", "hashOfConfig": "887"}, {"size": 4017, "mtime": 1753769463067, "results": "1292", "hashOfConfig": "887"}, {"size": 3456, "mtime": 1753769463068, "results": "1293", "hashOfConfig": "887"}, {"size": 4833, "mtime": 1753769463070, "results": "1294", "hashOfConfig": "887"}, {"size": 3938, "mtime": 1753769463071, "results": "1295", "hashOfConfig": "887"}, {"size": 5522, "mtime": 1753769463072, "results": "1296", "hashOfConfig": "887"}, {"size": 5183, "mtime": 1753769463072, "results": "1297", "hashOfConfig": "887"}, {"size": 7170, "mtime": 1753769463073, "results": "1298", "hashOfConfig": "887"}, {"size": 8695, "mtime": 1753769463074, "results": "1299", "hashOfConfig": "887"}, {"size": 1127, "mtime": 1753769463075, "results": "1300", "hashOfConfig": "887"}, {"size": 1607, "mtime": 1753769463069, "results": "1301", "hashOfConfig": "887"}, {"size": 2053, "mtime": 1753769463076, "results": "1302", "hashOfConfig": "887"}, {"size": 1135, "mtime": 1753769463077, "results": "1303", "hashOfConfig": "887"}, {"size": 9275, "mtime": 1753771415240, "results": "1304", "hashOfConfig": "887"}, {"size": 1655, "mtime": 1753769463078, "results": "1305", "hashOfConfig": "887"}, {"size": 4612, "mtime": 1753771415268, "results": "1306", "hashOfConfig": "887"}, {"size": 1388, "mtime": 1753769463081, "results": "1307", "hashOfConfig": "887"}, {"size": 8381, "mtime": 1753771415269, "results": "1308", "hashOfConfig": "887"}, {"size": 4902, "mtime": 1753771415270, "results": "1309", "hashOfConfig": "887"}, {"size": 5040, "mtime": 1753769463084, "results": "1310", "hashOfConfig": "887"}, {"size": 10831, "mtime": 1753771415267, "results": "1311", "hashOfConfig": "887"}, {"size": 1427, "mtime": 1753771415270, "results": "1312", "hashOfConfig": "887"}, {"size": 3766, "mtime": 1753769463086, "results": "1313", "hashOfConfig": "887"}, {"size": 3999, "mtime": 1753769463094, "results": "1314", "hashOfConfig": "887"}, {"size": 8192, "mtime": 1753769463095, "results": "1315", "hashOfConfig": "887"}, {"size": 4650, "mtime": 1753769463089, "results": "1316", "hashOfConfig": "887"}, {"size": 6600, "mtime": 1753769463088, "results": "1317", "hashOfConfig": "887"}, {"size": 5535, "mtime": 1753769463090, "results": "1318", "hashOfConfig": "887"}, {"size": 6402, "mtime": 1753769463091, "results": "1319", "hashOfConfig": "887"}, {"size": 6618, "mtime": 1753769463092, "results": "1320", "hashOfConfig": "887"}, {"size": 9406, "mtime": 1753769463093, "results": "1321", "hashOfConfig": "887"}, {"size": 4798, "mtime": 1753769463087, "results": "1322", "hashOfConfig": "887"}, {"size": 2182, "mtime": 1753769463095, "results": "1323", "hashOfConfig": "887"}, {"size": 14232, "mtime": 1753769463096, "results": "1324", "hashOfConfig": "887"}, {"size": 1626, "mtime": 1753769463097, "results": "1325", "hashOfConfig": "887"}, {"size": 14197, "mtime": 1753769463099, "results": "1326", "hashOfConfig": "887"}, {"size": 820, "mtime": 1753769463099, "results": "1327", "hashOfConfig": "887"}, {"size": 15316, "mtime": 1753769463104, "results": "1328", "hashOfConfig": "887"}, {"size": 1887, "mtime": 1753769463105, "results": "1329", "hashOfConfig": "887"}, {"size": 13686, "mtime": 1753769463106, "results": "1330", "hashOfConfig": "887"}, {"size": 1905, "mtime": 1753769463107, "results": "1331", "hashOfConfig": "887"}, {"size": 12380, "mtime": 1753769463109, "results": "1332", "hashOfConfig": "887"}, {"size": 1946, "mtime": 1753769463110, "results": "1333", "hashOfConfig": "887"}, {"size": 3999, "mtime": 1753769463111, "results": "1334", "hashOfConfig": "887"}, {"size": 6385, "mtime": 1753769463112, "results": "1335", "hashOfConfig": "887"}, {"size": 9551, "mtime": 1753769463113, "results": "1336", "hashOfConfig": "887"}, {"size": 13651, "mtime": 1753769463114, "results": "1337", "hashOfConfig": "887"}, {"size": 1826, "mtime": 1753769463115, "results": "1338", "hashOfConfig": "887"}, {"size": 1920, "mtime": 1753769463117, "results": "1339", "hashOfConfig": "887"}, {"size": 13936, "mtime": 1753769463116, "results": "1340", "hashOfConfig": "887"}, {"size": 1862, "mtime": 1753769463119, "results": "1341", "hashOfConfig": "887"}, {"size": 13015, "mtime": 1753769463118, "results": "1342", "hashOfConfig": "887"}, {"size": 14045, "mtime": 1753771415272, "results": "1343", "hashOfConfig": "887"}, {"size": 1899, "mtime": 1753769463121, "results": "1344", "hashOfConfig": "887"}, {"size": 11444, "mtime": 1753769463120, "results": "1345", "hashOfConfig": "887"}, {"size": 15279, "mtime": 1753769463122, "results": "1346", "hashOfConfig": "887"}, {"size": 852, "mtime": 1753769463123, "results": "1347", "hashOfConfig": "887"}, {"size": 1676, "mtime": 1753769463125, "results": "1348", "hashOfConfig": "887"}, {"size": 15760, "mtime": 1753771415273, "results": "1349", "hashOfConfig": "887"}, {"size": 759, "mtime": 1753771415274, "results": "1350", "hashOfConfig": "887"}, {"size": 4845, "mtime": 1753769463130, "results": "1351", "hashOfConfig": "887"}, {"size": 2787, "mtime": 1753771415275, "results": "1352", "hashOfConfig": "887"}, {"size": 9997, "mtime": 1753771415276, "results": "1353", "hashOfConfig": "887"}, {"size": 3634, "mtime": 1753769463133, "results": "1354", "hashOfConfig": "887"}, {"size": 10759, "mtime": 1753769463134, "results": "1355", "hashOfConfig": "887"}, {"size": 8359, "mtime": 1753771415276, "results": "1356", "hashOfConfig": "887"}, {"size": 995, "mtime": 1753769463137, "results": "1357", "hashOfConfig": "887"}, {"size": 3185, "mtime": 1753769463138, "results": "1358", "hashOfConfig": "887"}, {"size": 7318, "mtime": 1753769463139, "results": "1359", "hashOfConfig": "887"}, {"size": 4953, "mtime": 1753771415277, "results": "1360", "hashOfConfig": "887"}, {"size": 2913, "mtime": 1753769463140, "results": "1361", "hashOfConfig": "887"}, {"size": 1680, "mtime": 1753771415278, "results": "1362", "hashOfConfig": "887"}, {"size": 586, "mtime": 1753769463141, "results": "1363", "hashOfConfig": "887"}, {"size": 6995, "mtime": 1753769463126, "results": "1364", "hashOfConfig": "887"}, {"size": 1230, "mtime": 1753769463142, "results": "1365", "hashOfConfig": "887"}, {"size": 3561, "mtime": 1753769463143, "results": "1366", "hashOfConfig": "887"}, {"size": 9259, "mtime": 1753771415287, "results": "1367", "hashOfConfig": "887"}, {"size": 1417, "mtime": 1753771415287, "results": "1368", "hashOfConfig": "887"}, {"size": 7758, "mtime": 1753771415288, "results": "1369", "hashOfConfig": "887"}, {"size": 1485, "mtime": 1753769463187, "results": "1370", "hashOfConfig": "887"}, {"size": 5200, "mtime": 1753771415289, "results": "1371", "hashOfConfig": "887"}, {"size": 1954, "mtime": 1753769463214, "results": "1372", "hashOfConfig": "887"}, {"size": 4168, "mtime": 1753769463215, "results": "1373", "hashOfConfig": "887"}, {"size": 4294, "mtime": 1753771415296, "results": "1374", "hashOfConfig": "887"}, {"size": 8563, "mtime": 1753771415290, "results": "1375", "hashOfConfig": "887"}, {"size": 6840, "mtime": 1753771415291, "results": "1376", "hashOfConfig": "887"}, {"size": 6408, "mtime": 1753771415292, "results": "1377", "hashOfConfig": "887"}, {"size": 4277, "mtime": 1753769463197, "results": "1378", "hashOfConfig": "887"}, {"size": 3045, "mtime": 1753769463196, "results": "1379", "hashOfConfig": "887"}, {"size": 4253, "mtime": 1753771415292, "results": "1380", "hashOfConfig": "887"}, {"size": 5980, "mtime": 1753769463200, "results": "1381", "hashOfConfig": "887"}, {"size": 2041, "mtime": 1753769463201, "results": "1382", "hashOfConfig": "887"}, {"size": 228, "mtime": 1753769463202, "results": "1383", "hashOfConfig": "887"}, {"size": 7263, "mtime": 1753769463203, "results": "1384", "hashOfConfig": "887"}, {"size": 7147, "mtime": 1753769463204, "results": "1385", "hashOfConfig": "887"}, {"size": 846, "mtime": 1753769463205, "results": "1386", "hashOfConfig": "887"}, {"size": 3451, "mtime": 1753769463205, "results": "1387", "hashOfConfig": "887"}, {"size": 6345, "mtime": 1753771415293, "results": "1388", "hashOfConfig": "887"}, {"size": 5098, "mtime": 1753771415294, "results": "1389", "hashOfConfig": "887"}, {"size": 7441, "mtime": 1753769463208, "results": "1390", "hashOfConfig": "887"}, {"size": 4187, "mtime": 1753771415295, "results": "1391", "hashOfConfig": "887"}, {"size": 3193, "mtime": 1753769463210, "results": "1392", "hashOfConfig": "887"}, {"size": 9321, "mtime": 1753771415295, "results": "1393", "hashOfConfig": "887"}, {"size": 3720, "mtime": 1753769463218, "results": "1394", "hashOfConfig": "887"}, {"size": 3246, "mtime": 1753769463221, "results": "1395", "hashOfConfig": "887"}, {"size": 1961, "mtime": 1753769463222, "results": "1396", "hashOfConfig": "887"}, {"size": 2418, "mtime": 1753769463223, "results": "1397", "hashOfConfig": "887"}, {"size": 3537, "mtime": 1753769463224, "results": "1398", "hashOfConfig": "887"}, {"size": 5109, "mtime": 1753771415297, "results": "1399", "hashOfConfig": "887"}, {"size": 9454, "mtime": 1753769463226, "results": "1400", "hashOfConfig": "887"}, {"size": 2906, "mtime": 1753769463227, "results": "1401", "hashOfConfig": "887"}, {"size": 1215, "mtime": 1753769463228, "results": "1402", "hashOfConfig": "887"}, {"size": 2508, "mtime": 1753769463230, "results": "1403", "hashOfConfig": "887"}, {"size": 852, "mtime": 1753769463264, "results": "1404", "hashOfConfig": "887"}, {"size": 4790, "mtime": 1753769463264, "results": "1405", "hashOfConfig": "887"}, {"size": 8785, "mtime": 1753771415298, "results": "1406", "hashOfConfig": "887"}, {"size": 696, "mtime": 1753769463266, "results": "1407", "hashOfConfig": "887"}, {"size": 3247, "mtime": 1753769463267, "results": "1408", "hashOfConfig": "887"}, {"size": 16852, "mtime": 1753769463267, "results": "1409", "hashOfConfig": "887"}, {"size": 6060, "mtime": 1753769463268, "results": "1410", "hashOfConfig": "887"}, {"size": 605, "mtime": 1753769463269, "results": "1411", "hashOfConfig": "887"}, {"size": 9700, "mtime": 1753771415299, "results": "1412", "hashOfConfig": "887"}, {"size": 706, "mtime": 1753769463279, "results": "1413", "hashOfConfig": "887"}, {"size": 1299, "mtime": 1753769463281, "results": "1414", "hashOfConfig": "887"}, {"size": 918, "mtime": 1753769463281, "results": "1415", "hashOfConfig": "887"}, {"size": 1155, "mtime": 1753769463282, "results": "1416", "hashOfConfig": "887"}, {"size": 1050, "mtime": 1753769463283, "results": "1417", "hashOfConfig": "887"}, {"size": 573, "mtime": 1753769463284, "results": "1418", "hashOfConfig": "887"}, {"size": 1580, "mtime": 1753769463285, "results": "1419", "hashOfConfig": "887"}, {"size": 766, "mtime": 1753769463285, "results": "1420", "hashOfConfig": "887"}, {"size": 3070, "mtime": 1753769463270, "results": "1421", "hashOfConfig": "887"}, {"size": 1187, "mtime": 1753769463271, "results": "1422", "hashOfConfig": "887"}, {"size": 560, "mtime": 1753769463272, "results": "1423", "hashOfConfig": "887"}, {"size": 10200, "mtime": 1753771415299, "results": "1424", "hashOfConfig": "887"}, {"size": 387, "mtime": 1753769463274, "results": "1425", "hashOfConfig": "887"}, {"size": 13488, "mtime": 1753769463275, "results": "1426", "hashOfConfig": "887"}, {"size": 1007, "mtime": 1753769463275, "results": "1427", "hashOfConfig": "887"}, {"size": 10905, "mtime": 1753769463276, "results": "1428", "hashOfConfig": "887"}, {"size": 1695, "mtime": 1753769463277, "results": "1429", "hashOfConfig": "887"}, {"size": 928, "mtime": 1753769463286, "results": "1430", "hashOfConfig": "887"}, {"size": 11881, "mtime": 1753769463287, "results": "1431", "hashOfConfig": "887"}, {"size": 5051, "mtime": 1753769463288, "results": "1432", "hashOfConfig": "887"}, {"size": 12521, "mtime": 1753769463289, "results": "1433", "hashOfConfig": "887"}, {"size": 8293, "mtime": 1753769463290, "results": "1434", "hashOfConfig": "887"}, {"size": 8100, "mtime": 1753769463291, "results": "1435", "hashOfConfig": "887"}, {"size": 468, "mtime": 1753769463277, "results": "1436", "hashOfConfig": "887"}, {"size": 8404, "mtime": 1753769463292, "results": "1437", "hashOfConfig": "887"}, {"size": 420, "mtime": 1753769463297, "results": "1438", "hashOfConfig": "887"}, {"size": 8986, "mtime": 1753771415300, "results": "1439", "hashOfConfig": "887"}, {"size": 2536, "mtime": 1753769463294, "results": "1440", "hashOfConfig": "887"}, {"size": 3620, "mtime": 1753771415301, "results": "1441", "hashOfConfig": "887"}, {"size": 2764, "mtime": 1753769463295, "results": "1442", "hashOfConfig": "887"}, {"size": 2336, "mtime": 1753769463296, "results": "1443", "hashOfConfig": "887"}, {"size": 15343, "mtime": 1753769463298, "results": "1444", "hashOfConfig": "887"}, {"size": 2399, "mtime": 1753769463298, "results": "1445", "hashOfConfig": "887"}, {"size": 2537, "mtime": 1753771415302, "results": "1446", "hashOfConfig": "887"}, {"size": 532, "mtime": 1753769463304, "results": "1447", "hashOfConfig": "887"}, {"size": 7698, "mtime": 1753771415302, "results": "1448", "hashOfConfig": "887"}, {"size": 2870, "mtime": 1753771415303, "results": "1449", "hashOfConfig": "887"}, {"size": 3578, "mtime": 1753771415304, "results": "1450", "hashOfConfig": "887"}, {"size": 3654, "mtime": 1753769463302, "results": "1451", "hashOfConfig": "887"}, {"size": 2711, "mtime": 1753769463303, "results": "1452", "hashOfConfig": "887"}, {"size": 5073, "mtime": 1753769463278, "results": "1453", "hashOfConfig": "887"}, {"size": 468, "mtime": 1753769463305, "results": "1454", "hashOfConfig": "887"}, {"size": 2384, "mtime": 1753769463309, "results": "1455", "hashOfConfig": "887"}, {"size": 2875, "mtime": 1753769463311, "results": "1456", "hashOfConfig": "887"}, {"size": 7053, "mtime": 1753771415305, "results": "1457", "hashOfConfig": "887"}, {"size": 3476, "mtime": 1753769463316, "results": "1458", "hashOfConfig": "887"}, {"size": 2968, "mtime": 1753769463317, "results": "1459", "hashOfConfig": "887"}, {"size": 7056, "mtime": 1753771415321, "results": "1460", "hashOfConfig": "887"}, {"size": 543, "mtime": 1753769463314, "results": "1461", "hashOfConfig": "887"}, {"size": 881, "mtime": 1753769463319, "results": "1462", "hashOfConfig": "887"}, {"size": 563, "mtime": 1753769463319, "results": "1463", "hashOfConfig": "887"}, {"size": 5611, "mtime": 1753769463320, "results": "1464", "hashOfConfig": "887"}, {"size": 2417, "mtime": 1753769463321, "results": "1465", "hashOfConfig": "887"}, {"size": 2958, "mtime": 1753769463322, "results": "1466", "hashOfConfig": "887"}, {"size": 140, "mtime": 1753769463323, "results": "1467", "hashOfConfig": "887"}, {"size": 5043, "mtime": 1753769463325, "results": "1468", "hashOfConfig": "887"}, {"size": 3818, "mtime": 1753769463326, "results": "1469", "hashOfConfig": "887"}, {"size": 6246, "mtime": 1753769463324, "results": "1470", "hashOfConfig": "887"}, {"size": 6801, "mtime": 1753769463327, "results": "1471", "hashOfConfig": "887"}, {"size": 2232, "mtime": 1753769463328, "results": "1472", "hashOfConfig": "887"}, {"size": 1235, "mtime": 1753769463328, "results": "1473", "hashOfConfig": "887"}, {"size": 2119, "mtime": 1753769463312, "results": "1474", "hashOfConfig": "887"}, {"size": 3747, "mtime": 1753769463313, "results": "1475", "hashOfConfig": "887"}, {"size": 2394, "mtime": 1753769463331, "results": "1476", "hashOfConfig": "887"}, {"size": 431, "mtime": 1753769463331, "results": "1477", "hashOfConfig": "887"}, {"size": 1606, "mtime": 1753769463332, "results": "1478", "hashOfConfig": "887"}, {"size": 3178, "mtime": 1753769463333, "results": "1479", "hashOfConfig": "887"}, {"size": 2689, "mtime": 1753769463334, "results": "1480", "hashOfConfig": "887"}, {"size": 1735, "mtime": 1753771415322, "results": "1481", "hashOfConfig": "887"}, {"size": 1639, "mtime": 1753771415349, "results": "1482", "hashOfConfig": "887"}, {"size": 8235, "mtime": 1753771415279, "results": "1483", "hashOfConfig": "887"}, {"size": 2860, "mtime": 1753769463158, "results": "1484", "hashOfConfig": "887"}, {"size": 8058, "mtime": 1753769463147, "results": "1485", "hashOfConfig": "887"}, {"size": 19772, "mtime": 1753771415280, "results": "1486", "hashOfConfig": "887"}, {"size": 6916, "mtime": 1753769463149, "results": "1487", "hashOfConfig": "887"}, {"size": 5552, "mtime": 1753769463150, "results": "1488", "hashOfConfig": "887"}, {"size": 2806, "mtime": 1753769463151, "results": "1489", "hashOfConfig": "887"}, {"size": 8383, "mtime": 1753769463152, "results": "1490", "hashOfConfig": "887"}, {"size": 14273, "mtime": 1753769463152, "results": "1491", "hashOfConfig": "887"}, {"size": 6718, "mtime": 1753769463153, "results": "1492", "hashOfConfig": "887"}, {"size": 5950, "mtime": 1753769463154, "results": "1493", "hashOfConfig": "887"}, {"size": 3064, "mtime": 1753769463154, "results": "1494", "hashOfConfig": "887"}, {"size": 1050, "mtime": 1753769463155, "results": "1495", "hashOfConfig": "887"}, {"size": 22169, "mtime": 1753769463156, "results": "1496", "hashOfConfig": "887"}, {"size": 4436, "mtime": 1753769463157, "results": "1497", "hashOfConfig": "887"}, {"size": 9131, "mtime": 1753769463159, "results": "1498", "hashOfConfig": "887"}, {"size": 4551, "mtime": 1753771415281, "results": "1499", "hashOfConfig": "887"}, {"size": 1237, "mtime": 1753769463161, "results": "1500", "hashOfConfig": "887"}, {"size": 456, "mtime": 1753769463162, "results": "1501", "hashOfConfig": "887"}, {"size": 15175, "mtime": 1753771415282, "results": "1502", "hashOfConfig": "887"}, {"size": 15806, "mtime": 1753771415284, "results": "1503", "hashOfConfig": "887"}, {"size": 1555, "mtime": 1753769463167, "results": "1504", "hashOfConfig": "887"}, {"size": 12447, "mtime": 1753769463168, "results": "1505", "hashOfConfig": "887"}, {"size": 3075, "mtime": 1753769463169, "results": "1506", "hashOfConfig": "887"}, {"size": 2658, "mtime": 1753769463169, "results": "1507", "hashOfConfig": "887"}, {"size": 4697, "mtime": 1753769463170, "results": "1508", "hashOfConfig": "887"}, {"size": 22440, "mtime": 1753771415285, "results": "1509", "hashOfConfig": "887"}, {"size": 3730, "mtime": 1753769463172, "results": "1510", "hashOfConfig": "887"}, {"size": 12465, "mtime": 1753771415285, "results": "1511", "hashOfConfig": "887"}, {"size": 3044, "mtime": 1753769463173, "results": "1512", "hashOfConfig": "887"}, {"size": 5097, "mtime": 1753769463174, "results": "1513", "hashOfConfig": "887"}, {"size": 10225, "mtime": 1753771415283, "results": "1514", "hashOfConfig": "887"}, {"size": 2253, "mtime": 1753769463164, "results": "1515", "hashOfConfig": "887"}, {"size": 3263, "mtime": 1753769463144, "results": "1516", "hashOfConfig": "887"}, {"size": 5326, "mtime": 1753771415353, "results": "1517", "hashOfConfig": "887"}, {"size": 5995, "mtime": 1753769463338, "results": "1518", "hashOfConfig": "887"}, {"size": 3946, "mtime": 1753769463338, "results": "1519", "hashOfConfig": "887"}, {"size": 8264, "mtime": 1753771415354, "results": "1520", "hashOfConfig": "887"}, {"size": 3007, "mtime": 1753769463340, "results": "1521", "hashOfConfig": "887"}, {"size": 4189, "mtime": 1753769463340, "results": "1522", "hashOfConfig": "887"}, {"size": 9778, "mtime": 1753769463341, "results": "1523", "hashOfConfig": "887"}, {"size": 10169, "mtime": 1753771415355, "results": "1524", "hashOfConfig": "887"}, {"size": 10217, "mtime": 1753771415356, "results": "1525", "hashOfConfig": "887"}, {"size": 6291, "mtime": 1753769463352, "results": "1526", "hashOfConfig": "887"}, {"size": 7264, "mtime": 1753769463353, "results": "1527", "hashOfConfig": "887"}, {"size": 7194, "mtime": 1753769463354, "results": "1528", "hashOfConfig": "887"}, {"size": 3629, "mtime": 1753771415357, "results": "1529", "hashOfConfig": "887"}, {"size": 8662, "mtime": 1753769463356, "results": "1530", "hashOfConfig": "887"}, {"size": 4435, "mtime": 1753771415364, "results": "1531", "hashOfConfig": "887"}, {"size": 19439, "mtime": 1753769463360, "results": "1532", "hashOfConfig": "887"}, {"size": 7315, "mtime": 1753769463361, "results": "1533", "hashOfConfig": "887"}, {"size": 8073, "mtime": 1753769463362, "results": "1534", "hashOfConfig": "887"}, {"size": 2529, "mtime": 1753775850433, "results": "1535", "hashOfConfig": "887"}, {"size": 8788, "mtime": 1753771415358, "results": "1536", "hashOfConfig": "887"}, {"size": 459, "mtime": 1753771415358, "results": "1537", "hashOfConfig": "887"}, {"size": 919, "mtime": 1753769463347, "results": "1538", "hashOfConfig": "887"}, {"size": 27241, "mtime": 1753776407016, "results": "1539", "hashOfConfig": "887"}, {"size": 3391, "mtime": 1753771415360, "results": "1540", "hashOfConfig": "887"}, {"size": 3447, "mtime": 1753769463350, "results": "1541", "hashOfConfig": "887"}, {"size": 41540, "mtime": 1753771415362, "results": "1542", "hashOfConfig": "887"}, {"size": 18331, "mtime": 1753771415363, "results": "1543", "hashOfConfig": "887"}, {"size": 2446, "mtime": 1753776036500, "results": "1544", "hashOfConfig": "887"}, {"size": 935, "mtime": 1753769463364, "results": "1545", "hashOfConfig": "887"}, {"size": 5951, "mtime": 1753771415365, "results": "1546", "hashOfConfig": "887"}, {"size": 1935, "mtime": 1753769463366, "results": "1547", "hashOfConfig": "887"}, {"size": 625, "mtime": 1753769463366, "results": "1548", "hashOfConfig": "887"}, {"size": 7782, "mtime": 1753771415366, "results": "1549", "hashOfConfig": "887"}, {"size": 8655, "mtime": 1753771415366, "results": "1550", "hashOfConfig": "887"}, {"size": 10022, "mtime": 1753771415368, "results": "1551", "hashOfConfig": "887"}, {"size": 7671, "mtime": 1753769463368, "results": "1552", "hashOfConfig": "887"}, {"size": 5281, "mtime": 1753769463369, "results": "1553", "hashOfConfig": "887"}, {"size": 5670, "mtime": 1753769463370, "results": "1554", "hashOfConfig": "887"}, {"size": 4400, "mtime": 1753769463371, "results": "1555", "hashOfConfig": "887"}, {"size": 5504, "mtime": 1753769463372, "results": "1556", "hashOfConfig": "887"}, {"size": 638, "mtime": 1753769463372, "results": "1557", "hashOfConfig": "887"}, {"size": 2119, "mtime": 1753769463373, "results": "1558", "hashOfConfig": "887"}, {"size": 4021, "mtime": 1753769463374, "results": "1559", "hashOfConfig": "887"}, {"size": 1680, "mtime": 1753769463375, "results": "1560", "hashOfConfig": "887"}, {"size": 1150, "mtime": 1753769463376, "results": "1561", "hashOfConfig": "887"}, {"size": 1677, "mtime": 1753769463377, "results": "1562", "hashOfConfig": "887"}, {"size": 2466, "mtime": 1753769463377, "results": "1563", "hashOfConfig": "887"}, {"size": 2199, "mtime": 1753771415369, "results": "1564", "hashOfConfig": "887"}, {"size": 2995, "mtime": 1753769463379, "results": "1565", "hashOfConfig": "887"}, {"size": 2081, "mtime": 1753769463379, "results": "1566", "hashOfConfig": "887"}, {"size": 5798, "mtime": 1753769463380, "results": "1567", "hashOfConfig": "887"}, {"size": 2814, "mtime": 1753769463381, "results": "1568", "hashOfConfig": "887"}, {"size": 10137, "mtime": 1753769463381, "results": "1569", "hashOfConfig": "887"}, {"size": 1258, "mtime": 1753769463382, "results": "1570", "hashOfConfig": "887"}, {"size": 833, "mtime": 1753769463383, "results": "1571", "hashOfConfig": "887"}, {"size": 4833, "mtime": 1753769463383, "results": "1572", "hashOfConfig": "887"}, {"size": 4119, "mtime": 1753769463384, "results": "1573", "hashOfConfig": "887"}, {"size": 8541, "mtime": 1753769463385, "results": "1574", "hashOfConfig": "887"}, {"size": 3926, "mtime": 1753769463386, "results": "1575", "hashOfConfig": "887"}, {"size": 2331, "mtime": 1753769463386, "results": "1576", "hashOfConfig": "887"}, {"size": 988, "mtime": 1753769463387, "results": "1577", "hashOfConfig": "887"}, {"size": 635, "mtime": 1753769463387, "results": "1578", "hashOfConfig": "887"}, {"size": 6792, "mtime": 1753769463388, "results": "1579", "hashOfConfig": "887"}, {"size": 3090, "mtime": 1753769463389, "results": "1580", "hashOfConfig": "887"}, {"size": 1683, "mtime": 1753769463390, "results": "1581", "hashOfConfig": "887"}, {"size": 771, "mtime": 1753769463390, "results": "1582", "hashOfConfig": "887"}, {"size": 1511, "mtime": 1753769463391, "results": "1583", "hashOfConfig": "887"}, {"size": 8203, "mtime": 1753769463392, "results": "1584", "hashOfConfig": "887"}, {"size": 1703, "mtime": 1753769463393, "results": "1585", "hashOfConfig": "887"}, {"size": 2479, "mtime": 1753769463394, "results": "1586", "hashOfConfig": "887"}, {"size": 6438, "mtime": 1753769463395, "results": "1587", "hashOfConfig": "887"}, {"size": 732, "mtime": 1753769463396, "results": "1588", "hashOfConfig": "887"}, {"size": 4244, "mtime": 1753769463396, "results": "1589", "hashOfConfig": "887"}, {"size": 22737, "mtime": 1753769463397, "results": "1590", "hashOfConfig": "887"}, {"size": 289, "mtime": 1753769463398, "results": "1591", "hashOfConfig": "887"}, {"size": 2064, "mtime": 1753769463399, "results": "1592", "hashOfConfig": "887"}, {"size": 589, "mtime": 1753769463399, "results": "1593", "hashOfConfig": "887"}, {"size": 1208, "mtime": 1753769463400, "results": "1594", "hashOfConfig": "887"}, {"size": 2564, "mtime": 1753769463400, "results": "1595", "hashOfConfig": "887"}, {"size": 2035, "mtime": 1753769463401, "results": "1596", "hashOfConfig": "887"}, {"size": 777, "mtime": 1753769463402, "results": "1597", "hashOfConfig": "887"}, {"size": 3457, "mtime": 1753769463402, "results": "1598", "hashOfConfig": "887"}, {"size": 1952, "mtime": 1753769463403, "results": "1599", "hashOfConfig": "887"}, {"size": 145, "mtime": 1753769463404, "results": "1600", "hashOfConfig": "887"}, {"size": 831, "mtime": 1753769463405, "results": "1601", "hashOfConfig": "887"}, {"size": 3762, "mtime": 1753771415425, "results": "1602", "hashOfConfig": "887"}, {"size": 2385, "mtime": 1753769463443, "results": "1603", "hashOfConfig": "887"}, {"size": 6785, "mtime": 1753771415426, "results": "1604", "hashOfConfig": "887"}, {"size": 668, "mtime": 1753769463444, "results": "1605", "hashOfConfig": "887"}, {"size": 3684, "mtime": 1753771415426, "results": "1606", "hashOfConfig": "887"}, {"size": 3392, "mtime": 1753771415427, "results": "1607", "hashOfConfig": "887"}, {"size": 5690, "mtime": 1753771415428, "results": "1608", "hashOfConfig": "887"}, {"size": 1878, "mtime": 1753771415429, "results": "1609", "hashOfConfig": "887"}, {"size": 867, "mtime": 1753771415429, "results": "1610", "hashOfConfig": "887"}, {"size": 2364, "mtime": 1753769463448, "results": "1611", "hashOfConfig": "887"}, {"size": 10155, "mtime": 1753771415430, "results": "1612", "hashOfConfig": "887"}, {"size": 178, "mtime": 1753769463450, "results": "1613", "hashOfConfig": "887"}, {"size": 8054, "mtime": 1753771415431, "results": "1614", "hashOfConfig": "887"}, {"size": 198, "mtime": 1753769463452, "results": "1615", "hashOfConfig": "887"}, {"size": 6353, "mtime": 1753769463453, "results": "1616", "hashOfConfig": "887"}, {"size": 12499, "mtime": 1753771415432, "results": "1617", "hashOfConfig": "887"}, {"size": 17868, "mtime": 1753771415432, "results": "1618", "hashOfConfig": "887"}, {"size": 1712, "mtime": 1753771415442, "results": "1619", "hashOfConfig": "887"}, {"size": 6760, "mtime": 1753771415441, "results": "1620", "hashOfConfig": "887"}, {"size": 13300, "mtime": 1753777959778, "results": "1621", "hashOfConfig": "887"}, {"size": 3278, "mtime": 1753771415444, "results": "1622", "hashOfConfig": "887"}, {"size": 651, "mtime": 1753769463459, "results": "1623", "hashOfConfig": "887"}, {"size": 878, "mtime": 1753769463460, "results": "1624", "hashOfConfig": "887"}, {"size": 11300, "mtime": 1753771415446, "results": "1625", "hashOfConfig": "887"}, {"size": 1318, "mtime": 1753769463461, "results": "1626", "hashOfConfig": "887"}, {"size": 152, "mtime": 1753769463457, "results": "1627", "hashOfConfig": "887"}, {"size": 1465, "mtime": 1753771415447, "results": "1628", "hashOfConfig": "887"}, {"size": 3593, "mtime": 1753771415447, "results": "1629", "hashOfConfig": "887"}, {"size": 2311, "mtime": 1753769463464, "results": "1630", "hashOfConfig": "887"}, {"size": 4772, "mtime": 1753771415448, "results": "1631", "hashOfConfig": "887"}, {"size": 10339, "mtime": 1753771415449, "results": "1632", "hashOfConfig": "887"}, {"size": 2324, "mtime": 1753778167396, "results": "1633", "hashOfConfig": "887"}, {"size": 3387, "mtime": 1753771415450, "results": "1634", "hashOfConfig": "887"}, {"size": 3956, "mtime": 1753771415451, "results": "1635", "hashOfConfig": "887"}, {"size": 3758, "mtime": 1753771415452, "results": "1636", "hashOfConfig": "887"}, {"size": 6718, "mtime": 1753778078611, "results": "1637", "hashOfConfig": "887"}, {"size": 5830, "mtime": 1753771415453, "results": "1638", "hashOfConfig": "887"}, {"size": 9159, "mtime": 1753769463471, "results": "1639", "hashOfConfig": "887"}, {"size": 22790, "mtime": 1753771415454, "results": "1640", "hashOfConfig": "887"}, {"size": 13860, "mtime": 1753771415455, "results": "1641", "hashOfConfig": "887"}, {"size": 2009, "mtime": 1753769463474, "results": "1642", "hashOfConfig": "887"}, {"size": 7281, "mtime": 1753771415456, "results": "1643", "hashOfConfig": "887"}, {"size": 15438, "mtime": 1753771415457, "results": "1644", "hashOfConfig": "887"}, {"size": 700, "mtime": 1753769463477, "results": "1645", "hashOfConfig": "887"}, {"size": 7547, "mtime": 1753771415457, "results": "1646", "hashOfConfig": "887"}, {"size": 2909, "mtime": 1753771415458, "results": "1647", "hashOfConfig": "887"}, {"size": 20620, "mtime": 1753771415459, "results": "1648", "hashOfConfig": "887"}, {"size": 391, "mtime": 1753769463475, "results": "1649", "hashOfConfig": "887"}, {"size": 11167, "mtime": 1753769463481, "results": "1650", "hashOfConfig": "887"}, {"size": 14242, "mtime": 1753771415460, "results": "1651", "hashOfConfig": "887"}, {"size": 675, "mtime": 1753769463482, "results": "1652", "hashOfConfig": "887"}, {"size": 10573, "mtime": 1753771415461, "results": "1653", "hashOfConfig": "887"}, {"size": 3534, "mtime": 1753769463469, "results": "1654", "hashOfConfig": "887"}, {"size": 651, "mtime": 1753769463485, "results": "1655", "hashOfConfig": "887"}, {"size": 1264, "mtime": 1753769463486, "results": "1656", "hashOfConfig": "887"}, {"size": 6440, "mtime": 1753769463487, "results": "1657", "hashOfConfig": "887"}, {"size": 7233, "mtime": 1753769463488, "results": "1658", "hashOfConfig": "887"}, {"size": 22742, "mtime": 1753769463490, "results": "1659", "hashOfConfig": "887"}, {"size": 20955, "mtime": 1753769463490, "results": "1660", "hashOfConfig": "887"}, {"size": 2503, "mtime": 1753769463491, "results": "1661", "hashOfConfig": "887"}, {"size": 17737, "mtime": 1753769463493, "results": "1662", "hashOfConfig": "887"}, {"size": 1604, "mtime": 1753769463493, "results": "1663", "hashOfConfig": "887"}, {"size": 4395, "mtime": 1753769463494, "results": "1664", "hashOfConfig": "887"}, {"size": 3461, "mtime": 1753769463439, "results": "1665", "hashOfConfig": "887"}, {"size": 25034, "mtime": 1753769463496, "results": "1666", "hashOfConfig": "887"}, {"size": 2857, "mtime": 1753769463497, "results": "1667", "hashOfConfig": "887"}, {"size": 2531, "mtime": 1753769463498, "results": "1668", "hashOfConfig": "887"}, {"size": 1791, "mtime": 1753769463499, "results": "1669", "hashOfConfig": "887"}, {"size": 4594, "mtime": 1753769463500, "results": "1670", "hashOfConfig": "887"}, {"size": 204, "mtime": 1753769463501, "results": "1671", "hashOfConfig": "887"}, {"size": 1354, "mtime": 1753769463502, "results": "1672", "hashOfConfig": "887"}, {"size": 2050, "mtime": 1753769463502, "results": "1673", "hashOfConfig": "887"}, {"size": 6896, "mtime": 1753769463503, "results": "1674", "hashOfConfig": "887"}, {"size": 5462, "mtime": 1753769463504, "results": "1675", "hashOfConfig": "887"}, {"size": 413, "mtime": 1753769463505, "results": "1676", "hashOfConfig": "887"}, {"size": 6636, "mtime": 1753769463505, "results": "1677", "hashOfConfig": "887"}, {"size": 5603, "mtime": 1753769463503, "results": "1678", "hashOfConfig": "887"}, {"size": 4862, "mtime": 1753769463506, "results": "1679", "hashOfConfig": "887"}, {"size": 2137, "mtime": 1753769463507, "results": "1680", "hashOfConfig": "887"}, {"size": 1973, "mtime": 1753769463508, "results": "1681", "hashOfConfig": "887"}, {"size": 3119, "mtime": 1753769463509, "results": "1682", "hashOfConfig": "887"}, {"size": 660, "mtime": 1753769463509, "results": "1683", "hashOfConfig": "887"}, {"size": 5475, "mtime": 1753769463510, "results": "1684", "hashOfConfig": "887"}, {"size": 1623, "mtime": 1753769463511, "results": "1685", "hashOfConfig": "887"}, {"size": 8552, "mtime": 1753769463512, "results": "1686", "hashOfConfig": "887"}, {"size": 10690, "mtime": 1753769463512, "results": "1687", "hashOfConfig": "887"}, {"size": 6640, "mtime": 1753769463513, "results": "1688", "hashOfConfig": "887"}, {"size": 3241, "mtime": 1753769463514, "results": "1689", "hashOfConfig": "887"}, {"size": 7150, "mtime": 1753769463514, "results": "1690", "hashOfConfig": "887"}, {"size": 4983, "mtime": 1753771415462, "results": "1691", "hashOfConfig": "887"}, {"size": 2524, "mtime": 1753771415463, "results": "1692", "hashOfConfig": "887"}, {"size": 6372, "mtime": 1753771415464, "results": "1693", "hashOfConfig": "887"}, {"size": 489, "mtime": 1753769463519, "results": "1694", "hashOfConfig": "887"}, {"size": 4821, "mtime": 1753769463520, "results": "1695", "hashOfConfig": "887"}, {"size": 5260, "mtime": 1753769463521, "results": "1696", "hashOfConfig": "887"}, {"size": 399, "mtime": 1753769463522, "results": "1697", "hashOfConfig": "887"}, {"size": 11256, "mtime": 1753771415465, "results": "1698", "hashOfConfig": "887"}, {"size": 326, "mtime": 1753769463523, "results": "1699", "hashOfConfig": "887"}, {"size": 16659, "mtime": 1753771415466, "results": "1700", "hashOfConfig": "887"}, {"size": 5532, "mtime": 1753769463525, "results": "1701", "hashOfConfig": "887"}, {"size": 745, "mtime": 1753769463526, "results": "1702", "hashOfConfig": "887"}, {"size": 18910, "mtime": 1753771415467, "results": "1703", "hashOfConfig": "887"}, {"size": 4902, "mtime": 1753769463527, "results": "1704", "hashOfConfig": "887"}, {"size": 5364, "mtime": 1753771415468, "results": "1705", "hashOfConfig": "887"}, {"size": 13619, "mtime": 1753771415469, "results": "1706", "hashOfConfig": "887"}, {"size": 17685, "mtime": 1753771415469, "results": "1707", "hashOfConfig": "887"}, {"size": 6851, "mtime": 1753771415471, "results": "1708", "hashOfConfig": "887"}, {"size": 7605, "mtime": 1753771415473, "results": "1709", "hashOfConfig": "887"}, {"size": 5185, "mtime": 1753771415474, "results": "1710", "hashOfConfig": "887"}, {"size": 5336, "mtime": 1753771415475, "results": "1711", "hashOfConfig": "887"}, {"size": 5761, "mtime": 1753771415476, "results": "1712", "hashOfConfig": "887"}, {"size": 4665, "mtime": 1753771415476, "results": "1713", "hashOfConfig": "887"}, {"size": 2533, "mtime": 1753769463535, "results": "1714", "hashOfConfig": "887"}, {"size": 423, "mtime": 1753769463535, "results": "1715", "hashOfConfig": "887"}, {"size": 730, "mtime": 1753769463536, "results": "1716", "hashOfConfig": "887"}, {"size": 18833, "mtime": 1753771415477, "results": "1717", "hashOfConfig": "887"}, {"size": 215, "mtime": 1753769463538, "results": "1718", "hashOfConfig": "887"}, {"size": 4141, "mtime": 1753769463538, "results": "1719", "hashOfConfig": "887"}, {"size": 5808, "mtime": 1753771415478, "results": "1720", "hashOfConfig": "887"}, {"size": 442, "mtime": 1753769463516, "results": "1721", "hashOfConfig": "887"}, {"size": 6526, "mtime": 1753771415478, "results": "1722", "hashOfConfig": "887"}, {"size": 8817, "mtime": 1753771415479, "results": "1723", "hashOfConfig": "887"}, {"size": 3451, "mtime": 1753769463542, "results": "1724", "hashOfConfig": "887"}, {"size": 14131, "mtime": 1753769463542, "results": "1725", "hashOfConfig": "887"}, {"size": 1467, "mtime": 1753771415480, "results": "1726", "hashOfConfig": "887"}, {"size": 3060, "mtime": 1753771415480, "results": "1727", "hashOfConfig": "887"}, {"size": 5105, "mtime": 1753769463545, "results": "1728", "hashOfConfig": "887"}, {"size": 31716, "mtime": 1753771415481, "results": "1729", "hashOfConfig": "887"}, {"size": 10437, "mtime": 1753771415482, "results": "1730", "hashOfConfig": "887"}, {"size": 1344, "mtime": 1753769463547, "results": "1731", "hashOfConfig": "887"}, {"size": 2968, "mtime": 1753769463548, "results": "1732", "hashOfConfig": "887"}, {"size": 5366, "mtime": 1753769463550, "results": "1733", "hashOfConfig": "887"}, {"size": 4748, "mtime": 1753769463548, "results": "1734", "hashOfConfig": "887"}, {"size": 15108, "mtime": 1753771415502, "results": "1735", "hashOfConfig": "887"}, {"size": 2252, "mtime": 1753769463551, "results": "1736", "hashOfConfig": "887"}, {"size": 1929, "mtime": 1753774305280, "results": "1737", "hashOfConfig": "887"}, {"size": 5627, "mtime": 1753771415503, "results": "1738", "hashOfConfig": "887"}, {"size": 4375, "mtime": 1753771415504, "results": "1739", "hashOfConfig": "887"}, {"size": 9274, "mtime": 1753771415505, "results": "1740", "hashOfConfig": "887"}, {"size": 7254, "mtime": 1753771415506, "results": "1741", "hashOfConfig": "887"}, {"size": 737, "mtime": 1753771415506, "results": "1742", "hashOfConfig": "887"}, {"size": 504, "mtime": 1753771415507, "results": "1743", "hashOfConfig": "887"}, {"size": 416, "mtime": 1753769463553, "results": "1744", "hashOfConfig": "887"}, {"size": 1666, "mtime": 1753771415508, "results": "1745", "hashOfConfig": "887"}, {"size": 5367, "mtime": 1753776017130, "results": "1746", "hashOfConfig": "887"}, {"size": 1838, "mtime": 1753769463555, "results": "1747", "hashOfConfig": "887"}, {"size": 3163, "mtime": 1753769463557, "results": "1748", "hashOfConfig": "887"}, {"size": 4249, "mtime": 1753769463557, "results": "1749", "hashOfConfig": "887"}, {"size": 7117, "mtime": 1753771415509, "results": "1750", "hashOfConfig": "887"}, {"size": 4659, "mtime": 1753769463558, "results": "1751", "hashOfConfig": "887"}, {"size": 5800, "mtime": 1753769463559, "results": "1752", "hashOfConfig": "887"}, {"size": 1337, "mtime": 1753769463560, "results": "1753", "hashOfConfig": "887"}, {"size": 3715, "mtime": 1753769463560, "results": "1754", "hashOfConfig": "887"}, {"size": 5943, "mtime": 1753769463561, "results": "1755", "hashOfConfig": "887"}, {"size": 9375, "mtime": 1753769463562, "results": "1756", "hashOfConfig": "887"}, {"size": 10403, "mtime": 1753769463563, "results": "1757", "hashOfConfig": "887"}, {"size": 4149, "mtime": 1753769463564, "results": "1758", "hashOfConfig": "887"}, {"size": 7483, "mtime": 1753769463565, "results": "1759", "hashOfConfig": "887"}, {"size": 5633, "mtime": 1753769463566, "results": "1760", "hashOfConfig": "887"}, {"size": 6872, "mtime": 1753769463566, "results": "1761", "hashOfConfig": "887"}, {"size": 5929, "mtime": 1753769463567, "results": "1762", "hashOfConfig": "887"}, {"size": 1824, "mtime": 1753769463568, "results": "1763", "hashOfConfig": "887"}, {"size": 2184, "mtime": 1753769463568, "results": "1764", "hashOfConfig": "887"}, {"size": 5635, "mtime": 1753771415510, "results": "1765", "hashOfConfig": "887"}, {"size": 7773, "mtime": 1753771415510, "results": "1766", "hashOfConfig": "887"}, {"size": 1869, "mtime": 1753771415511, "results": "1767", "hashOfConfig": "887"}, {"size": 6965, "mtime": 1753769463570, "results": "1768", "hashOfConfig": "887"}, {"size": 7708, "mtime": 1753771415512, "results": "1769", "hashOfConfig": "887"}, {"size": 10682, "mtime": 1753771415513, "results": "1770", "hashOfConfig": "887"}, {"size": 6604, "mtime": 1753769463556, "results": "1771", "hashOfConfig": "887"}, {"filePath": "1772", "messages": "1773", "suppressedMessages": "1774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "v66z3k", {"filePath": "1775", "messages": "1776", "suppressedMessages": "1777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1778", "messages": "1779", "suppressedMessages": "1780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1781", "messages": "1782", "suppressedMessages": "1783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1784", "messages": "1785", "suppressedMessages": "1786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1787", "messages": "1788", "suppressedMessages": "1789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1790", "messages": "1791", "suppressedMessages": "1792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1793", "messages": "1794", "suppressedMessages": "1795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1796", "messages": "1797", "suppressedMessages": "1798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1799", "messages": "1800", "suppressedMessages": "1801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1802", "messages": "1803", "suppressedMessages": "1804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1805", "messages": "1806", "suppressedMessages": "1807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1808", "messages": "1809", "suppressedMessages": "1810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1811", "messages": "1812", "suppressedMessages": "1813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1814", "messages": "1815", "suppressedMessages": "1816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1817", "messages": "1818", "suppressedMessages": "1819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1820", "messages": "1821", "suppressedMessages": "1822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1823", "messages": "1824", "suppressedMessages": "1825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1826", "messages": "1827", "suppressedMessages": "1828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1829", "messages": "1830", "suppressedMessages": "1831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1832", "messages": "1833", "suppressedMessages": "1834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1835", "messages": "1836", "suppressedMessages": "1837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1838", "messages": "1839", "suppressedMessages": "1840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1841", "messages": "1842", "suppressedMessages": "1843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1844", "messages": "1845", "suppressedMessages": "1846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1847", "messages": "1848", "suppressedMessages": "1849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1850", "messages": "1851", "suppressedMessages": "1852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1853", "messages": "1854", "suppressedMessages": "1855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1856", "messages": "1857", "suppressedMessages": "1858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1859", "messages": "1860", "suppressedMessages": "1861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1862", "messages": "1863", "suppressedMessages": "1864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1865", "messages": "1866", "suppressedMessages": "1867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1868", "messages": "1869", "suppressedMessages": "1870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1871", "messages": "1872", "suppressedMessages": "1873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1874", "messages": "1875", "suppressedMessages": "1876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1877", "messages": "1878", "suppressedMessages": "1879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1880", "messages": "1881", "suppressedMessages": "1882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1883", "messages": "1884", "suppressedMessages": "1885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1886", "messages": "1887", "suppressedMessages": "1888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1889", "messages": "1890", "suppressedMessages": "1891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1892", "messages": "1893", "suppressedMessages": "1894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1895", "messages": "1896", "suppressedMessages": "1897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1898", "messages": "1899", "suppressedMessages": "1900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1901", "messages": "1902", "suppressedMessages": "1903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1904", "messages": "1905", "suppressedMessages": "1906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1907", "messages": "1908", "suppressedMessages": "1909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1910", "messages": "1911", "suppressedMessages": "1912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1913", "messages": "1914", "suppressedMessages": "1915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1916", "messages": "1917", "suppressedMessages": "1918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1919", "messages": "1920", "suppressedMessages": "1921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1922", "messages": "1923", "suppressedMessages": "1924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1925", "messages": "1926", "suppressedMessages": "1927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1928", "messages": "1929", "suppressedMessages": "1930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1931", "messages": "1932", "suppressedMessages": "1933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1934", "messages": "1935", "suppressedMessages": "1936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1937", "messages": "1938", "suppressedMessages": "1939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1940", "messages": "1941", "suppressedMessages": "1942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1943", "messages": "1944", "suppressedMessages": "1945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1946", "messages": "1947", "suppressedMessages": "1948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1949", "messages": "1950", "suppressedMessages": "1951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1952", "messages": "1953", "suppressedMessages": "1954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1955", "messages": "1956", "suppressedMessages": "1957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1958", "messages": "1959", "suppressedMessages": "1960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1961", "messages": "1962", "suppressedMessages": "1963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1964", "messages": "1965", "suppressedMessages": "1966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1967", "messages": "1968", "suppressedMessages": "1969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1970", "messages": "1971", "suppressedMessages": "1972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1973", "messages": "1974", "suppressedMessages": "1975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1976", "messages": "1977", "suppressedMessages": "1978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1979", "messages": "1980", "suppressedMessages": "1981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1982", "messages": "1983", "suppressedMessages": "1984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1985", "messages": "1986", "suppressedMessages": "1987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1988", "messages": "1989", "suppressedMessages": "1990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1991", "messages": "1992", "suppressedMessages": "1993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1994", "messages": "1995", "suppressedMessages": "1996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1997", "messages": "1998", "suppressedMessages": "1999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2000", "messages": "2001", "suppressedMessages": "2002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2003", "messages": "2004", "suppressedMessages": "2005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2006", "messages": "2007", "suppressedMessages": "2008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2009", "messages": "2010", "suppressedMessages": "2011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2012", "messages": "2013", "suppressedMessages": "2014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2015", "messages": "2016", "suppressedMessages": "2017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2018", "messages": "2019", "suppressedMessages": "2020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2021", "messages": "2022", "suppressedMessages": "2023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2024", "messages": "2025", "suppressedMessages": "2026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2027", "messages": "2028", "suppressedMessages": "2029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2030", "messages": "2031", "suppressedMessages": "2032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2033", "messages": "2034", "suppressedMessages": "2035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2036", "messages": "2037", "suppressedMessages": "2038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2039", "messages": "2040", "suppressedMessages": "2041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2042", "messages": "2043", "suppressedMessages": "2044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2045", "messages": "2046", "suppressedMessages": "2047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2048", "messages": "2049", "suppressedMessages": "2050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2051", "messages": "2052", "suppressedMessages": "2053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2054", "messages": "2055", "suppressedMessages": "2056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2057", "messages": "2058", "suppressedMessages": "2059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2060", "messages": "2061", "suppressedMessages": "2062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2063", "messages": "2064", "suppressedMessages": "2065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2066", "messages": "2067", "suppressedMessages": "2068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2069", "messages": "2070", "suppressedMessages": "2071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2072", "messages": "2073", "suppressedMessages": "2074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2075", "messages": "2076", "suppressedMessages": "2077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2078", "messages": "2079", "suppressedMessages": "2080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2081", "messages": "2082", "suppressedMessages": "2083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2084", "messages": "2085", "suppressedMessages": "2086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2087", "messages": "2088", "suppressedMessages": "2089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2090", "messages": "2091", "suppressedMessages": "2092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2093", "messages": "2094", "suppressedMessages": "2095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2096", "messages": "2097", "suppressedMessages": "2098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2099", "messages": "2100", "suppressedMessages": "2101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2102", "messages": "2103", "suppressedMessages": "2104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2105", "messages": "2106", "suppressedMessages": "2107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2108", "messages": "2109", "suppressedMessages": "2110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2111", "messages": "2112", "suppressedMessages": "2113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2114", "messages": "2115", "suppressedMessages": "2116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2117", "messages": "2118", "suppressedMessages": "2119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2120", "messages": "2121", "suppressedMessages": "2122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2123", "messages": "2124", "suppressedMessages": "2125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2126", "messages": "2127", "suppressedMessages": "2128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2129", "messages": "2130", "suppressedMessages": "2131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2132", "messages": "2133", "suppressedMessages": "2134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2135", "messages": "2136", "suppressedMessages": "2137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2138", "messages": "2139", "suppressedMessages": "2140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2141", "messages": "2142", "suppressedMessages": "2143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2144", "messages": "2145", "suppressedMessages": "2146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2147", "messages": "2148", "suppressedMessages": "2149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2150", "messages": "2151", "suppressedMessages": "2152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2153", "messages": "2154", "suppressedMessages": "2155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2156", "messages": "2157", "suppressedMessages": "2158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2159", "messages": "2160", "suppressedMessages": "2161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2162", "messages": "2163", "suppressedMessages": "2164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2165", "messages": "2166", "suppressedMessages": "2167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2168", "messages": "2169", "suppressedMessages": "2170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2171", "messages": "2172", "suppressedMessages": "2173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2174", "messages": "2175", "suppressedMessages": "2176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2177", "messages": "2178", "suppressedMessages": "2179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2180", "messages": "2181", "suppressedMessages": "2182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2183", "messages": "2184", "suppressedMessages": "2185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2186", "messages": "2187", "suppressedMessages": "2188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2189", "messages": "2190", "suppressedMessages": "2191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2192", "messages": "2193", "suppressedMessages": "2194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2195", "messages": "2196", "suppressedMessages": "2197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2198", "messages": "2199", "suppressedMessages": "2200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2201", "messages": "2202", "suppressedMessages": "2203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2204", "messages": "2205", "suppressedMessages": "2206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2207", "messages": "2208", "suppressedMessages": "2209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2210", "messages": "2211", "suppressedMessages": "2212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2213", "messages": "2214", "suppressedMessages": "2215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2216", "messages": "2217", "suppressedMessages": "2218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2219", "messages": "2220", "suppressedMessages": "2221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2222", "messages": "2223", "suppressedMessages": "2224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2225", "messages": "2226", "suppressedMessages": "2227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2228", "messages": "2229", "suppressedMessages": "2230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2231", "messages": "2232", "suppressedMessages": "2233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2234", "messages": "2235", "suppressedMessages": "2236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2237", "messages": "2238", "suppressedMessages": "2239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2240", "messages": "2241", "suppressedMessages": "2242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2243", "messages": "2244", "suppressedMessages": "2245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2246", "messages": "2247", "suppressedMessages": "2248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2249", "messages": "2250", "suppressedMessages": "2251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2252", "messages": "2253", "suppressedMessages": "2254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2255", "messages": "2256", "suppressedMessages": "2257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2258", "messages": "2259", "suppressedMessages": "2260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2261", "messages": "2262", "suppressedMessages": "2263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2264", "messages": "2265", "suppressedMessages": "2266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2267", "messages": "2268", "suppressedMessages": "2269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2270", "messages": "2271", "suppressedMessages": "2272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2273", "messages": "2274", "suppressedMessages": "2275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2276", "messages": "2277", "suppressedMessages": "2278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2279", "messages": "2280", "suppressedMessages": "2281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2282", "messages": "2283", "suppressedMessages": "2284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2285", "messages": "2286", "suppressedMessages": "2287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2288", "messages": "2289", "suppressedMessages": "2290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2291", "messages": "2292", "suppressedMessages": "2293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2294", "messages": "2295", "suppressedMessages": "2296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2297", "messages": "2298", "suppressedMessages": "2299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2300", "messages": "2301", "suppressedMessages": "2302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2303", "messages": "2304", "suppressedMessages": "2305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2306", "messages": "2307", "suppressedMessages": "2308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2309", "messages": "2310", "suppressedMessages": "2311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2312", "messages": "2313", "suppressedMessages": "2314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2315", "messages": "2316", "suppressedMessages": "2317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2318", "messages": "2319", "suppressedMessages": "2320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2321", "messages": "2322", "suppressedMessages": "2323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2324", "messages": "2325", "suppressedMessages": "2326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2327", "messages": "2328", "suppressedMessages": "2329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2330", "messages": "2331", "suppressedMessages": "2332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2333", "messages": "2334", "suppressedMessages": "2335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2336", "messages": "2337", "suppressedMessages": "2338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2339", "messages": "2340", "suppressedMessages": "2341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2342", "messages": "2343", "suppressedMessages": "2344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2345", "messages": "2346", "suppressedMessages": "2347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2348", "messages": "2349", "suppressedMessages": "2350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2351", "messages": "2352", "suppressedMessages": "2353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2354", "messages": "2355", "suppressedMessages": "2356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2357", "messages": "2358", "suppressedMessages": "2359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2360", "messages": "2361", "suppressedMessages": "2362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2363", "messages": "2364", "suppressedMessages": "2365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2366", "messages": "2367", "suppressedMessages": "2368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2369", "messages": "2370", "suppressedMessages": "2371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2372", "messages": "2373", "suppressedMessages": "2374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2375", "messages": "2376", "suppressedMessages": "2377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2378", "messages": "2379", "suppressedMessages": "2380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2381", "messages": "2382", "suppressedMessages": "2383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2384", "messages": "2385", "suppressedMessages": "2386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2387", "messages": "2388", "suppressedMessages": "2389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2390", "messages": "2391", "suppressedMessages": "2392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2393", "messages": "2394", "suppressedMessages": "2395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2396", "messages": "2397", "suppressedMessages": "2398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2399", "messages": "2400", "suppressedMessages": "2401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2402", "messages": "2403", "suppressedMessages": "2404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2405", "messages": "2406", "suppressedMessages": "2407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2408", "messages": "2409", "suppressedMessages": "2410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2411", "messages": "2412", "suppressedMessages": "2413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2414", "messages": "2415", "suppressedMessages": "2416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2417", "messages": "2418", "suppressedMessages": "2419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2420", "messages": "2421", "suppressedMessages": "2422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2423", "messages": "2424", "suppressedMessages": "2425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2426", "messages": "2427", "suppressedMessages": "2428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2429", "messages": "2430", "suppressedMessages": "2431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2432", "messages": "2433", "suppressedMessages": "2434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2435", "messages": "2436", "suppressedMessages": "2437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2438", "messages": "2439", "suppressedMessages": "2440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2441", "messages": "2442", "suppressedMessages": "2443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2444", "messages": "2445", "suppressedMessages": "2446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2447", "messages": "2448", "suppressedMessages": "2449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2450", "messages": "2451", "suppressedMessages": "2452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2453", "messages": "2454", "suppressedMessages": "2455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2456", "messages": "2457", "suppressedMessages": "2458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2459", "messages": "2460", "suppressedMessages": "2461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2462", "messages": "2463", "suppressedMessages": "2464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2465", "messages": "2466", "suppressedMessages": "2467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2468", "messages": "2469", "suppressedMessages": "2470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2471", "messages": "2472", "suppressedMessages": "2473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2474", "messages": "2475", "suppressedMessages": "2476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2477", "messages": "2478", "suppressedMessages": "2479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2480", "messages": "2481", "suppressedMessages": "2482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2483", "messages": "2484", "suppressedMessages": "2485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2486", "messages": "2487", "suppressedMessages": "2488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2489", "messages": "2490", "suppressedMessages": "2491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2492", "messages": "2493", "suppressedMessages": "2494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2495", "messages": "2496", "suppressedMessages": "2497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2498", "messages": "2499", "suppressedMessages": "2500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2501", "messages": "2502", "suppressedMessages": "2503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2504", "messages": "2505", "suppressedMessages": "2506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2507", "messages": "2508", "suppressedMessages": "2509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2510", "messages": "2511", "suppressedMessages": "2512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2513", "messages": "2514", "suppressedMessages": "2515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2516", "messages": "2517", "suppressedMessages": "2518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2519", "messages": "2520", "suppressedMessages": "2521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2522", "messages": "2523", "suppressedMessages": "2524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2525", "messages": "2526", "suppressedMessages": "2527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2528", "messages": "2529", "suppressedMessages": "2530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2531", "messages": "2532", "suppressedMessages": "2533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2534", "messages": "2535", "suppressedMessages": "2536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2537", "messages": "2538", "suppressedMessages": "2539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2540", "messages": "2541", "suppressedMessages": "2542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2543", "messages": "2544", "suppressedMessages": "2545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2546", "messages": "2547", "suppressedMessages": "2548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2549", "messages": "2550", "suppressedMessages": "2551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2552", "messages": "2553", "suppressedMessages": "2554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2555", "messages": "2556", "suppressedMessages": "2557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2558", "messages": "2559", "suppressedMessages": "2560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2561", "messages": "2562", "suppressedMessages": "2563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2564", "messages": "2565", "suppressedMessages": "2566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2567", "messages": "2568", "suppressedMessages": "2569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2570", "messages": "2571", "suppressedMessages": "2572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2573", "messages": "2574", "suppressedMessages": "2575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2576", "messages": "2577", "suppressedMessages": "2578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2579", "messages": "2580", "suppressedMessages": "2581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2582", "messages": "2583", "suppressedMessages": "2584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2585", "messages": "2586", "suppressedMessages": "2587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2588", "messages": "2589", "suppressedMessages": "2590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2591", "messages": "2592", "suppressedMessages": "2593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2594", "messages": "2595", "suppressedMessages": "2596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2597", "messages": "2598", "suppressedMessages": "2599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2600", "messages": "2601", "suppressedMessages": "2602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2603", "messages": "2604", "suppressedMessages": "2605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2606", "messages": "2607", "suppressedMessages": "2608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2609", "messages": "2610", "suppressedMessages": "2611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2612", "messages": "2613", "suppressedMessages": "2614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2615", "messages": "2616", "suppressedMessages": "2617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2618", "messages": "2619", "suppressedMessages": "2620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2621", "messages": "2622", "suppressedMessages": "2623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2624", "messages": "2625", "suppressedMessages": "2626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2627", "messages": "2628", "suppressedMessages": "2629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2630", "messages": "2631", "suppressedMessages": "2632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2633", "messages": "2634", "suppressedMessages": "2635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2636", "messages": "2637", "suppressedMessages": "2638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2639", "messages": "2640", "suppressedMessages": "2641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2642", "messages": "2643", "suppressedMessages": "2644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2645", "messages": "2646", "suppressedMessages": "2647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2648", "messages": "2649", "suppressedMessages": "2650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2651", "messages": "2652", "suppressedMessages": "2653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2654", "messages": "2655", "suppressedMessages": "2656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2657", "messages": "2658", "suppressedMessages": "2659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2660", "messages": "2661", "suppressedMessages": "2662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2663", "messages": "2664", "suppressedMessages": "2665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2666", "messages": "2667", "suppressedMessages": "2668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2669", "messages": "2670", "suppressedMessages": "2671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2672", "messages": "2673", "suppressedMessages": "2674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2675", "messages": "2676", "suppressedMessages": "2677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2678", "messages": "2679", "suppressedMessages": "2680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2681", "messages": "2682", "suppressedMessages": "2683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2684", "messages": "2685", "suppressedMessages": "2686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2687", "messages": "2688", "suppressedMessages": "2689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2690", "messages": "2691", "suppressedMessages": "2692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2693", "messages": "2694", "suppressedMessages": "2695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2696", "messages": "2697", "suppressedMessages": "2698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2699", "messages": "2700", "suppressedMessages": "2701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2702", "messages": "2703", "suppressedMessages": "2704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2705", "messages": "2706", "suppressedMessages": "2707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2708", "messages": "2709", "suppressedMessages": "2710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2711", "messages": "2712", "suppressedMessages": "2713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2714", "messages": "2715", "suppressedMessages": "2716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2717", "messages": "2718", "suppressedMessages": "2719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2720", "messages": "2721", "suppressedMessages": "2722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2723", "messages": "2724", "suppressedMessages": "2725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2726", "messages": "2727", "suppressedMessages": "2728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2729", "messages": "2730", "suppressedMessages": "2731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2732", "messages": "2733", "suppressedMessages": "2734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2735", "messages": "2736", "suppressedMessages": "2737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2738", "messages": "2739", "suppressedMessages": "2740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2741", "messages": "2742", "suppressedMessages": "2743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2744", "messages": "2745", "suppressedMessages": "2746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2747", "messages": "2748", "suppressedMessages": "2749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2750", "messages": "2751", "suppressedMessages": "2752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2753", "messages": "2754", "suppressedMessages": "2755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2756", "messages": "2757", "suppressedMessages": "2758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2759", "messages": "2760", "suppressedMessages": "2761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2762", "messages": "2763", "suppressedMessages": "2764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2765", "messages": "2766", "suppressedMessages": "2767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2768", "messages": "2769", "suppressedMessages": "2770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2771", "messages": "2772", "suppressedMessages": "2773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2774", "messages": "2775", "suppressedMessages": "2776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2777", "messages": "2778", "suppressedMessages": "2779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2780", "messages": "2781", "suppressedMessages": "2782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2783", "messages": "2784", "suppressedMessages": "2785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2786", "messages": "2787", "suppressedMessages": "2788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2789", "messages": "2790", "suppressedMessages": "2791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2792", "messages": "2793", "suppressedMessages": "2794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2795", "messages": "2796", "suppressedMessages": "2797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2798", "messages": "2799", "suppressedMessages": "2800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2801", "messages": "2802", "suppressedMessages": "2803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2804", "messages": "2805", "suppressedMessages": "2806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2807", "messages": "2808", "suppressedMessages": "2809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2810", "messages": "2811", "suppressedMessages": "2812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2813", "messages": "2814", "suppressedMessages": "2815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2816", "messages": "2817", "suppressedMessages": "2818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2819", "messages": "2820", "suppressedMessages": "2821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2822", "messages": "2823", "suppressedMessages": "2824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2825", "messages": "2826", "suppressedMessages": "2827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2828", "messages": "2829", "suppressedMessages": "2830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2831", "messages": "2832", "suppressedMessages": "2833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2834", "messages": "2835", "suppressedMessages": "2836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2837", "messages": "2838", "suppressedMessages": "2839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2840", "messages": "2841", "suppressedMessages": "2842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2843", "messages": "2844", "suppressedMessages": "2845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2846", "messages": "2847", "suppressedMessages": "2848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2849", "messages": "2850", "suppressedMessages": "2851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2852", "messages": "2853", "suppressedMessages": "2854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2855", "messages": "2856", "suppressedMessages": "2857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2858", "messages": "2859", "suppressedMessages": "2860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2861", "messages": "2862", "suppressedMessages": "2863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2864", "messages": "2865", "suppressedMessages": "2866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2867", "messages": "2868", "suppressedMessages": "2869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2870", "messages": "2871", "suppressedMessages": "2872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2873", "messages": "2874", "suppressedMessages": "2875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2876", "messages": "2877", "suppressedMessages": "2878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2879", "messages": "2880", "suppressedMessages": "2881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2882", "messages": "2883", "suppressedMessages": "2884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2885", "messages": "2886", "suppressedMessages": "2887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2888", "messages": "2889", "suppressedMessages": "2890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2891", "messages": "2892", "suppressedMessages": "2893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2894", "messages": "2895", "suppressedMessages": "2896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2897", "messages": "2898", "suppressedMessages": "2899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2900", "messages": "2901", "suppressedMessages": "2902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2903", "messages": "2904", "suppressedMessages": "2905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2906", "messages": "2907", "suppressedMessages": "2908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2909", "messages": "2910", "suppressedMessages": "2911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2912", "messages": "2913", "suppressedMessages": "2914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2915", "messages": "2916", "suppressedMessages": "2917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2918", "messages": "2919", "suppressedMessages": "2920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2921", "messages": "2922", "suppressedMessages": "2923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2924", "messages": "2925", "suppressedMessages": "2926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2927", "messages": "2928", "suppressedMessages": "2929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2930", "messages": "2931", "suppressedMessages": "2932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2933", "messages": "2934", "suppressedMessages": "2935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2936", "messages": "2937", "suppressedMessages": "2938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2939", "messages": "2940", "suppressedMessages": "2941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2942", "messages": "2943", "suppressedMessages": "2944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2945", "messages": "2946", "suppressedMessages": "2947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2948", "messages": "2949", "suppressedMessages": "2950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2951", "messages": "2952", "suppressedMessages": "2953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2954", "messages": "2955", "suppressedMessages": "2956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2957", "messages": "2958", "suppressedMessages": "2959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2960", "messages": "2961", "suppressedMessages": "2962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2963", "messages": "2964", "suppressedMessages": "2965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2966", "messages": "2967", "suppressedMessages": "2968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2969", "messages": "2970", "suppressedMessages": "2971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2972", "messages": "2973", "suppressedMessages": "2974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2975", "messages": "2976", "suppressedMessages": "2977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2978", "messages": "2979", "suppressedMessages": "2980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2981", "messages": "2982", "suppressedMessages": "2983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2984", "messages": "2985", "suppressedMessages": "2986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2987", "messages": "2988", "suppressedMessages": "2989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2990", "messages": "2991", "suppressedMessages": "2992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2993", "messages": "2994", "suppressedMessages": "2995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2996", "messages": "2997", "suppressedMessages": "2998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2999", "messages": "3000", "suppressedMessages": "3001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3002", "messages": "3003", "suppressedMessages": "3004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3005", "messages": "3006", "suppressedMessages": "3007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3008", "messages": "3009", "suppressedMessages": "3010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3011", "messages": "3012", "suppressedMessages": "3013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3014", "messages": "3015", "suppressedMessages": "3016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3017", "messages": "3018", "suppressedMessages": "3019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3020", "messages": "3021", "suppressedMessages": "3022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3023", "messages": "3024", "suppressedMessages": "3025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3026", "messages": "3027", "suppressedMessages": "3028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3029", "messages": "3030", "suppressedMessages": "3031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3032", "messages": "3033", "suppressedMessages": "3034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3035", "messages": "3036", "suppressedMessages": "3037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3038", "messages": "3039", "suppressedMessages": "3040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3041", "messages": "3042", "suppressedMessages": "3043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3044", "messages": "3045", "suppressedMessages": "3046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3047", "messages": "3048", "suppressedMessages": "3049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3050", "messages": "3051", "suppressedMessages": "3052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3053", "messages": "3054", "suppressedMessages": "3055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3056", "messages": "3057", "suppressedMessages": "3058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3059", "messages": "3060", "suppressedMessages": "3061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3062", "messages": "3063", "suppressedMessages": "3064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3065", "messages": "3066", "suppressedMessages": "3067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3068", "messages": "3069", "suppressedMessages": "3070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3071", "messages": "3072", "suppressedMessages": "3073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3074", "messages": "3075", "suppressedMessages": "3076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3077", "messages": "3078", "suppressedMessages": "3079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3080", "messages": "3081", "suppressedMessages": "3082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3083", "messages": "3084", "suppressedMessages": "3085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3086", "messages": "3087", "suppressedMessages": "3088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3089", "messages": "3090", "suppressedMessages": "3091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3092", "messages": "3093", "suppressedMessages": "3094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3095", "messages": "3096", "suppressedMessages": "3097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3098", "messages": "3099", "suppressedMessages": "3100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3101", "messages": "3102", "suppressedMessages": "3103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3104", "messages": "3105", "suppressedMessages": "3106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3107", "messages": "3108", "suppressedMessages": "3109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3110", "messages": "3111", "suppressedMessages": "3112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3113", "messages": "3114", "suppressedMessages": "3115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3116", "messages": "3117", "suppressedMessages": "3118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3119", "messages": "3120", "suppressedMessages": "3121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3122", "messages": "3123", "suppressedMessages": "3124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3125", "messages": "3126", "suppressedMessages": "3127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3128", "messages": "3129", "suppressedMessages": "3130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3131", "messages": "3132", "suppressedMessages": "3133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3134", "messages": "3135", "suppressedMessages": "3136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3137", "messages": "3138", "suppressedMessages": "3139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3140", "messages": "3141", "suppressedMessages": "3142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3143", "messages": "3144", "suppressedMessages": "3145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3146", "messages": "3147", "suppressedMessages": "3148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3149", "messages": "3150", "suppressedMessages": "3151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3152", "messages": "3153", "suppressedMessages": "3154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3155", "messages": "3156", "suppressedMessages": "3157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3158", "messages": "3159", "suppressedMessages": "3160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3161", "messages": "3162", "suppressedMessages": "3163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3164", "messages": "3165", "suppressedMessages": "3166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3167", "messages": "3168", "suppressedMessages": "3169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3170", "messages": "3171", "suppressedMessages": "3172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3173", "messages": "3174", "suppressedMessages": "3175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3176", "messages": "3177", "suppressedMessages": "3178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3179", "messages": "3180", "suppressedMessages": "3181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3182", "messages": "3183", "suppressedMessages": "3184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3185", "messages": "3186", "suppressedMessages": "3187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3188", "messages": "3189", "suppressedMessages": "3190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3191", "messages": "3192", "suppressedMessages": "3193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3194", "messages": "3195", "suppressedMessages": "3196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3197", "messages": "3198", "suppressedMessages": "3199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3200", "messages": "3201", "suppressedMessages": "3202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3203", "messages": "3204", "suppressedMessages": "3205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3206", "messages": "3207", "suppressedMessages": "3208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3209", "messages": "3210", "suppressedMessages": "3211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3212", "messages": "3213", "suppressedMessages": "3214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3215", "messages": "3216", "suppressedMessages": "3217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3218", "messages": "3219", "suppressedMessages": "3220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3221", "messages": "3222", "suppressedMessages": "3223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3224", "messages": "3225", "suppressedMessages": "3226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3227", "messages": "3228", "suppressedMessages": "3229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3230", "messages": "3231", "suppressedMessages": "3232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3233", "messages": "3234", "suppressedMessages": "3235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3236", "messages": "3237", "suppressedMessages": "3238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3239", "messages": "3240", "suppressedMessages": "3241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3242", "messages": "3243", "suppressedMessages": "3244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3245", "messages": "3246", "suppressedMessages": "3247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3248", "messages": "3249", "suppressedMessages": "3250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3251", "messages": "3252", "suppressedMessages": "3253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3254", "messages": "3255", "suppressedMessages": "3256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3257", "messages": "3258", "suppressedMessages": "3259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3260", "messages": "3261", "suppressedMessages": "3262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3263", "messages": "3264", "suppressedMessages": "3265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3266", "messages": "3267", "suppressedMessages": "3268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3269", "messages": "3270", "suppressedMessages": "3271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3272", "messages": "3273", "suppressedMessages": "3274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3275", "messages": "3276", "suppressedMessages": "3277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3278", "messages": "3279", "suppressedMessages": "3280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3281", "messages": "3282", "suppressedMessages": "3283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3284", "messages": "3285", "suppressedMessages": "3286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3287", "messages": "3288", "suppressedMessages": "3289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3290", "messages": "3291", "suppressedMessages": "3292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3293", "messages": "3294", "suppressedMessages": "3295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3296", "messages": "3297", "suppressedMessages": "3298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3299", "messages": "3300", "suppressedMessages": "3301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3302", "messages": "3303", "suppressedMessages": "3304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3305", "messages": "3306", "suppressedMessages": "3307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3308", "messages": "3309", "suppressedMessages": "3310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3311", "messages": "3312", "suppressedMessages": "3313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3314", "messages": "3315", "suppressedMessages": "3316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3317", "messages": "3318", "suppressedMessages": "3319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3320", "messages": "3321", "suppressedMessages": "3322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3323", "messages": "3324", "suppressedMessages": "3325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3326", "messages": "3327", "suppressedMessages": "3328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3329", "messages": "3330", "suppressedMessages": "3331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3332", "messages": "3333", "suppressedMessages": "3334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3335", "messages": "3336", "suppressedMessages": "3337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3338", "messages": "3339", "suppressedMessages": "3340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3341", "messages": "3342", "suppressedMessages": "3343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3344", "messages": "3345", "suppressedMessages": "3346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3347", "messages": "3348", "suppressedMessages": "3349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3350", "messages": "3351", "suppressedMessages": "3352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3353", "messages": "3354", "suppressedMessages": "3355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3356", "messages": "3357", "suppressedMessages": "3358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3359", "messages": "3360", "suppressedMessages": "3361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3362", "messages": "3363", "suppressedMessages": "3364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3365", "messages": "3366", "suppressedMessages": "3367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3368", "messages": "3369", "suppressedMessages": "3370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3371", "messages": "3372", "suppressedMessages": "3373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3374", "messages": "3375", "suppressedMessages": "3376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3377", "messages": "3378", "suppressedMessages": "3379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3380", "messages": "3381", "suppressedMessages": "3382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3383", "messages": "3384", "suppressedMessages": "3385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3386", "messages": "3387", "suppressedMessages": "3388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3389", "messages": "3390", "suppressedMessages": "3391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3392", "messages": "3393", "suppressedMessages": "3394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3395", "messages": "3396", "suppressedMessages": "3397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3398", "messages": "3399", "suppressedMessages": "3400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3401", "messages": "3402", "suppressedMessages": "3403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3404", "messages": "3405", "suppressedMessages": "3406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3407", "messages": "3408", "suppressedMessages": "3409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3410", "messages": "3411", "suppressedMessages": "3412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3413", "messages": "3414", "suppressedMessages": "3415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3416", "messages": "3417", "suppressedMessages": "3418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3419", "messages": "3420", "suppressedMessages": "3421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3422", "messages": "3423", "suppressedMessages": "3424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3425", "messages": "3426", "suppressedMessages": "3427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3428", "messages": "3429", "suppressedMessages": "3430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3431", "messages": "3432", "suppressedMessages": "3433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3434", "messages": "3435", "suppressedMessages": "3436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3437", "messages": "3438", "suppressedMessages": "3439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3440", "messages": "3441", "suppressedMessages": "3442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3443", "messages": "3444", "suppressedMessages": "3445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3446", "messages": "3447", "suppressedMessages": "3448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3449", "messages": "3450", "suppressedMessages": "3451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3452", "messages": "3453", "suppressedMessages": "3454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3455", "messages": "3456", "suppressedMessages": "3457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3458", "messages": "3459", "suppressedMessages": "3460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3461", "messages": "3462", "suppressedMessages": "3463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3464", "messages": "3465", "suppressedMessages": "3466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3467", "messages": "3468", "suppressedMessages": "3469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3470", "messages": "3471", "suppressedMessages": "3472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3473", "messages": "3474", "suppressedMessages": "3475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3476", "messages": "3477", "suppressedMessages": "3478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3479", "messages": "3480", "suppressedMessages": "3481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3482", "messages": "3483", "suppressedMessages": "3484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3485", "messages": "3486", "suppressedMessages": "3487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3488", "messages": "3489", "suppressedMessages": "3490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3491", "messages": "3492", "suppressedMessages": "3493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3494", "messages": "3495", "suppressedMessages": "3496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3497", "messages": "3498", "suppressedMessages": "3499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3500", "messages": "3501", "suppressedMessages": "3502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3503", "messages": "3504", "suppressedMessages": "3505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3506", "messages": "3507", "suppressedMessages": "3508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3509", "messages": "3510", "suppressedMessages": "3511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3512", "messages": "3513", "suppressedMessages": "3514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3515", "messages": "3516", "suppressedMessages": "3517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3518", "messages": "3519", "suppressedMessages": "3520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3521", "messages": "3522", "suppressedMessages": "3523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3524", "messages": "3525", "suppressedMessages": "3526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3527", "messages": "3528", "suppressedMessages": "3529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3530", "messages": "3531", "suppressedMessages": "3532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3533", "messages": "3534", "suppressedMessages": "3535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3536", "messages": "3537", "suppressedMessages": "3538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3539", "messages": "3540", "suppressedMessages": "3541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3542", "messages": "3543", "suppressedMessages": "3544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3545", "messages": "3546", "suppressedMessages": "3547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3548", "messages": "3549", "suppressedMessages": "3550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3551", "messages": "3552", "suppressedMessages": "3553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3554", "messages": "3555", "suppressedMessages": "3556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3557", "messages": "3558", "suppressedMessages": "3559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3560", "messages": "3561", "suppressedMessages": "3562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3563", "messages": "3564", "suppressedMessages": "3565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3566", "messages": "3567", "suppressedMessages": "3568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3569", "messages": "3570", "suppressedMessages": "3571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3572", "messages": "3573", "suppressedMessages": "3574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3575", "messages": "3576", "suppressedMessages": "3577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3578", "messages": "3579", "suppressedMessages": "3580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3581", "messages": "3582", "suppressedMessages": "3583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3584", "messages": "3585", "suppressedMessages": "3586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3587", "messages": "3588", "suppressedMessages": "3589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3590", "messages": "3591", "suppressedMessages": "3592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3593", "messages": "3594", "suppressedMessages": "3595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3596", "messages": "3597", "suppressedMessages": "3598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3599", "messages": "3600", "suppressedMessages": "3601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3602", "messages": "3603", "suppressedMessages": "3604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3605", "messages": "3606", "suppressedMessages": "3607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3608", "messages": "3609", "suppressedMessages": "3610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3611", "messages": "3612", "suppressedMessages": "3613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3614", "messages": "3615", "suppressedMessages": "3616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3617", "messages": "3618", "suppressedMessages": "3619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3620", "messages": "3621", "suppressedMessages": "3622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3623", "messages": "3624", "suppressedMessages": "3625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3626", "messages": "3627", "suppressedMessages": "3628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3629", "messages": "3630", "suppressedMessages": "3631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3632", "messages": "3633", "suppressedMessages": "3634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3635", "messages": "3636", "suppressedMessages": "3637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3638", "messages": "3639", "suppressedMessages": "3640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3641", "messages": "3642", "suppressedMessages": "3643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3644", "messages": "3645", "suppressedMessages": "3646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3647", "messages": "3648", "suppressedMessages": "3649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3650", "messages": "3651", "suppressedMessages": "3652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3653", "messages": "3654", "suppressedMessages": "3655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3656", "messages": "3657", "suppressedMessages": "3658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3659", "messages": "3660", "suppressedMessages": "3661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3662", "messages": "3663", "suppressedMessages": "3664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3665", "messages": "3666", "suppressedMessages": "3667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3668", "messages": "3669", "suppressedMessages": "3670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3671", "messages": "3672", "suppressedMessages": "3673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3674", "messages": "3675", "suppressedMessages": "3676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3677", "messages": "3678", "suppressedMessages": "3679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3680", "messages": "3681", "suppressedMessages": "3682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3683", "messages": "3684", "suppressedMessages": "3685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3686", "messages": "3687", "suppressedMessages": "3688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3689", "messages": "3690", "suppressedMessages": "3691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3692", "messages": "3693", "suppressedMessages": "3694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3695", "messages": "3696", "suppressedMessages": "3697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3698", "messages": "3699", "suppressedMessages": "3700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3701", "messages": "3702", "suppressedMessages": "3703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3704", "messages": "3705", "suppressedMessages": "3706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3707", "messages": "3708", "suppressedMessages": "3709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3710", "messages": "3711", "suppressedMessages": "3712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3713", "messages": "3714", "suppressedMessages": "3715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3716", "messages": "3717", "suppressedMessages": "3718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3719", "messages": "3720", "suppressedMessages": "3721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3722", "messages": "3723", "suppressedMessages": "3724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3725", "messages": "3726", "suppressedMessages": "3727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3728", "messages": "3729", "suppressedMessages": "3730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3731", "messages": "3732", "suppressedMessages": "3733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3734", "messages": "3735", "suppressedMessages": "3736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3737", "messages": "3738", "suppressedMessages": "3739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3740", "messages": "3741", "suppressedMessages": "3742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3743", "messages": "3744", "suppressedMessages": "3745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3746", "messages": "3747", "suppressedMessages": "3748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3749", "messages": "3750", "suppressedMessages": "3751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3752", "messages": "3753", "suppressedMessages": "3754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3755", "messages": "3756", "suppressedMessages": "3757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3758", "messages": "3759", "suppressedMessages": "3760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3761", "messages": "3762", "suppressedMessages": "3763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3764", "messages": "3765", "suppressedMessages": "3766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3767", "messages": "3768", "suppressedMessages": "3769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3770", "messages": "3771", "suppressedMessages": "3772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3773", "messages": "3774", "suppressedMessages": "3775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3776", "messages": "3777", "suppressedMessages": "3778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3779", "messages": "3780", "suppressedMessages": "3781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3782", "messages": "3783", "suppressedMessages": "3784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3785", "messages": "3786", "suppressedMessages": "3787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3788", "messages": "3789", "suppressedMessages": "3790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3791", "messages": "3792", "suppressedMessages": "3793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3794", "messages": "3795", "suppressedMessages": "3796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3797", "messages": "3798", "suppressedMessages": "3799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3800", "messages": "3801", "suppressedMessages": "3802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3803", "messages": "3804", "suppressedMessages": "3805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3806", "messages": "3807", "suppressedMessages": "3808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3809", "messages": "3810", "suppressedMessages": "3811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3812", "messages": "3813", "suppressedMessages": "3814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3815", "messages": "3816", "suppressedMessages": "3817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3818", "messages": "3819", "suppressedMessages": "3820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3821", "messages": "3822", "suppressedMessages": "3823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3824", "messages": "3825", "suppressedMessages": "3826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3827", "messages": "3828", "suppressedMessages": "3829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3830", "messages": "3831", "suppressedMessages": "3832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3833", "messages": "3834", "suppressedMessages": "3835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3836", "messages": "3837", "suppressedMessages": "3838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3839", "messages": "3840", "suppressedMessages": "3841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3842", "messages": "3843", "suppressedMessages": "3844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3845", "messages": "3846", "suppressedMessages": "3847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3848", "messages": "3849", "suppressedMessages": "3850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3851", "messages": "3852", "suppressedMessages": "3853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3854", "messages": "3855", "suppressedMessages": "3856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3857", "messages": "3858", "suppressedMessages": "3859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3860", "messages": "3861", "suppressedMessages": "3862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3863", "messages": "3864", "suppressedMessages": "3865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3866", "messages": "3867", "suppressedMessages": "3868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3869", "messages": "3870", "suppressedMessages": "3871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3872", "messages": "3873", "suppressedMessages": "3874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3875", "messages": "3876", "suppressedMessages": "3877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3878", "messages": "3879", "suppressedMessages": "3880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3881", "messages": "3882", "suppressedMessages": "3883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3884", "messages": "3885", "suppressedMessages": "3886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3887", "messages": "3888", "suppressedMessages": "3889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3890", "messages": "3891", "suppressedMessages": "3892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3893", "messages": "3894", "suppressedMessages": "3895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3896", "messages": "3897", "suppressedMessages": "3898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3899", "messages": "3900", "suppressedMessages": "3901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3902", "messages": "3903", "suppressedMessages": "3904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3905", "messages": "3906", "suppressedMessages": "3907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3908", "messages": "3909", "suppressedMessages": "3910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3911", "messages": "3912", "suppressedMessages": "3913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3914", "messages": "3915", "suppressedMessages": "3916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3917", "messages": "3918", "suppressedMessages": "3919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3920", "messages": "3921", "suppressedMessages": "3922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3923", "messages": "3924", "suppressedMessages": "3925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3926", "messages": "3927", "suppressedMessages": "3928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3929", "messages": "3930", "suppressedMessages": "3931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3932", "messages": "3933", "suppressedMessages": "3934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3935", "messages": "3936", "suppressedMessages": "3937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3938", "messages": "3939", "suppressedMessages": "3940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3941", "messages": "3942", "suppressedMessages": "3943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3944", "messages": "3945", "suppressedMessages": "3946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3947", "messages": "3948", "suppressedMessages": "3949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3950", "messages": "3951", "suppressedMessages": "3952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3953", "messages": "3954", "suppressedMessages": "3955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3956", "messages": "3957", "suppressedMessages": "3958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3959", "messages": "3960", "suppressedMessages": "3961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3962", "messages": "3963", "suppressedMessages": "3964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3965", "messages": "3966", "suppressedMessages": "3967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3968", "messages": "3969", "suppressedMessages": "3970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3971", "messages": "3972", "suppressedMessages": "3973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3974", "messages": "3975", "suppressedMessages": "3976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3977", "messages": "3978", "suppressedMessages": "3979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3980", "messages": "3981", "suppressedMessages": "3982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3983", "messages": "3984", "suppressedMessages": "3985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3986", "messages": "3987", "suppressedMessages": "3988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3989", "messages": "3990", "suppressedMessages": "3991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3992", "messages": "3993", "suppressedMessages": "3994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3995", "messages": "3996", "suppressedMessages": "3997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3998", "messages": "3999", "suppressedMessages": "4000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4001", "messages": "4002", "suppressedMessages": "4003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4004", "messages": "4005", "suppressedMessages": "4006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4007", "messages": "4008", "suppressedMessages": "4009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4010", "messages": "4011", "suppressedMessages": "4012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4013", "messages": "4014", "suppressedMessages": "4015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4016", "messages": "4017", "suppressedMessages": "4018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4019", "messages": "4020", "suppressedMessages": "4021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4022", "messages": "4023", "suppressedMessages": "4024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4025", "messages": "4026", "suppressedMessages": "4027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4028", "messages": "4029", "suppressedMessages": "4030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4031", "messages": "4032", "suppressedMessages": "4033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4034", "messages": "4035", "suppressedMessages": "4036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4037", "messages": "4038", "suppressedMessages": "4039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4040", "messages": "4041", "suppressedMessages": "4042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4043", "messages": "4044", "suppressedMessages": "4045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4046", "messages": "4047", "suppressedMessages": "4048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4049", "messages": "4050", "suppressedMessages": "4051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4052", "messages": "4053", "suppressedMessages": "4054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4055", "messages": "4056", "suppressedMessages": "4057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4058", "messages": "4059", "suppressedMessages": "4060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4061", "messages": "4062", "suppressedMessages": "4063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4064", "messages": "4065", "suppressedMessages": "4066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4067", "messages": "4068", "suppressedMessages": "4069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4070", "messages": "4071", "suppressedMessages": "4072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4073", "messages": "4074", "suppressedMessages": "4075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4076", "messages": "4077", "suppressedMessages": "4078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4079", "messages": "4080", "suppressedMessages": "4081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4082", "messages": "4083", "suppressedMessages": "4084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4085", "messages": "4086", "suppressedMessages": "4087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4088", "messages": "4089", "suppressedMessages": "4090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4091", "messages": "4092", "suppressedMessages": "4093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4094", "messages": "4095", "suppressedMessages": "4096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4097", "messages": "4098", "suppressedMessages": "4099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4100", "messages": "4101", "suppressedMessages": "4102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4103", "messages": "4104", "suppressedMessages": "4105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4106", "messages": "4107", "suppressedMessages": "4108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4109", "messages": "4110", "suppressedMessages": "4111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4112", "messages": "4113", "suppressedMessages": "4114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4115", "messages": "4116", "suppressedMessages": "4117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4118", "messages": "4119", "suppressedMessages": "4120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4121", "messages": "4122", "suppressedMessages": "4123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4124", "messages": "4125", "suppressedMessages": "4126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4127", "messages": "4128", "suppressedMessages": "4129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4130", "messages": "4131", "suppressedMessages": "4132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4133", "messages": "4134", "suppressedMessages": "4135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4136", "messages": "4137", "suppressedMessages": "4138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4139", "messages": "4140", "suppressedMessages": "4141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4142", "messages": "4143", "suppressedMessages": "4144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4145", "messages": "4146", "suppressedMessages": "4147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4148", "messages": "4149", "suppressedMessages": "4150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4151", "messages": "4152", "suppressedMessages": "4153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4154", "messages": "4155", "suppressedMessages": "4156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4157", "messages": "4158", "suppressedMessages": "4159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4160", "messages": "4161", "suppressedMessages": "4162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4163", "messages": "4164", "suppressedMessages": "4165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4166", "messages": "4167", "suppressedMessages": "4168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4169", "messages": "4170", "suppressedMessages": "4171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4172", "messages": "4173", "suppressedMessages": "4174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4175", "messages": "4176", "suppressedMessages": "4177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4178", "messages": "4179", "suppressedMessages": "4180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4181", "messages": "4182", "suppressedMessages": "4183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4184", "messages": "4185", "suppressedMessages": "4186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4187", "messages": "4188", "suppressedMessages": "4189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4190", "messages": "4191", "suppressedMessages": "4192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4193", "messages": "4194", "suppressedMessages": "4195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4196", "messages": "4197", "suppressedMessages": "4198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4199", "messages": "4200", "suppressedMessages": "4201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4202", "messages": "4203", "suppressedMessages": "4204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4205", "messages": "4206", "suppressedMessages": "4207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4208", "messages": "4209", "suppressedMessages": "4210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4211", "messages": "4212", "suppressedMessages": "4213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4214", "messages": "4215", "suppressedMessages": "4216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4217", "messages": "4218", "suppressedMessages": "4219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4220", "messages": "4221", "suppressedMessages": "4222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4223", "messages": "4224", "suppressedMessages": "4225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4226", "messages": "4227", "suppressedMessages": "4228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4229", "messages": "4230", "suppressedMessages": "4231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4232", "messages": "4233", "suppressedMessages": "4234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4235", "messages": "4236", "suppressedMessages": "4237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4238", "messages": "4239", "suppressedMessages": "4240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4241", "messages": "4242", "suppressedMessages": "4243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4244", "messages": "4245", "suppressedMessages": "4246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4247", "messages": "4248", "suppressedMessages": "4249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4250", "messages": "4251", "suppressedMessages": "4252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4253", "messages": "4254", "suppressedMessages": "4255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4256", "messages": "4257", "suppressedMessages": "4258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4259", "messages": "4260", "suppressedMessages": "4261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4262", "messages": "4263", "suppressedMessages": "4264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4265", "messages": "4266", "suppressedMessages": "4267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4268", "messages": "4269", "suppressedMessages": "4270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4271", "messages": "4272", "suppressedMessages": "4273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4274", "messages": "4275", "suppressedMessages": "4276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4277", "messages": "4278", "suppressedMessages": "4279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4280", "messages": "4281", "suppressedMessages": "4282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4283", "messages": "4284", "suppressedMessages": "4285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4286", "messages": "4287", "suppressedMessages": "4288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4289", "messages": "4290", "suppressedMessages": "4291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4292", "messages": "4293", "suppressedMessages": "4294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4295", "messages": "4296", "suppressedMessages": "4297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4298", "messages": "4299", "suppressedMessages": "4300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4301", "messages": "4302", "suppressedMessages": "4303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4304", "messages": "4305", "suppressedMessages": "4306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4307", "messages": "4308", "suppressedMessages": "4309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4310", "messages": "4311", "suppressedMessages": "4312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4313", "messages": "4314", "suppressedMessages": "4315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4316", "messages": "4317", "suppressedMessages": "4318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4319", "messages": "4320", "suppressedMessages": "4321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4322", "messages": "4323", "suppressedMessages": "4324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4325", "messages": "4326", "suppressedMessages": "4327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4328", "messages": "4329", "suppressedMessages": "4330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4331", "messages": "4332", "suppressedMessages": "4333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4334", "messages": "4335", "suppressedMessages": "4336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4337", "messages": "4338", "suppressedMessages": "4339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4340", "messages": "4341", "suppressedMessages": "4342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4343", "messages": "4344", "suppressedMessages": "4345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4346", "messages": "4347", "suppressedMessages": "4348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4349", "messages": "4350", "suppressedMessages": "4351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4352", "messages": "4353", "suppressedMessages": "4354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4355", "messages": "4356", "suppressedMessages": "4357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4358", "messages": "4359", "suppressedMessages": "4360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4361", "messages": "4362", "suppressedMessages": "4363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4364", "messages": "4365", "suppressedMessages": "4366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4367", "messages": "4368", "suppressedMessages": "4369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4370", "messages": "4371", "suppressedMessages": "4372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4373", "messages": "4374", "suppressedMessages": "4375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4376", "messages": "4377", "suppressedMessages": "4378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4379", "messages": "4380", "suppressedMessages": "4381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4382", "messages": "4383", "suppressedMessages": "4384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4385", "messages": "4386", "suppressedMessages": "4387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4388", "messages": "4389", "suppressedMessages": "4390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4391", "messages": "4392", "suppressedMessages": "4393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4394", "messages": "4395", "suppressedMessages": "4396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4397", "messages": "4398", "suppressedMessages": "4399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4400", "messages": "4401", "suppressedMessages": "4402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4403", "messages": "4404", "suppressedMessages": "4405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4406", "messages": "4407", "suppressedMessages": "4408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4409", "messages": "4410", "suppressedMessages": "4411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4412", "messages": "4413", "suppressedMessages": "4414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4415", "messages": "4416", "suppressedMessages": "4417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4418", "messages": "4419", "suppressedMessages": "4420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "4421", "messages": "4422", "suppressedMessages": "4423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "4424", "messages": "4425", "suppressedMessages": "4426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\DailyVisitTrendChart.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedAnalyticsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedChartCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedEngagementMetricsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\EnhancedVisitMetricsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\HourlyVisitTrendChart.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\MonthlyVisitTrendChart.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\components\\PremiumFeatureLock.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\analytics\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customAdUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\customHeaderUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeSpecificHeaderUpload.ts", ["4427"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts", ["4428"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomAdUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CustomBrandingSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ThemeSpecificHeaderUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedSubscriptionStatus.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedSubscriptionStatus.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts", ["4429", "4430"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BillingToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\BusinessPlanSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\ConfirmationDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\DialogManager.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedActionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedCurrentPlanSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedGlowButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedInvoiceHistoryCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanPageWithManager.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedPlanSelectionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedSubscriptionDetailsCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\EnhancedTrialAlert.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\FirstTimePaidPlanDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PaymentMethodLimitationsDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\PlanPageContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\RealtimePlanPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SimplifiedPlanActionDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\DialogComponents.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EligiblePaymentMethodsCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedPaymentHistoryCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\EnhancedSubscriptionActionCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\hooks.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernCancellationDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernRefundDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernSubscriptionStatusCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\ModernTabs.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionManager.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionProcessingIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\SubscriptionTabsToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\subscription-manager\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusBadge.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionStatusIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\SubscriptionTabContent.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialManagement.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\TrialSubscriptionWarningDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\UpiPaymentMethodWarning.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\components\\WebhookWaitingIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\context\\SubscriptionProcessingContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionHandler.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useSubscriptionLogic.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\hooks\\useUrlParameterHandler.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\plan\\PlanPageWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts", ["4431", "4432", "4433", "4434", "4435", "4436"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts", ["4437", "4438"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts", ["4439", "4440", "4441"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts", ["4442"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx", ["4443", "4444", "4445", "4446"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx", ["4447"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx", ["4448", "4449", "4450"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx", ["4451", "4452"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx", ["4453", "4454"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx", ["4455"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx", ["4456"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\actions.ts", ["4457", "4458"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts", ["4459", "4460"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts", ["4461", "4462", "4463", "4464", "4465", "4466"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts", ["4467", "4468", "4469", "4470"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts", ["4471", "4472", "4473", "4474"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts", ["4475", "4476"], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx", ["4477"], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\BillingToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PricingSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\FeatureComparisonTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx", [], ["4478"], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\PlanComparisonSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\AnimatedTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\animations\\PricingAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCards.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingCTA.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingFAQ.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingHero.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\EnhancedPricingToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\components\\FeatureComparisonTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\EnhancedPricingPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\pricing\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\AnalyticsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\analytics\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts", ["4479", "4480", "4481", "4482"], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\PlanSelectionStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\admin\\fix-subscription-inconsistency\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts", ["4483", "4484"], [], "C:\\web-app\\dukancard\\app\\api\\health\\subscription\\route.ts", ["4485", "4486"], [], "C:\\web-app\\dukancard\\app\\api\\razorpay\\key\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscription\\[id]\\payments\\route.ts", ["4487", "4488"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\centralized\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\list\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\my\\route.ts", ["4489", "4490"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\cancel\\route.ts", ["4491", "4492"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\invoices\\route.ts", ["4493", "4494"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pause\\route.ts", ["4495", "4496", "4497", "4498"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\payments\\[paymentId]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\pending-update\\route.ts", ["4499", "4500", "4501", "4502", "4503", "4504"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\databaseOperations.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\errorHandlers.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\paymentHelpers.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\razorpayApi.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\refund\\utils\\validators.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\resume\\route.ts", ["4505", "4506"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\route.ts", ["4507", "4508"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\scheduled-changes\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\switch-with-new-payment\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\[id]\\update\\route.ts", ["4509", "4510", "4511"], [], "C:\\web-app\\dukancard\\app\\api\\test\\subscription-flow\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\create\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\delete\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\get\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\list\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\retry\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\webhooks\\razorpay\\update\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\auth\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Footer.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Header.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx", [], ["4512"], "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PricingCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PricingCardContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx", [], ["4513"], "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx", [], [], "C:\\web-app\\dukancard\\app\\context\\PaymentMethodLimitationsContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts", ["4514", "4515", "4516"], ["4517"], "C:\\web-app\\dukancard\\app\\locality\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx", [], ["4518"], "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\products\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\robots.ts", [], [], "C:\\web-app\\dukancard\\app\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts", ["4519", "4520"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\VisitTracker.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx", ["4521"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx", ["4522", "4523"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts", ["4524"], ["4525"], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx", ["4526", "4527", "4528", "4529"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx", ["4530"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx", [], [], "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx", ["4531"], [], "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx", ["4532", "4533"], [], "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx", ["4534", "4535", "4536", "4537"], [], "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\badge.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\card.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\chart.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\command.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\form.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\label.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\popover.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\progress.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\select.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\separator.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\slider.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\switch.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\table.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\toast.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts", [], [], "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx", [], [], "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts", ["4538", "4539", "4540"], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts", [], ["4541", "4542"], "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts", ["4543"], [], "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts", ["4544", "4545", "4546", "4547", "4548", "4549", "4550", "4551"], [], "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts", ["4552", "4553", "4554", "4555", "4556"], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts", ["4557", "4558", "4559"], [], "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts", ["4560", "4561", "4562", "4563"], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts", ["4564", "4565", "4566", "4567"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\activateTrial.ts", ["4568", "4569", "4570", "4571", "4572", "4573", "4574", "4575"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\centralized.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\confirm.ts", ["4576", "4577", "4578", "4579", "4580", "4581", "4582", "4583", "4584", "4585"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\create.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\cancel.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\change.ts", ["4586", "4587", "4588"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\manage.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\schedule.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage\\switch.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\manage.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\payment.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\status.ts", ["4589", "4590", "4591", "4592", "4593", "4594", "4595"], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\subscription.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\user\\getUserAndProfile.ts", [], [], "C:\\web-app\\dukancard\\lib\\api\\response.ts", [], [], "C:\\web-app\\dukancard\\lib\\cardDownloader.ts", [], [], "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\categories.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\plans.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\states.ts", [], [], "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts", [], [], "C:\\web-app\\dukancard\\lib\\csrf.ts", [], [], "C:\\web-app\\dukancard\\lib\\errorHandling.ts", [], ["4596", "4597"], "C:\\web-app\\dukancard\\lib\\PricingPlans.ts", [], [], "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts", [], [], "C:\\web-app\\dukancard\\lib\\rateLimiter.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\razorpayClient.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\create.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\get.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\customer\\update.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\invoice.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\getPayment.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\payment.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\plan.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\cancel.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\create.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\get.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\scheduled.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\subscription\\update.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\services\\webhook.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\types\\api.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\auth.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\utils\\loadRazorpaySDK.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\errorTracking.ts", ["4598", "4599", "4600"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\eventManager.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\subscriptionManager.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\eventOrderValidator.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\core\\validation\\stateTransitionValidator.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\mainHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\moreSubscriptionHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\paymentHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\refundHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-constants.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-db-updater.ts", ["4601", "4602", "4603"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-manager.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscription-state-validator.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionActivated.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionAuthenticated.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCancelled.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCharged.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionCompleted.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionExpired.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionHalted.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionPending.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\handleSubscriptionUpdated.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionEventHandlers\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\subscriptionHandlers.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\transactionUtils.ts", ["4604", "4605", "4606", "4607", "4608", "4609", "4610", "4611", "4612", "4613"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhook-utils.ts", [], ["4614"], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handlers\\webhookProcessor.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\handleWebhook.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\idempotency.ts", ["4615"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\monitoring.ts", ["4616", "4617", "4618"], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\razorpay\\webhooks\\validation.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts", [], ["4619"], "C:\\web-app\\dukancard\\lib\\services\\socialService.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\subscription.ts", ["4620"], [], "C:\\web-app\\dukancard\\lib\\site-config.ts", [], [], "C:\\web-app\\dukancard\\lib\\siteContent.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\edge-validation.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowManager.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\SubscriptionFlowTester.ts", [], [], "C:\\web-app\\dukancard\\lib\\subscription\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\database.ts", ["4621"], [], "C:\\web-app\\dukancard\\lib\\testing\\mswHandlers.ts", [], ["4622", "4623"], "C:\\web-app\\dukancard\\lib\\testing\\testDataFactories.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\testUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\testing\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\activities.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\api.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\blog.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\subscription.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\seo.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts", ["4624", "4625"], [], "C:\\web-app\\dukancard\\lib\\utils.ts", [], [], {"ruleId": "4626", "severity": 1, "message": "4627", "line": 126, "column": 14, "nodeType": "4628", "messageId": "4629", "endLine": 126, "endColumn": 17, "suggestions": "4630"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 95, "column": 50, "nodeType": "4628", "messageId": "4629", "endLine": 95, "endColumn": 53, "suggestions": "4631"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 144, "column": 44, "nodeType": "4628", "messageId": "4629", "endLine": 144, "endColumn": 47, "suggestions": "4632"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 285, "column": 44, "nodeType": "4628", "messageId": "4629", "endLine": 285, "endColumn": 47, "suggestions": "4633"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 137, "column": 41, "nodeType": "4628", "messageId": "4629", "endLine": 137, "endColumn": 44, "suggestions": "4634"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 141, "column": 21, "nodeType": "4628", "messageId": "4629", "endLine": 141, "endColumn": 24, "suggestions": "4635"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 262, "column": 65, "nodeType": "4628", "messageId": "4629", "endLine": 262, "endColumn": 68, "suggestions": "4636"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 266, "column": 21, "nodeType": "4628", "messageId": "4629", "endLine": 266, "endColumn": 24, "suggestions": "4637"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 267, "column": 41, "nodeType": "4628", "messageId": "4629", "endLine": 267, "endColumn": 44, "suggestions": "4638"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 267, "column": 95, "nodeType": "4628", "messageId": "4629", "endLine": 267, "endColumn": 98, "suggestions": "4639"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 66, "column": 35, "nodeType": "4628", "messageId": "4629", "endLine": 66, "endColumn": 38, "suggestions": "4640"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 67, "column": 35, "nodeType": "4628", "messageId": "4629", "endLine": 67, "endColumn": 38, "suggestions": "4641"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 69, "column": 54, "nodeType": "4628", "messageId": "4629", "endLine": 69, "endColumn": 57, "suggestions": "4642"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 110, "column": 52, "nodeType": "4628", "messageId": "4629", "endLine": 110, "endColumn": 55, "suggestions": "4643"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 152, "column": 60, "nodeType": "4628", "messageId": "4629", "endLine": 152, "endColumn": 63, "suggestions": "4644"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 114, "column": 20, "nodeType": "4628", "messageId": "4629", "endLine": 114, "endColumn": 23, "suggestions": "4645"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 142, "column": 33, "nodeType": "4628", "messageId": "4629", "endLine": 142, "endColumn": 36, "suggestions": "4646"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 142, "column": 65, "nodeType": "4628", "messageId": "4629", "endLine": 142, "endColumn": 68, "suggestions": "4647"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 143, "column": 31, "nodeType": "4628", "messageId": "4629", "endLine": 143, "endColumn": 34, "suggestions": "4648"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 143, "column": 63, "nodeType": "4628", "messageId": "4629", "endLine": 143, "endColumn": 66, "suggestions": "4649"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 424, "column": 63, "nodeType": "4628", "messageId": "4629", "endLine": 424, "endColumn": 66, "suggestions": "4650"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 291, "column": 70, "nodeType": "4628", "messageId": "4629", "endLine": 291, "endColumn": 73, "suggestions": "4651"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 295, "column": 31, "nodeType": "4628", "messageId": "4629", "endLine": 295, "endColumn": 34, "suggestions": "4652"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 308, "column": 27, "nodeType": "4628", "messageId": "4629", "endLine": 308, "endColumn": 30, "suggestions": "4653"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 70, "column": 37, "nodeType": "4628", "messageId": "4629", "endLine": 70, "endColumn": 40, "suggestions": "4654"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 106, "column": 55, "nodeType": "4628", "messageId": "4629", "endLine": 106, "endColumn": 58, "suggestions": "4655"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 292, "column": 65, "nodeType": "4628", "messageId": "4629", "endLine": 292, "endColumn": 68, "suggestions": "4656"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 429, "column": 79, "nodeType": "4628", "messageId": "4629", "endLine": 429, "endColumn": 82, "suggestions": "4657"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 191, "column": 54, "nodeType": "4628", "messageId": "4629", "endLine": 191, "endColumn": 57, "suggestions": "4658"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 115, "column": 73, "nodeType": "4628", "messageId": "4629", "endLine": 115, "endColumn": 76, "suggestions": "4659"}, {"ruleId": "4660", "severity": 1, "message": "4661", "line": 3, "column": 26, "nodeType": "4662", "messageId": "4663", "endLine": 3, "endColumn": 39, "suggestions": "4664"}, {"ruleId": "4665", "severity": 1, "message": "4661", "line": 3, "column": 26, "nodeType": null, "messageId": "4663", "endLine": 3, "endColumn": 39}, {"ruleId": "4660", "severity": 1, "message": "4666", "line": 512, "column": 13, "nodeType": "4662", "messageId": "4663", "endLine": 512, "endColumn": 25}, {"ruleId": "4665", "severity": 1, "message": "4666", "line": 512, "column": 13, "nodeType": null, "messageId": "4663", "endLine": 512, "endColumn": 25}, {"ruleId": "4660", "severity": 1, "message": "4667", "line": 5, "column": 3, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 22, "suggestions": "4668"}, {"ruleId": "4665", "severity": 1, "message": "4667", "line": 5, "column": 3, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 22}, {"ruleId": "4660", "severity": 1, "message": "4669", "line": 6, "column": 3, "nodeType": "4662", "messageId": "4663", "endLine": 6, "endColumn": 18, "suggestions": "4670"}, {"ruleId": "4665", "severity": 1, "message": "4669", "line": 6, "column": 3, "nodeType": null, "messageId": "4663", "endLine": 6, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4671", "line": 7, "column": 3, "nodeType": "4662", "messageId": "4663", "endLine": 7, "endColumn": 26, "suggestions": "4672"}, {"ruleId": "4665", "severity": 1, "message": "4671", "line": 7, "column": 3, "nodeType": null, "messageId": "4663", "endLine": 7, "endColumn": 26}, {"ruleId": "4660", "severity": 1, "message": "4673", "line": 4, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 4, "endColumn": 24, "suggestions": "4674"}, {"ruleId": "4665", "severity": 1, "message": "4673", "line": 4, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 4, "endColumn": 24}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4676"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4673", "line": 4, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 4, "endColumn": 24, "suggestions": "4677"}, {"ruleId": "4665", "severity": 1, "message": "4673", "line": 4, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 4, "endColumn": 24}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4678"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 89, "column": 59, "nodeType": "4628", "messageId": "4629", "endLine": 89, "endColumn": 62, "suggestions": "4679"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 90, "column": 49, "nodeType": "4628", "messageId": "4629", "endLine": 90, "endColumn": 52, "suggestions": "4680"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 79, "column": 37, "nodeType": "4628", "messageId": "4629", "endLine": 79, "endColumn": 40, "suggestions": "4681"}, {"ruleId": "4682", "severity": 1, "message": "4683", "line": 282, "column": 6, "nodeType": "4684", "endLine": 282, "endColumn": 8, "suggestions": "4685", "suppressions": "4686"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 397, "column": 19, "nodeType": "4628", "messageId": "4629", "endLine": 397, "endColumn": 22, "suggestions": "4687"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 398, "column": 67, "nodeType": "4628", "messageId": "4629", "endLine": 398, "endColumn": 70, "suggestions": "4688"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 399, "column": 20, "nodeType": "4628", "messageId": "4629", "endLine": 399, "endColumn": 23, "suggestions": "4689"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 405, "column": 32, "nodeType": "4628", "messageId": "4629", "endLine": 405, "endColumn": 35, "suggestions": "4690"}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 4, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 4, "endColumn": 18, "suggestions": "4691"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 4, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 4, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 4, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 4, "endColumn": 18, "suggestions": "4692"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 4, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 4, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4693"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4694"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4695"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4696"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4697"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 168, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 168, "endColumn": 45, "suggestions": "4698"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 169, "column": 113, "nodeType": "4628", "messageId": "4629", "endLine": 169, "endColumn": 116, "suggestions": "4699"}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4700"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4701", "line": 59, "column": 11, "nodeType": "4662", "messageId": "4663", "endLine": 59, "endColumn": 15, "suggestions": "4702"}, {"ruleId": "4665", "severity": 1, "message": "4701", "line": 59, "column": 11, "nodeType": null, "messageId": "4663", "endLine": 59, "endColumn": 15}, {"ruleId": "4660", "severity": 1, "message": "4703", "line": 60, "column": 11, "nodeType": "4662", "messageId": "4663", "endLine": 60, "endColumn": 16, "suggestions": "4704"}, {"ruleId": "4665", "severity": 1, "message": "4703", "line": 60, "column": 11, "nodeType": null, "messageId": "4663", "endLine": 60, "endColumn": 16}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 166, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 166, "endColumn": 45, "suggestions": "4705"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 167, "column": 113, "nodeType": "4628", "messageId": "4629", "endLine": 167, "endColumn": 116, "suggestions": "4706"}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4707"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 246, "column": 44, "nodeType": "4628", "messageId": "4629", "endLine": 246, "endColumn": 47, "suggestions": "4708"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 254, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 254, "endColumn": 45, "suggestions": "4709"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 255, "column": 113, "nodeType": "4628", "messageId": "4629", "endLine": 255, "endColumn": 116, "suggestions": "4710"}, {"ruleId": "4711", "severity": 1, "message": "4712", "line": 25, "column": 9, "nodeType": "4713", "endLine": 31, "endColumn": 11, "suppressions": "4714"}, {"ruleId": "4682", "severity": 1, "message": "4715", "line": 53, "column": 6, "nodeType": "4684", "endLine": 53, "endColumn": 46, "suggestions": "4716", "suppressions": "4717"}, {"ruleId": "4660", "severity": 1, "message": "4718", "line": 132, "column": 15, "nodeType": "4662", "messageId": "4663", "endLine": 132, "endColumn": 33, "suggestions": "4719"}, {"ruleId": "4665", "severity": 1, "message": "4718", "line": 132, "column": 15, "nodeType": null, "messageId": "4663", "endLine": 132, "endColumn": 33}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 149, "column": 54, "nodeType": "4628", "messageId": "4629", "endLine": 149, "endColumn": 57, "suggestions": "4720"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 158, "column": 57, "nodeType": "4628", "messageId": "4629", "endLine": 158, "endColumn": 60, "suggestions": "4721", "suppressions": "4722"}, {"ruleId": "4682", "severity": 1, "message": "4723", "line": 172, "column": 6, "nodeType": "4684", "endLine": 172, "endColumn": 8, "suggestions": "4724", "suppressions": "4725"}, {"ruleId": "4660", "severity": 1, "message": "4726", "line": 8, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 8, "endColumn": 16, "suggestions": "4727"}, {"ruleId": "4665", "severity": 1, "message": "4726", "line": 8, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 8, "endColumn": 16}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 92, "column": 35, "nodeType": "4628", "messageId": "4629", "endLine": 92, "endColumn": 38, "suggestions": "4728"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 289, "column": 57, "nodeType": "4628", "messageId": "4629", "endLine": 289, "endColumn": 60, "suggestions": "4729"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 290, "column": 47, "nodeType": "4628", "messageId": "4629", "endLine": 290, "endColumn": 50, "suggestions": "4730"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 244, "column": 17, "nodeType": "4628", "messageId": "4629", "endLine": 244, "endColumn": 20, "suggestions": "4731"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 441, "column": 53, "nodeType": "4628", "messageId": "4629", "endLine": 441, "endColumn": 56, "suggestions": "4732", "suppressions": "4733"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 67, "column": 79, "nodeType": "4628", "messageId": "4629", "endLine": 67, "endColumn": 82, "suggestions": "4734"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 117, "column": 81, "nodeType": "4628", "messageId": "4629", "endLine": 117, "endColumn": 84, "suggestions": "4735"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 156, "column": 88, "nodeType": "4628", "messageId": "4629", "endLine": 156, "endColumn": 91, "suggestions": "4736"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 169, "column": 69, "nodeType": "4628", "messageId": "4629", "endLine": 169, "endColumn": 72, "suggestions": "4737"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 205, "column": 55, "nodeType": "4628", "messageId": "4629", "endLine": 205, "endColumn": 58, "suggestions": "4738"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 45, "column": 21, "nodeType": "4628", "messageId": "4629", "endLine": 45, "endColumn": 24, "suggestions": "4739"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 78, "column": 21, "nodeType": "4628", "messageId": "4629", "endLine": 78, "endColumn": 24, "suggestions": "4740"}, {"ruleId": "4682", "severity": 1, "message": "4741", "line": 115, "column": 6, "nodeType": "4684", "endLine": 115, "endColumn": 8, "suggestions": "4742"}, {"ruleId": "4660", "severity": 1, "message": "4743", "line": 30, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 30, "endColumn": 25, "suggestions": "4744"}, {"ruleId": "4665", "severity": 1, "message": "4743", "line": 30, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 30, "endColumn": 25}, {"ruleId": "4660", "severity": 1, "message": "4745", "line": 31, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 31, "endColumn": 22, "suggestions": "4746"}, {"ruleId": "4665", "severity": 1, "message": "4745", "line": 31, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 31, "endColumn": 22}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 115, "column": 23, "nodeType": "4628", "messageId": "4629", "endLine": 115, "endColumn": 26, "suggestions": "4747"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 122, "column": 24, "nodeType": "4628", "messageId": "4629", "endLine": 122, "endColumn": 27, "suggestions": "4748"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 123, "column": 19, "nodeType": "4628", "messageId": "4629", "endLine": 123, "endColumn": 22, "suggestions": "4749"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 9, "column": 37, "nodeType": "4628", "messageId": "4629", "endLine": 9, "endColumn": 40, "suggestions": "4750", "suppressions": "4751"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 9, "column": 67, "nodeType": "4628", "messageId": "4629", "endLine": 9, "endColumn": 70, "suggestions": "4752", "suppressions": "4753"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 289, "column": 56, "nodeType": "4628", "messageId": "4629", "endLine": 289, "endColumn": 59, "suggestions": "4754"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 51, "column": 24, "nodeType": "4628", "messageId": "4629", "endLine": 51, "endColumn": 27, "suggestions": "4755"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 51, "column": 68, "nodeType": "4628", "messageId": "4629", "endLine": 51, "endColumn": 71, "suggestions": "4756"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 138, "column": 24, "nodeType": "4628", "messageId": "4629", "endLine": 138, "endColumn": 27, "suggestions": "4757"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 138, "column": 68, "nodeType": "4628", "messageId": "4629", "endLine": 138, "endColumn": 71, "suggestions": "4758"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 233, "column": 24, "nodeType": "4628", "messageId": "4629", "endLine": 233, "endColumn": 27, "suggestions": "4759"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 233, "column": 68, "nodeType": "4628", "messageId": "4629", "endLine": 233, "endColumn": 71, "suggestions": "4760"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 344, "column": 24, "nodeType": "4628", "messageId": "4629", "endLine": 344, "endColumn": 27, "suggestions": "4761"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 344, "column": 68, "nodeType": "4628", "messageId": "4629", "endLine": 344, "endColumn": 71, "suggestions": "4762"}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 3, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 3, "endColumn": 18, "suggestions": "4763"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 3, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 3, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4726", "line": 3, "column": 20, "nodeType": "4662", "messageId": "4663", "endLine": 3, "endColumn": 26, "suggestions": "4764"}, {"ruleId": "4665", "severity": 1, "message": "4726", "line": 3, "column": 20, "nodeType": null, "messageId": "4663", "endLine": 3, "endColumn": 26}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 92, "column": 43, "nodeType": "4628", "messageId": "4629", "endLine": 92, "endColumn": 46, "suggestions": "4765"}, {"ruleId": "4660", "severity": 1, "message": "4766", "line": 38, "column": 9, "nodeType": "4662", "messageId": "4663", "endLine": 38, "endColumn": 17, "suggestions": "4767"}, {"ruleId": "4665", "severity": 1, "message": "4766", "line": 38, "column": 9, "nodeType": null, "messageId": "4663", "endLine": 38, "endColumn": 17}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 124, "column": 70, "nodeType": "4628", "messageId": "4629", "endLine": 124, "endColumn": 73, "suggestions": "4768"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 95, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 95, "endColumn": 45, "suggestions": "4769"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 130, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 130, "endColumn": 45, "suggestions": "4770"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 324, "column": 41, "nodeType": "4628", "messageId": "4629", "endLine": 324, "endColumn": 44, "suggestions": "4771"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 334, "column": 41, "nodeType": "4628", "messageId": "4629", "endLine": 334, "endColumn": 44, "suggestions": "4772"}, {"ruleId": "4660", "severity": 1, "message": "4673", "line": 4, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 4, "endColumn": 24, "suggestions": "4773"}, {"ruleId": "4665", "severity": 1, "message": "4673", "line": 4, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 4, "endColumn": 24}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4774"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4660", "severity": 1, "message": "4673", "line": 2, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 2, "endColumn": 24, "suggestions": "4775"}, {"ruleId": "4665", "severity": 1, "message": "4673", "line": 2, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 2, "endColumn": 24}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 3, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 3, "endColumn": 18, "suggestions": "4776"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 3, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 3, "endColumn": 18}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 99, "column": 34, "nodeType": "4628", "messageId": "4629", "endLine": 99, "endColumn": 37, "suggestions": "4777"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 106, "column": 12, "nodeType": "4628", "messageId": "4629", "endLine": 106, "endColumn": 15, "suggestions": "4778"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 110, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 110, "endColumn": 45, "suggestions": "4779"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 111, "column": 91, "nodeType": "4628", "messageId": "4629", "endLine": 111, "endColumn": 94, "suggestions": "4780"}, {"ruleId": "4660", "severity": 1, "message": "4673", "line": 4, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 4, "endColumn": 24, "suggestions": "4781"}, {"ruleId": "4665", "severity": 1, "message": "4673", "line": 4, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 4, "endColumn": 24}, {"ruleId": "4660", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": "4662", "messageId": "4663", "endLine": 5, "endColumn": 18, "suggestions": "4782"}, {"ruleId": "4665", "severity": 1, "message": "4675", "line": 5, "column": 10, "nodeType": null, "messageId": "4663", "endLine": 5, "endColumn": 18}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 414, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 414, "endColumn": 45, "suggestions": "4783"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 418, "column": 44, "nodeType": "4628", "messageId": "4629", "endLine": 418, "endColumn": 47, "suggestions": "4784"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 419, "column": 121, "nodeType": "4628", "messageId": "4629", "endLine": 419, "endColumn": 124, "suggestions": "4785"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 449, "column": 32, "nodeType": "4628", "messageId": "4629", "endLine": 449, "endColumn": 35, "suggestions": "4786"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 453, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 453, "endColumn": 45, "suggestions": "4787"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 454, "column": 100, "nodeType": "4628", "messageId": "4629", "endLine": 454, "endColumn": 103, "suggestions": "4788"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 159, "column": 10, "nodeType": "4628", "messageId": "4629", "endLine": 159, "endColumn": 13, "suggestions": "4789"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 163, "column": 40, "nodeType": "4628", "messageId": "4629", "endLine": 163, "endColumn": 43, "suggestions": "4790"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 164, "column": 94, "nodeType": "4628", "messageId": "4629", "endLine": 164, "endColumn": 97, "suggestions": "4791"}, {"ruleId": "4660", "severity": 1, "message": "4766", "line": 28, "column": 9, "nodeType": "4662", "messageId": "4663", "endLine": 28, "endColumn": 17, "suggestions": "4792"}, {"ruleId": "4665", "severity": 1, "message": "4766", "line": 28, "column": 9, "nodeType": null, "messageId": "4663", "endLine": 28, "endColumn": 17}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 121, "column": 12, "nodeType": "4628", "messageId": "4629", "endLine": 121, "endColumn": 15, "suggestions": "4793"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 125, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 125, "endColumn": 45, "suggestions": "4794"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 126, "column": 113, "nodeType": "4628", "messageId": "4629", "endLine": 126, "endColumn": 116, "suggestions": "4795"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 240, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 240, "endColumn": 45, "suggestions": "4796"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 241, "column": 113, "nodeType": "4628", "messageId": "4629", "endLine": 241, "endColumn": 116, "suggestions": "4797"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 6, "column": 18, "nodeType": "4628", "messageId": "4629", "endLine": 6, "endColumn": 21, "suggestions": "4798", "suppressions": "4799"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 13, "column": 10, "nodeType": "4628", "messageId": "4629", "endLine": 13, "endColumn": 13, "suggestions": "4800", "suppressions": "4801"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 57, "column": 27, "nodeType": "4628", "messageId": "4629", "endLine": 57, "endColumn": 30, "suggestions": "4802"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 112, "column": 29, "nodeType": "4628", "messageId": "4629", "endLine": 112, "endColumn": 32, "suggestions": "4803"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 157, "column": 12, "nodeType": "4628", "messageId": "4629", "endLine": 157, "endColumn": 15, "suggestions": "4804"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 386, "column": 60, "nodeType": "4628", "messageId": "4629", "endLine": 386, "endColumn": 63, "suggestions": "4805"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 398, "column": 21, "nodeType": "4628", "messageId": "4629", "endLine": 398, "endColumn": 24, "suggestions": "4806"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 402, "column": 29, "nodeType": "4628", "messageId": "4629", "endLine": 402, "endColumn": 32, "suggestions": "4807"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 149, "column": 58, "nodeType": "4628", "messageId": "4629", "endLine": 149, "endColumn": 61, "suggestions": "4808"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 153, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 153, "endColumn": 45, "suggestions": "4809"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 154, "column": 130, "nodeType": "4628", "messageId": "4629", "endLine": 154, "endColumn": 133, "suggestions": "4810"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 157, "column": 89, "nodeType": "4628", "messageId": "4629", "endLine": 157, "endColumn": 92, "suggestions": "4811"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 279, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 279, "endColumn": 45, "suggestions": "4812"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 280, "column": 171, "nodeType": "4628", "messageId": "4629", "endLine": 280, "endColumn": 174, "suggestions": "4813"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 283, "column": 87, "nodeType": "4628", "messageId": "4629", "endLine": 283, "endColumn": 90, "suggestions": "4814"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 371, "column": 42, "nodeType": "4628", "messageId": "4629", "endLine": 371, "endColumn": 45, "suggestions": "4815"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 372, "column": 138, "nodeType": "4628", "messageId": "4629", "endLine": 372, "endColumn": 141, "suggestions": "4816"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 375, "column": 93, "nodeType": "4628", "messageId": "4629", "endLine": 375, "endColumn": 96, "suggestions": "4817"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 12, "column": 50, "nodeType": "4628", "messageId": "4629", "endLine": 12, "endColumn": 53, "suggestions": "4818", "suppressions": "4819"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 121, "column": 14, "nodeType": "4628", "messageId": "4629", "endLine": 121, "endColumn": 17, "suggestions": "4820"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 64, "column": 61, "nodeType": "4628", "messageId": "4629", "endLine": 64, "endColumn": 64, "suggestions": "4821"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 69, "column": 87, "nodeType": "4628", "messageId": "4629", "endLine": 69, "endColumn": 90, "suggestions": "4822"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 206, "column": 37, "nodeType": "4628", "messageId": "4629", "endLine": 206, "endColumn": 40, "suggestions": "4823"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 55, "column": 31, "nodeType": "4628", "messageId": "4629", "endLine": 55, "endColumn": 34, "suggestions": "4824", "suppressions": "4825"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 257, "column": 40, "nodeType": "4628", "messageId": "4629", "endLine": 257, "endColumn": 43, "suggestions": "4826"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 150, "column": 12, "nodeType": "4628", "messageId": "4629", "endLine": 150, "endColumn": 15, "suggestions": "4827"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 131, "column": 60, "nodeType": "4628", "messageId": "4629", "endLine": 131, "endColumn": 63, "suggestions": "4828", "suppressions": "4829"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 138, "column": 61, "nodeType": "4628", "messageId": "4629", "endLine": 138, "endColumn": 64, "suggestions": "4830", "suppressions": "4831"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 318, "column": 47, "nodeType": "4628", "messageId": "4629", "endLine": 318, "endColumn": 50, "suggestions": "4832"}, {"ruleId": "4626", "severity": 1, "message": "4627", "line": 334, "column": 50, "nodeType": "4628", "messageId": "4629", "endLine": 334, "endColumn": 53, "suggestions": "4833"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["4834", "4835"], ["4836", "4837"], ["4838", "4839"], ["4840", "4841"], ["4842", "4843"], ["4844", "4845"], ["4846", "4847"], ["4848", "4849"], ["4850", "4851"], ["4852", "4853"], ["4854", "4855"], ["4856", "4857"], ["4858", "4859"], ["4860", "4861"], ["4862", "4863"], ["4864", "4865"], ["4866", "4867"], ["4868", "4869"], ["4870", "4871"], ["4872", "4873"], ["4874", "4875"], ["4876", "4877"], ["4878", "4879"], ["4880", "4881"], ["4882", "4883"], ["4884", "4885"], ["4886", "4887"], ["4888", "4889"], ["4890", "4891"], ["4892", "4893"], "no-unused-vars", "'ReviewsResult' is defined but never used. Allowed unused vars must match /^_/u.", "Identifier", "unusedVar", ["4894"], "@typescript-eslint/no-unused-vars", "'createClient' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'FollowerWithProfile' is defined but never used. Allowed unused vars must match /^_/u.", ["4895"], "'FollowersResult' is defined but never used. Allowed unused vars must match /^_/u.", ["4896"], "'SubscriptionWithProfile' is defined but never used. Allowed unused vars must match /^_/u.", ["4897"], "'SupabaseClient' is defined but never used. Allowed unused vars must match /^_/u.", ["4898"], "'Database' is defined but never used. Allowed unused vars must match /^_/u.", ["4899"], ["4900"], ["4901"], ["4902", "4903"], ["4904", "4905"], ["4906", "4907"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'initialBusinessName', 'initialCategory', 'initialCity', 'initialLocality', 'initialPincode', 'performSearch', 'productFilterBy', and 'productSortBy'. Either include them or remove the dependency array.", "ArrayExpression", ["4908"], ["4909"], ["4910", "4911"], ["4912", "4913"], ["4914", "4915"], ["4916", "4917"], ["4918"], ["4919"], ["4920"], ["4921"], ["4922"], ["4923"], ["4924"], ["4925", "4926"], ["4927", "4928"], ["4929"], "'page' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4930"], "'count' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4931"], ["4932", "4933"], ["4934", "4935"], ["4936"], ["4937", "4938"], ["4939", "4940"], ["4941", "4942"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["4943"], "React Hook useEffect has a missing dependency: 'loadReviews'. Either include it or remove the dependency array.", ["4944"], ["4945"], "'ProductServiceData' is defined but never used. Allowed unused vars must match /^_/u.", ["4946"], ["4947", "4948"], ["4949", "4950"], ["4951"], "React Hook useEffect has missing dependencies: 'isSearching', 'performSearch', 'products.length', and 'viewType'. Either include them or remove the dependency array.", ["4952"], ["4953"], "'Tables' is defined but never used. Allowed unused vars must match /^_/u.", ["4954"], ["4955", "4956"], ["4957", "4958"], ["4959", "4960"], ["4961", "4962"], ["4963", "4964"], ["4965"], ["4966", "4967"], ["4968", "4969"], ["4970", "4971"], ["4972", "4973"], ["4974", "4975"], ["4976", "4977"], ["4978", "4979"], "React Hook useCallback has a missing dependency: 'handleScanSuccess'. Either include it or remove the dependency array.", ["4980"], "'realtimeService' is defined but never used. Allowed unused vars must match /^_/u.", ["4981"], "'createClient' is defined but never used. Allowed unused vars must match /^_/u.", ["4982"], ["4983", "4984"], ["4985", "4986"], ["4987", "4988"], ["4989", "4990"], ["4991"], ["4992", "4993"], ["4994"], ["4995", "4996"], ["4997", "4998"], ["4999", "5000"], ["5001", "5002"], ["5003", "5004"], ["5005", "5006"], ["5007", "5008"], ["5009", "5010"], ["5011", "5012"], ["5013"], ["5014"], ["5015", "5016"], "'supabase' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["5017"], ["5018", "5019"], ["5020", "5021"], ["5022", "5023"], ["5024", "5025"], ["5026", "5027"], ["5028"], ["5029"], ["5030"], ["5031"], ["5032", "5033"], ["5034", "5035"], ["5036", "5037"], ["5038", "5039"], ["5040"], ["5041"], ["5042", "5043"], ["5044", "5045"], ["5046", "5047"], ["5048", "5049"], ["5050", "5051"], ["5052", "5053"], ["5054", "5055"], ["5056", "5057"], ["5058", "5059"], ["5060"], ["5061", "5062"], ["5063", "5064"], ["5065", "5066"], ["5067", "5068"], ["5069", "5070"], ["5071", "5072"], ["5073"], ["5074", "5075"], ["5076"], ["5077", "5078"], ["5079", "5080"], ["5081", "5082"], ["5083", "5084"], ["5085", "5086"], ["5087", "5088"], ["5089", "5090"], ["5091", "5092"], ["5093", "5094"], ["5095", "5096"], ["5097", "5098"], ["5099", "5100"], ["5101", "5102"], ["5103", "5104"], ["5105", "5106"], ["5107", "5108"], ["5109", "5110"], ["5111"], ["5112", "5113"], ["5114", "5115"], ["5116", "5117"], ["5118", "5119"], ["5120", "5121"], ["5122"], ["5123", "5124"], ["5125", "5126"], ["5127", "5128"], ["5129"], ["5130", "5131"], ["5132"], ["5133", "5134"], ["5135", "5136"], {"messageId": "5137", "fix": "5138", "desc": "5139"}, {"messageId": "5140", "fix": "5141", "desc": "5142"}, {"messageId": "5137", "fix": "5143", "desc": "5139"}, {"messageId": "5140", "fix": "5144", "desc": "5142"}, {"messageId": "5137", "fix": "5145", "desc": "5139"}, {"messageId": "5140", "fix": "5146", "desc": "5142"}, {"messageId": "5137", "fix": "5147", "desc": "5139"}, {"messageId": "5140", "fix": "5148", "desc": "5142"}, {"messageId": "5137", "fix": "5149", "desc": "5139"}, {"messageId": "5140", "fix": "5150", "desc": "5142"}, {"messageId": "5137", "fix": "5151", "desc": "5139"}, {"messageId": "5140", "fix": "5152", "desc": "5142"}, {"messageId": "5137", "fix": "5153", "desc": "5139"}, {"messageId": "5140", "fix": "5154", "desc": "5142"}, {"messageId": "5137", "fix": "5155", "desc": "5139"}, {"messageId": "5140", "fix": "5156", "desc": "5142"}, {"messageId": "5137", "fix": "5157", "desc": "5139"}, {"messageId": "5140", "fix": "5158", "desc": "5142"}, {"messageId": "5137", "fix": "5159", "desc": "5139"}, {"messageId": "5140", "fix": "5160", "desc": "5142"}, {"messageId": "5137", "fix": "5161", "desc": "5139"}, {"messageId": "5140", "fix": "5162", "desc": "5142"}, {"messageId": "5137", "fix": "5163", "desc": "5139"}, {"messageId": "5140", "fix": "5164", "desc": "5142"}, {"messageId": "5137", "fix": "5165", "desc": "5139"}, {"messageId": "5140", "fix": "5166", "desc": "5142"}, {"messageId": "5137", "fix": "5167", "desc": "5139"}, {"messageId": "5140", "fix": "5168", "desc": "5142"}, {"messageId": "5137", "fix": "5169", "desc": "5139"}, {"messageId": "5140", "fix": "5170", "desc": "5142"}, {"messageId": "5137", "fix": "5171", "desc": "5139"}, {"messageId": "5140", "fix": "5172", "desc": "5142"}, {"messageId": "5137", "fix": "5173", "desc": "5139"}, {"messageId": "5140", "fix": "5174", "desc": "5142"}, {"messageId": "5137", "fix": "5175", "desc": "5139"}, {"messageId": "5140", "fix": "5176", "desc": "5142"}, {"messageId": "5137", "fix": "5177", "desc": "5139"}, {"messageId": "5140", "fix": "5178", "desc": "5142"}, {"messageId": "5137", "fix": "5179", "desc": "5139"}, {"messageId": "5140", "fix": "5180", "desc": "5142"}, {"messageId": "5137", "fix": "5181", "desc": "5139"}, {"messageId": "5140", "fix": "5182", "desc": "5142"}, {"messageId": "5137", "fix": "5183", "desc": "5139"}, {"messageId": "5140", "fix": "5184", "desc": "5142"}, {"messageId": "5137", "fix": "5185", "desc": "5139"}, {"messageId": "5140", "fix": "5186", "desc": "5142"}, {"messageId": "5137", "fix": "5187", "desc": "5139"}, {"messageId": "5140", "fix": "5188", "desc": "5142"}, {"messageId": "5137", "fix": "5189", "desc": "5139"}, {"messageId": "5140", "fix": "5190", "desc": "5142"}, {"messageId": "5137", "fix": "5191", "desc": "5139"}, {"messageId": "5140", "fix": "5192", "desc": "5142"}, {"messageId": "5137", "fix": "5193", "desc": "5139"}, {"messageId": "5140", "fix": "5194", "desc": "5142"}, {"messageId": "5137", "fix": "5195", "desc": "5139"}, {"messageId": "5140", "fix": "5196", "desc": "5142"}, {"messageId": "5137", "fix": "5197", "desc": "5139"}, {"messageId": "5140", "fix": "5198", "desc": "5142"}, {"messageId": "5137", "fix": "5199", "desc": "5139"}, {"messageId": "5140", "fix": "5200", "desc": "5142"}, {"messageId": "5201", "data": "5202", "fix": "5203", "desc": "5204"}, {"messageId": "5201", "data": "5205", "fix": "5206", "desc": "5207"}, {"messageId": "5201", "data": "5208", "fix": "5209", "desc": "5210"}, {"messageId": "5201", "data": "5211", "fix": "5212", "desc": "5213"}, {"messageId": "5201", "data": "5214", "fix": "5215", "desc": "5216"}, {"messageId": "5201", "data": "5217", "fix": "5218", "desc": "5219"}, {"messageId": "5201", "data": "5220", "fix": "5221", "desc": "5216"}, {"messageId": "5201", "data": "5222", "fix": "5223", "desc": "5219"}, {"messageId": "5137", "fix": "5224", "desc": "5139"}, {"messageId": "5140", "fix": "5225", "desc": "5142"}, {"messageId": "5137", "fix": "5226", "desc": "5139"}, {"messageId": "5140", "fix": "5227", "desc": "5142"}, {"messageId": "5137", "fix": "5228", "desc": "5139"}, {"messageId": "5140", "fix": "5229", "desc": "5142"}, {"desc": "5230", "fix": "5231"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5234", "desc": "5139"}, {"messageId": "5140", "fix": "5235", "desc": "5142"}, {"messageId": "5137", "fix": "5236", "desc": "5139"}, {"messageId": "5140", "fix": "5237", "desc": "5142"}, {"messageId": "5137", "fix": "5238", "desc": "5139"}, {"messageId": "5140", "fix": "5239", "desc": "5142"}, {"messageId": "5137", "fix": "5240", "desc": "5139"}, {"messageId": "5140", "fix": "5241", "desc": "5142"}, {"messageId": "5201", "data": "5242", "fix": "5243", "desc": "5219"}, {"messageId": "5201", "data": "5244", "fix": "5245", "desc": "5219"}, {"messageId": "5201", "data": "5246", "fix": "5247", "desc": "5219"}, {"messageId": "5201", "data": "5248", "fix": "5249", "desc": "5219"}, {"messageId": "5201", "data": "5250", "fix": "5251", "desc": "5219"}, {"messageId": "5201", "data": "5252", "fix": "5253", "desc": "5219"}, {"messageId": "5201", "data": "5254", "fix": "5255", "desc": "5219"}, {"messageId": "5137", "fix": "5256", "desc": "5139"}, {"messageId": "5140", "fix": "5257", "desc": "5142"}, {"messageId": "5137", "fix": "5258", "desc": "5139"}, {"messageId": "5140", "fix": "5259", "desc": "5142"}, {"messageId": "5201", "data": "5260", "fix": "5261", "desc": "5219"}, {"messageId": "5201", "data": "5262", "fix": "5263", "desc": "5264"}, {"messageId": "5201", "data": "5265", "fix": "5266", "desc": "5267"}, {"messageId": "5137", "fix": "5268", "desc": "5139"}, {"messageId": "5140", "fix": "5269", "desc": "5142"}, {"messageId": "5137", "fix": "5270", "desc": "5139"}, {"messageId": "5140", "fix": "5271", "desc": "5142"}, {"messageId": "5201", "data": "5272", "fix": "5273", "desc": "5219"}, {"messageId": "5137", "fix": "5274", "desc": "5139"}, {"messageId": "5140", "fix": "5275", "desc": "5142"}, {"messageId": "5137", "fix": "5276", "desc": "5139"}, {"messageId": "5140", "fix": "5277", "desc": "5142"}, {"messageId": "5137", "fix": "5278", "desc": "5139"}, {"messageId": "5140", "fix": "5279", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"desc": "5280", "fix": "5281"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5201", "data": "5282", "fix": "5283", "desc": "5284"}, {"messageId": "5137", "fix": "5285", "desc": "5139"}, {"messageId": "5140", "fix": "5286", "desc": "5142"}, {"messageId": "5137", "fix": "5287", "desc": "5139"}, {"messageId": "5140", "fix": "5288", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"desc": "5289", "fix": "5290"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5201", "data": "5291", "fix": "5292", "desc": "5293"}, {"messageId": "5137", "fix": "5294", "desc": "5139"}, {"messageId": "5140", "fix": "5295", "desc": "5142"}, {"messageId": "5137", "fix": "5296", "desc": "5139"}, {"messageId": "5140", "fix": "5297", "desc": "5142"}, {"messageId": "5137", "fix": "5298", "desc": "5139"}, {"messageId": "5140", "fix": "5299", "desc": "5142"}, {"messageId": "5137", "fix": "5300", "desc": "5139"}, {"messageId": "5140", "fix": "5301", "desc": "5142"}, {"messageId": "5137", "fix": "5302", "desc": "5139"}, {"messageId": "5140", "fix": "5303", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5304", "desc": "5139"}, {"messageId": "5140", "fix": "5305", "desc": "5142"}, {"messageId": "5137", "fix": "5306", "desc": "5139"}, {"messageId": "5140", "fix": "5307", "desc": "5142"}, {"messageId": "5137", "fix": "5308", "desc": "5139"}, {"messageId": "5140", "fix": "5309", "desc": "5142"}, {"messageId": "5137", "fix": "5310", "desc": "5139"}, {"messageId": "5140", "fix": "5311", "desc": "5142"}, {"messageId": "5137", "fix": "5312", "desc": "5139"}, {"messageId": "5140", "fix": "5313", "desc": "5142"}, {"messageId": "5137", "fix": "5314", "desc": "5139"}, {"messageId": "5140", "fix": "5315", "desc": "5142"}, {"messageId": "5137", "fix": "5316", "desc": "5139"}, {"messageId": "5140", "fix": "5317", "desc": "5142"}, {"desc": "5318", "fix": "5319"}, {"messageId": "5201", "data": "5320", "fix": "5321", "desc": "5322"}, {"messageId": "5201", "data": "5323", "fix": "5324", "desc": "5325"}, {"messageId": "5137", "fix": "5326", "desc": "5139"}, {"messageId": "5140", "fix": "5327", "desc": "5142"}, {"messageId": "5137", "fix": "5328", "desc": "5139"}, {"messageId": "5140", "fix": "5329", "desc": "5142"}, {"messageId": "5137", "fix": "5330", "desc": "5139"}, {"messageId": "5140", "fix": "5331", "desc": "5142"}, {"messageId": "5137", "fix": "5332", "desc": "5139"}, {"messageId": "5140", "fix": "5333", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5334", "desc": "5139"}, {"messageId": "5140", "fix": "5335", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5336", "desc": "5139"}, {"messageId": "5140", "fix": "5337", "desc": "5142"}, {"messageId": "5137", "fix": "5338", "desc": "5139"}, {"messageId": "5140", "fix": "5339", "desc": "5142"}, {"messageId": "5137", "fix": "5340", "desc": "5139"}, {"messageId": "5140", "fix": "5341", "desc": "5142"}, {"messageId": "5137", "fix": "5342", "desc": "5139"}, {"messageId": "5140", "fix": "5343", "desc": "5142"}, {"messageId": "5137", "fix": "5344", "desc": "5139"}, {"messageId": "5140", "fix": "5345", "desc": "5142"}, {"messageId": "5137", "fix": "5346", "desc": "5139"}, {"messageId": "5140", "fix": "5347", "desc": "5142"}, {"messageId": "5137", "fix": "5348", "desc": "5139"}, {"messageId": "5140", "fix": "5349", "desc": "5142"}, {"messageId": "5137", "fix": "5350", "desc": "5139"}, {"messageId": "5140", "fix": "5351", "desc": "5142"}, {"messageId": "5137", "fix": "5352", "desc": "5139"}, {"messageId": "5140", "fix": "5353", "desc": "5142"}, {"messageId": "5201", "data": "5354", "fix": "5355", "desc": "5219"}, {"messageId": "5201", "data": "5356", "fix": "5357", "desc": "5293"}, {"messageId": "5137", "fix": "5358", "desc": "5139"}, {"messageId": "5140", "fix": "5359", "desc": "5142"}, {"messageId": "5201", "data": "5360", "fix": "5361", "desc": "5362"}, {"messageId": "5137", "fix": "5363", "desc": "5139"}, {"messageId": "5140", "fix": "5364", "desc": "5142"}, {"messageId": "5137", "fix": "5365", "desc": "5139"}, {"messageId": "5140", "fix": "5366", "desc": "5142"}, {"messageId": "5137", "fix": "5367", "desc": "5139"}, {"messageId": "5140", "fix": "5368", "desc": "5142"}, {"messageId": "5137", "fix": "5369", "desc": "5139"}, {"messageId": "5140", "fix": "5370", "desc": "5142"}, {"messageId": "5137", "fix": "5371", "desc": "5139"}, {"messageId": "5140", "fix": "5372", "desc": "5142"}, {"messageId": "5201", "data": "5373", "fix": "5374", "desc": "5216"}, {"messageId": "5201", "data": "5375", "fix": "5376", "desc": "5219"}, {"messageId": "5201", "data": "5377", "fix": "5378", "desc": "5216"}, {"messageId": "5201", "data": "5379", "fix": "5380", "desc": "5219"}, {"messageId": "5137", "fix": "5381", "desc": "5139"}, {"messageId": "5140", "fix": "5382", "desc": "5142"}, {"messageId": "5137", "fix": "5383", "desc": "5139"}, {"messageId": "5140", "fix": "5384", "desc": "5142"}, {"messageId": "5137", "fix": "5385", "desc": "5139"}, {"messageId": "5140", "fix": "5386", "desc": "5142"}, {"messageId": "5137", "fix": "5387", "desc": "5139"}, {"messageId": "5140", "fix": "5388", "desc": "5142"}, {"messageId": "5201", "data": "5389", "fix": "5390", "desc": "5216"}, {"messageId": "5201", "data": "5391", "fix": "5392", "desc": "5219"}, {"messageId": "5137", "fix": "5393", "desc": "5139"}, {"messageId": "5140", "fix": "5394", "desc": "5142"}, {"messageId": "5137", "fix": "5395", "desc": "5139"}, {"messageId": "5140", "fix": "5396", "desc": "5142"}, {"messageId": "5137", "fix": "5397", "desc": "5139"}, {"messageId": "5140", "fix": "5398", "desc": "5142"}, {"messageId": "5137", "fix": "5399", "desc": "5139"}, {"messageId": "5140", "fix": "5400", "desc": "5142"}, {"messageId": "5137", "fix": "5401", "desc": "5139"}, {"messageId": "5140", "fix": "5402", "desc": "5142"}, {"messageId": "5137", "fix": "5403", "desc": "5139"}, {"messageId": "5140", "fix": "5404", "desc": "5142"}, {"messageId": "5137", "fix": "5405", "desc": "5139"}, {"messageId": "5140", "fix": "5406", "desc": "5142"}, {"messageId": "5137", "fix": "5407", "desc": "5139"}, {"messageId": "5140", "fix": "5408", "desc": "5142"}, {"messageId": "5137", "fix": "5409", "desc": "5139"}, {"messageId": "5140", "fix": "5410", "desc": "5142"}, {"messageId": "5201", "data": "5411", "fix": "5412", "desc": "5362"}, {"messageId": "5137", "fix": "5413", "desc": "5139"}, {"messageId": "5140", "fix": "5414", "desc": "5142"}, {"messageId": "5137", "fix": "5415", "desc": "5139"}, {"messageId": "5140", "fix": "5416", "desc": "5142"}, {"messageId": "5137", "fix": "5417", "desc": "5139"}, {"messageId": "5140", "fix": "5418", "desc": "5142"}, {"messageId": "5137", "fix": "5419", "desc": "5139"}, {"messageId": "5140", "fix": "5420", "desc": "5142"}, {"messageId": "5137", "fix": "5421", "desc": "5139"}, {"messageId": "5140", "fix": "5422", "desc": "5142"}, {"messageId": "5137", "fix": "5423", "desc": "5139"}, {"messageId": "5140", "fix": "5424", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5425", "desc": "5139"}, {"messageId": "5140", "fix": "5426", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5427", "desc": "5139"}, {"messageId": "5140", "fix": "5428", "desc": "5142"}, {"messageId": "5137", "fix": "5429", "desc": "5139"}, {"messageId": "5140", "fix": "5430", "desc": "5142"}, {"messageId": "5137", "fix": "5431", "desc": "5139"}, {"messageId": "5140", "fix": "5432", "desc": "5142"}, {"messageId": "5137", "fix": "5433", "desc": "5139"}, {"messageId": "5140", "fix": "5434", "desc": "5142"}, {"messageId": "5137", "fix": "5435", "desc": "5139"}, {"messageId": "5140", "fix": "5436", "desc": "5142"}, {"messageId": "5137", "fix": "5437", "desc": "5139"}, {"messageId": "5140", "fix": "5438", "desc": "5142"}, {"messageId": "5137", "fix": "5439", "desc": "5139"}, {"messageId": "5140", "fix": "5440", "desc": "5142"}, {"messageId": "5137", "fix": "5441", "desc": "5139"}, {"messageId": "5140", "fix": "5442", "desc": "5142"}, {"messageId": "5137", "fix": "5443", "desc": "5139"}, {"messageId": "5140", "fix": "5444", "desc": "5142"}, {"messageId": "5137", "fix": "5445", "desc": "5139"}, {"messageId": "5140", "fix": "5446", "desc": "5142"}, {"messageId": "5137", "fix": "5447", "desc": "5139"}, {"messageId": "5140", "fix": "5448", "desc": "5142"}, {"messageId": "5137", "fix": "5449", "desc": "5139"}, {"messageId": "5140", "fix": "5450", "desc": "5142"}, {"messageId": "5137", "fix": "5451", "desc": "5139"}, {"messageId": "5140", "fix": "5452", "desc": "5142"}, {"messageId": "5137", "fix": "5453", "desc": "5139"}, {"messageId": "5140", "fix": "5454", "desc": "5142"}, {"messageId": "5137", "fix": "5455", "desc": "5139"}, {"messageId": "5140", "fix": "5456", "desc": "5142"}, {"messageId": "5137", "fix": "5457", "desc": "5139"}, {"messageId": "5140", "fix": "5458", "desc": "5142"}, {"messageId": "5137", "fix": "5459", "desc": "5139"}, {"messageId": "5140", "fix": "5460", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5461", "desc": "5139"}, {"messageId": "5140", "fix": "5462", "desc": "5142"}, {"messageId": "5137", "fix": "5463", "desc": "5139"}, {"messageId": "5140", "fix": "5464", "desc": "5142"}, {"messageId": "5137", "fix": "5465", "desc": "5139"}, {"messageId": "5140", "fix": "5466", "desc": "5142"}, {"messageId": "5137", "fix": "5467", "desc": "5139"}, {"messageId": "5140", "fix": "5468", "desc": "5142"}, {"messageId": "5137", "fix": "5469", "desc": "5139"}, {"messageId": "5140", "fix": "5470", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5471", "desc": "5139"}, {"messageId": "5140", "fix": "5472", "desc": "5142"}, {"messageId": "5137", "fix": "5473", "desc": "5139"}, {"messageId": "5140", "fix": "5474", "desc": "5142"}, {"messageId": "5137", "fix": "5475", "desc": "5139"}, {"messageId": "5140", "fix": "5476", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5477", "desc": "5139"}, {"messageId": "5140", "fix": "5478", "desc": "5142"}, {"kind": "5232", "justification": "5233"}, {"messageId": "5137", "fix": "5479", "desc": "5139"}, {"messageId": "5140", "fix": "5480", "desc": "5142"}, {"messageId": "5137", "fix": "5481", "desc": "5139"}, {"messageId": "5140", "fix": "5482", "desc": "5142"}, "suggestUnknown", {"range": "5483", "text": "5484"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "5485", "text": "5486"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "5487", "text": "5484"}, {"range": "5488", "text": "5486"}, {"range": "5489", "text": "5484"}, {"range": "5490", "text": "5486"}, {"range": "5491", "text": "5484"}, {"range": "5492", "text": "5486"}, {"range": "5493", "text": "5484"}, {"range": "5494", "text": "5486"}, {"range": "5495", "text": "5484"}, {"range": "5496", "text": "5486"}, {"range": "5497", "text": "5484"}, {"range": "5498", "text": "5486"}, {"range": "5499", "text": "5484"}, {"range": "5500", "text": "5486"}, {"range": "5501", "text": "5484"}, {"range": "5502", "text": "5486"}, {"range": "5503", "text": "5484"}, {"range": "5504", "text": "5486"}, {"range": "5505", "text": "5484"}, {"range": "5506", "text": "5486"}, {"range": "5507", "text": "5484"}, {"range": "5508", "text": "5486"}, {"range": "5509", "text": "5484"}, {"range": "5510", "text": "5486"}, {"range": "5511", "text": "5484"}, {"range": "5512", "text": "5486"}, {"range": "5513", "text": "5484"}, {"range": "5514", "text": "5486"}, {"range": "5515", "text": "5484"}, {"range": "5516", "text": "5486"}, {"range": "5517", "text": "5484"}, {"range": "5518", "text": "5486"}, {"range": "5519", "text": "5484"}, {"range": "5520", "text": "5486"}, {"range": "5521", "text": "5484"}, {"range": "5522", "text": "5486"}, {"range": "5523", "text": "5484"}, {"range": "5524", "text": "5486"}, {"range": "5525", "text": "5484"}, {"range": "5526", "text": "5486"}, {"range": "5527", "text": "5484"}, {"range": "5528", "text": "5486"}, {"range": "5529", "text": "5484"}, {"range": "5530", "text": "5486"}, {"range": "5531", "text": "5484"}, {"range": "5532", "text": "5486"}, {"range": "5533", "text": "5484"}, {"range": "5534", "text": "5486"}, {"range": "5535", "text": "5484"}, {"range": "5536", "text": "5486"}, {"range": "5537", "text": "5484"}, {"range": "5538", "text": "5486"}, {"range": "5539", "text": "5484"}, {"range": "5540", "text": "5486"}, {"range": "5541", "text": "5484"}, {"range": "5542", "text": "5486"}, {"range": "5543", "text": "5484"}, {"range": "5544", "text": "5486"}, "removeVar", {"varName": "5545"}, {"range": "5546", "text": "5233"}, "Remove unused variable 'ReviewsResult'.", {"varName": "5547"}, {"range": "5548", "text": "5233"}, "Remove unused variable 'FollowerWithProfile'.", {"varName": "5549"}, {"range": "5550", "text": "5233"}, "Remove unused variable 'FollowersR<PERSON>ult'.", {"varName": "5551"}, {"range": "5552", "text": "5233"}, "Remove unused variable 'SubscriptionWithProfile'.", {"varName": "5553"}, {"range": "5554", "text": "5233"}, "Remove unused variable 'SupabaseClient'.", {"varName": "5555"}, {"range": "5556", "text": "5233"}, "Remove unused variable 'Database'.", {"varName": "5553"}, {"range": "5557", "text": "5233"}, {"varName": "5555"}, {"range": "5558", "text": "5233"}, {"range": "5559", "text": "5484"}, {"range": "5560", "text": "5486"}, {"range": "5561", "text": "5484"}, {"range": "5562", "text": "5486"}, {"range": "5563", "text": "5484"}, {"range": "5564", "text": "5486"}, "Update the dependencies array to be: [initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", {"range": "5565", "text": "5566"}, "directive", "", {"range": "5567", "text": "5484"}, {"range": "5568", "text": "5486"}, {"range": "5569", "text": "5484"}, {"range": "5570", "text": "5486"}, {"range": "5571", "text": "5484"}, {"range": "5572", "text": "5486"}, {"range": "5573", "text": "5484"}, {"range": "5574", "text": "5486"}, {"varName": "5555"}, {"range": "5575", "text": "5233"}, {"varName": "5555"}, {"range": "5576", "text": "5233"}, {"varName": "5555"}, {"range": "5577", "text": "5233"}, {"varName": "5555"}, {"range": "5578", "text": "5233"}, {"varName": "5555"}, {"range": "5579", "text": "5233"}, {"varName": "5555"}, {"range": "5580", "text": "5233"}, {"varName": "5555"}, {"range": "5581", "text": "5233"}, {"range": "5582", "text": "5484"}, {"range": "5583", "text": "5486"}, {"range": "5584", "text": "5484"}, {"range": "5585", "text": "5486"}, {"varName": "5555"}, {"range": "5586", "text": "5233"}, {"varName": "5587"}, {"range": "5588", "text": "5233"}, "Remove unused variable 'page'.", {"varName": "5589"}, {"range": "5590", "text": "5233"}, "Remove unused variable 'count'.", {"range": "5591", "text": "5484"}, {"range": "5592", "text": "5486"}, {"range": "5593", "text": "5484"}, {"range": "5594", "text": "5486"}, {"varName": "5555"}, {"range": "5595", "text": "5233"}, {"range": "5596", "text": "5484"}, {"range": "5597", "text": "5486"}, {"range": "5598", "text": "5484"}, {"range": "5599", "text": "5486"}, {"range": "5600", "text": "5484"}, {"range": "5601", "text": "5486"}, "Update the dependencies array to be: [currentPage, sortBy, businessProfileId, loadReviews]", {"range": "5602", "text": "5603"}, {"varName": "5604"}, {"range": "5605", "text": "5233"}, "Remove unused variable 'ProductServiceData'.", {"range": "5606", "text": "5484"}, {"range": "5607", "text": "5486"}, {"range": "5608", "text": "5484"}, {"range": "5609", "text": "5486"}, "Update the dependencies array to be: [isSearching, performSearch, products.length, viewType]", {"range": "5610", "text": "5611"}, {"varName": "5612"}, {"range": "5613", "text": "5233"}, "Remove unused variable 'Tables'.", {"range": "5614", "text": "5484"}, {"range": "5615", "text": "5486"}, {"range": "5616", "text": "5484"}, {"range": "5617", "text": "5486"}, {"range": "5618", "text": "5484"}, {"range": "5619", "text": "5486"}, {"range": "5620", "text": "5484"}, {"range": "5621", "text": "5486"}, {"range": "5622", "text": "5484"}, {"range": "5623", "text": "5486"}, {"range": "5624", "text": "5484"}, {"range": "5625", "text": "5486"}, {"range": "5626", "text": "5484"}, {"range": "5627", "text": "5486"}, {"range": "5628", "text": "5484"}, {"range": "5629", "text": "5486"}, {"range": "5630", "text": "5484"}, {"range": "5631", "text": "5486"}, {"range": "5632", "text": "5484"}, {"range": "5633", "text": "5486"}, {"range": "5634", "text": "5484"}, {"range": "5635", "text": "5486"}, {"range": "5636", "text": "5484"}, {"range": "5637", "text": "5486"}, "Update the dependencies array to be: [handleScanSuccess]", {"range": "5638", "text": "5639"}, {"varName": "5640"}, {"range": "5641", "text": "5233"}, "Remove unused variable 'realtimeService'.", {"varName": "5642"}, {"range": "5643", "text": "5233"}, "Remove unused variable 'createClient'.", {"range": "5644", "text": "5484"}, {"range": "5645", "text": "5486"}, {"range": "5646", "text": "5484"}, {"range": "5647", "text": "5486"}, {"range": "5648", "text": "5484"}, {"range": "5649", "text": "5486"}, {"range": "5650", "text": "5484"}, {"range": "5651", "text": "5486"}, {"range": "5652", "text": "5484"}, {"range": "5653", "text": "5486"}, {"range": "5654", "text": "5484"}, {"range": "5655", "text": "5486"}, {"range": "5656", "text": "5484"}, {"range": "5657", "text": "5486"}, {"range": "5658", "text": "5484"}, {"range": "5659", "text": "5486"}, {"range": "5660", "text": "5484"}, {"range": "5661", "text": "5486"}, {"range": "5662", "text": "5484"}, {"range": "5663", "text": "5486"}, {"range": "5664", "text": "5484"}, {"range": "5665", "text": "5486"}, {"range": "5666", "text": "5484"}, {"range": "5667", "text": "5486"}, {"range": "5668", "text": "5484"}, {"range": "5669", "text": "5486"}, {"range": "5670", "text": "5484"}, {"range": "5671", "text": "5486"}, {"varName": "5555"}, {"range": "5672", "text": "5233"}, {"varName": "5612"}, {"range": "5673", "text": "5233"}, {"range": "5674", "text": "5484"}, {"range": "5675", "text": "5486"}, {"varName": "5676"}, {"range": "5677", "text": "5233"}, "Remove unused variable 'supabase'.", {"range": "5678", "text": "5484"}, {"range": "5679", "text": "5486"}, {"range": "5680", "text": "5484"}, {"range": "5681", "text": "5486"}, {"range": "5682", "text": "5484"}, {"range": "5683", "text": "5486"}, {"range": "5684", "text": "5484"}, {"range": "5685", "text": "5486"}, {"range": "5686", "text": "5484"}, {"range": "5687", "text": "5486"}, {"varName": "5553"}, {"range": "5688", "text": "5233"}, {"varName": "5555"}, {"range": "5689", "text": "5233"}, {"varName": "5553"}, {"range": "5690", "text": "5233"}, {"varName": "5555"}, {"range": "5691", "text": "5233"}, {"range": "5692", "text": "5484"}, {"range": "5693", "text": "5486"}, {"range": "5694", "text": "5484"}, {"range": "5695", "text": "5486"}, {"range": "5696", "text": "5484"}, {"range": "5697", "text": "5486"}, {"range": "5698", "text": "5484"}, {"range": "5699", "text": "5486"}, {"varName": "5553"}, {"range": "5700", "text": "5233"}, {"varName": "5555"}, {"range": "5701", "text": "5233"}, {"range": "5702", "text": "5484"}, {"range": "5703", "text": "5486"}, {"range": "5704", "text": "5484"}, {"range": "5705", "text": "5486"}, {"range": "5706", "text": "5484"}, {"range": "5707", "text": "5486"}, {"range": "5708", "text": "5484"}, {"range": "5709", "text": "5486"}, {"range": "5710", "text": "5484"}, {"range": "5711", "text": "5486"}, {"range": "5712", "text": "5484"}, {"range": "5713", "text": "5486"}, {"range": "5714", "text": "5484"}, {"range": "5715", "text": "5486"}, {"range": "5716", "text": "5484"}, {"range": "5717", "text": "5486"}, {"range": "5718", "text": "5484"}, {"range": "5719", "text": "5486"}, {"varName": "5676"}, {"range": "5720", "text": "5233"}, {"range": "5721", "text": "5484"}, {"range": "5722", "text": "5486"}, {"range": "5723", "text": "5484"}, {"range": "5724", "text": "5486"}, {"range": "5725", "text": "5484"}, {"range": "5726", "text": "5486"}, {"range": "5727", "text": "5484"}, {"range": "5728", "text": "5486"}, {"range": "5729", "text": "5484"}, {"range": "5730", "text": "5486"}, {"range": "5731", "text": "5484"}, {"range": "5732", "text": "5486"}, {"range": "5733", "text": "5484"}, {"range": "5734", "text": "5486"}, {"range": "5735", "text": "5484"}, {"range": "5736", "text": "5486"}, {"range": "5737", "text": "5484"}, {"range": "5738", "text": "5486"}, {"range": "5739", "text": "5484"}, {"range": "5740", "text": "5486"}, {"range": "5741", "text": "5484"}, {"range": "5742", "text": "5486"}, {"range": "5743", "text": "5484"}, {"range": "5744", "text": "5486"}, {"range": "5745", "text": "5484"}, {"range": "5746", "text": "5486"}, {"range": "5747", "text": "5484"}, {"range": "5748", "text": "5486"}, {"range": "5749", "text": "5484"}, {"range": "5750", "text": "5486"}, {"range": "5751", "text": "5484"}, {"range": "5752", "text": "5486"}, {"range": "5753", "text": "5484"}, {"range": "5754", "text": "5486"}, {"range": "5755", "text": "5484"}, {"range": "5756", "text": "5486"}, {"range": "5757", "text": "5484"}, {"range": "5758", "text": "5486"}, {"range": "5759", "text": "5484"}, {"range": "5760", "text": "5486"}, {"range": "5761", "text": "5484"}, {"range": "5762", "text": "5486"}, {"range": "5763", "text": "5484"}, {"range": "5764", "text": "5486"}, {"range": "5765", "text": "5484"}, {"range": "5766", "text": "5486"}, {"range": "5767", "text": "5484"}, {"range": "5768", "text": "5486"}, {"range": "5769", "text": "5484"}, {"range": "5770", "text": "5486"}, {"range": "5771", "text": "5484"}, {"range": "5772", "text": "5486"}, {"range": "5773", "text": "5484"}, {"range": "5774", "text": "5486"}, {"range": "5775", "text": "5484"}, {"range": "5776", "text": "5486"}, {"range": "5777", "text": "5484"}, {"range": "5778", "text": "5486"}, {"range": "5779", "text": "5484"}, {"range": "5780", "text": "5486"}, {"range": "5781", "text": "5484"}, {"range": "5782", "text": "5486"}, {"range": "5783", "text": "5484"}, {"range": "5784", "text": "5486"}, {"range": "5785", "text": "5484"}, {"range": "5786", "text": "5486"}, {"range": "5787", "text": "5484"}, {"range": "5788", "text": "5486"}, {"range": "5789", "text": "5484"}, {"range": "5790", "text": "5486"}, [3990, 3993], "unknown", [3990, 3993], "never", [3157, 3160], [3157, 3160], [4508, 4511], [4508, 4511], [8534, 8537], [8534, 8537], [5356, 5359], [5356, 5359], [5519, 5522], [5519, 5522], [9806, 9809], [9806, 9809], [9989, 9992], [9989, 9992], [10035, 10038], [10035, 10038], [10089, 10092], [10089, 10092], [2514, 2517], [2514, 2517], [2602, 2605], [2602, 2605], [1926, 1929], [1926, 1929], [3315, 3318], [3315, 3318], [4948, 4951], [4948, 4951], [4201, 4204], [4201, 4204], [4527, 4530], [4527, 4530], [4559, 4562], [4559, 4562], [4621, 4624], [4621, 4624], [4653, 4656], [4653, 4656], [15428, 15431], [15428, 15431], [9228, 9231], [9228, 9231], [9438, 9441], [9438, 9441], [9809, 9812], [9809, 9812], [2386, 2389], [2386, 2389], [3553, 3556], [3553, 3556], [10077, 10080], [10077, 10080], [16494, 16497], [16494, 16497], [6043, 6046], [6043, 6046], [3496, 3499], [3496, 3499], "ReviewsResult", [40, 55], "FollowerWithProfile", [49, 73], "FollowersResult", [73, 93], "SubscriptionWithProfile", [93, 121], "SupabaseClient", [74, 129], "Database", [131, 175], [74, 129], [131, 175], [3752, 3755], [3752, 3755], [3819, 3822], [3819, 3822], [2107, 2110], [2107, 2110], [9509, 9511], "[initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", [14288, 14291], [14288, 14291], [14372, 14375], [14372, 14375], [14406, 14409], [14406, 14409], [14660, 14663], [14660, 14663], [163, 207], [185, 229], [255, 299], [236, 280], [252, 296], [255, 299], [251, 295], [5307, 5310], [5307, 5310], [5437, 5440], [5437, 5440], [253, 297], "page", [1811, 1870], "count", [1876, 1938], [5109, 5112], [5109, 5112], [5239, 5242], [5239, 5242], [249, 293], [7881, 7884], [7881, 7884], [8246, 8249], [8246, 8249], [8376, 8379], [8376, 8379], [1800, 1840], "[currentPage, sortBy, businessProfileId, loadReviews]", "ProductServiceData", [3570, 3588], [4123, 4126], [4123, 4126], [4605, 4608], [4605, 4608], [5544, 5546], "[isSearching, performSearch, products.length, viewType]", "Tables", [303, 345], [3267, 3270], [3267, 3270], [9983, 9986], [9983, 9986], [10035, 10038], [10035, 10038], [6590, 6593], [6590, 6593], [12572, 12575], [12572, 12575], [2152, 2155], [2152, 2155], [3970, 3973], [3970, 3973], [5410, 5413], [5410, 5413], [5983, 5986], [5983, 5986], [7360, 7363], [7360, 7363], [1660, 1663], [1660, 1663], [2569, 2572], [2569, 2572], [4157, 4159], "[handleScanSuccess]", "realtimeService", [747, 812], "createClient", [814, 869], [3732, 3735], [3732, 3735], [4043, 4046], [4043, 4046], [4067, 4070], [4067, 4070], [394, 397], [394, 397], [424, 427], [424, 427], [9442, 9445], [9442, 9445], [1549, 1552], [1549, 1552], [1593, 1596], [1593, 1596], [4462, 4465], [4462, 4465], [4506, 4509], [4506, 4509], [7783, 7786], [7783, 7786], [7827, 7830], [7827, 7830], [11366, 11369], [11366, 11369], [11410, 11413], [11410, 11413], [127, 136], [135, 143], [2843, 2846], [2843, 2846], "supabase", [961, 999], [4025, 4028], [4025, 4028], [2674, 2677], [2674, 2677], [3825, 3828], [3825, 3828], [9549, 9552], [9549, 9552], [9820, 9823], [9820, 9823], [74, 129], [131, 175], [57, 112], [114, 158], [4368, 4371], [4368, 4371], [4796, 4799], [4796, 4799], [4892, 4895], [4892, 4895], [5000, 5003], [5000, 5003], [74, 129], [131, 175], [19205, 19208], [19205, 19208], [19307, 19310], [19307, 19310], [19445, 19448], [19445, 19448], [20910, 20913], [20910, 20913], [21006, 21009], [21006, 21009], [21123, 21126], [21123, 21126], [6218, 6221], [6218, 6221], [6308, 6311], [6308, 6311], [6419, 6422], [6419, 6422], [868, 934], [5047, 5050], [5047, 5050], [5143, 5146], [5143, 5146], [5273, 5276], [5273, 5276], [9953, 9956], [9953, 9956], [10083, 10086], [10083, 10086], [170, 173], [170, 173], [362, 365], [362, 365], [1899, 1902], [1899, 1902], [3493, 3496], [3493, 3496], [4841, 4844], [4841, 4844], [17809, 17812], [17809, 17812], [18166, 18169], [18166, 18169], [18366, 18369], [18366, 18369], [6359, 6362], [6359, 6362], [6506, 6509], [6506, 6509], [6653, 6656], [6653, 6656], [6797, 6800], [6797, 6800], [11147, 11150], [11147, 11150], [11335, 11338], [11335, 11338], [11477, 11480], [11477, 11480], [15260, 15263], [15260, 15263], [15415, 15418], [15415, 15418], [15563, 15566], [15563, 15566], [478, 481], [478, 481], [4829, 4832], [4829, 4832], [2134, 2137], [2134, 2137], [2340, 2343], [2340, 2343], [7100, 7103], [7100, 7103], [1622, 1625], [1622, 1625], [8671, 8674], [8671, 8674], [4671, 4674], [4671, 4674], [3747, 3750], [3747, 3750], [3995, 3998], [3995, 3998], [9079, 9082], [9079, 9082], [9584, 9587], [9584, 9587]]