# Expired Ads View Documentation

## View Overview

The `expired_ads_view` in the Dukancard application provides a focused interface for viewing and managing expired custom ads. This view filters and presents only ads that have passed their expiry date, along with computed status information and duration calculations.

## View Purpose

This view serves specific administrative and cleanup purposes:
- Identifying ads that need to be deactivated or removed
- Providing cleanup reports for expired content
- Supporting ad lifecycle management
- Enabling expired ad analytics and reporting

## View Schema

| Column Name | Data Type | Source | Description |
|-------------|-----------|---------|-------------|
| ad_id | uuid | custom_ads.id | Unique identifier for the expired ad |
| ad_image_url | text | custom_ads.ad_image_url | URL to the ad image |
| ad_link_url | text | custom_ads.ad_link_url | URL that the ad links to when clicked |
| is_active | boolean | custom_ads.is_active | Whether the ad is still marked as active |
| expiry_date | timestamptz | custom_ads.expiry_date | When the ad expired |
| created_at | timestamptz | custom_ads.created_at | When the ad was originally created |
| status | text | Computed | Current status: 'Expired', 'Expiring Soon', or 'No Expiry' |
| expired_for | interval | Computed | How long the ad has been expired (negative for future expiry) |

## View Definition

```sql
CREATE VIEW expired_ads_view AS
SELECT
    c.id AS ad_id,
    c.ad_image_url,
    c.ad_link_url,
    c.is_active,
    c.expiry_date,
    c.created_at,
    CASE
        WHEN c.expiry_date IS NULL THEN 'No Expiry'
        WHEN c.expiry_date <= now() THEN 'Expired'
        WHEN c.expiry_date <= (now() + INTERVAL '7 days') THEN 'Expiring Soon'
        ELSE 'Active'
    END AS status,
    (now() - c.expiry_date) AS expired_for
FROM custom_ads c
WHERE c.expiry_date IS NOT NULL
  AND c.expiry_date <= now()
ORDER BY c.expiry_date DESC;
```

## Security Configuration

This view follows Supabase security best practices:
- **No SECURITY DEFINER**: View uses the permissions of the querying user (not the view creator)
- **Inherited RLS**: Row Level Security policies from the source table are automatically applied
- **User-based access**: Each user sees only the expired ads they're authorized to access

## Filter Logic

The view automatically filters to include only:
- **Ads with expiry dates**: `expiry_date IS NOT NULL`
- **Past expiry dates**: `expiry_date <= now()`

This ensures the view contains only truly expired ads.

## Status Field Values

- **'Expired'**: Ad expiry date has passed (primary case for this view)
- **'Expiring Soon'**: Ad expires within 7 days (edge case, shouldn't appear due to WHERE clause)
- **'No Expiry'**: Ad has no expiry date (shouldn't appear due to WHERE clause)

## Usage Patterns

### Cleanup Operations
```sql
-- Get all expired ads that are still marked as active
SELECT ad_id, ad_image_url, expiry_date, expired_for
FROM expired_ads_view 
WHERE is_active = true
ORDER BY expiry_date ASC;

-- Get ads expired for more than 30 days
SELECT ad_id, ad_image_url, expiry_date, expired_for
FROM expired_ads_view 
WHERE expired_for > INTERVAL '30 days'
ORDER BY expired_for DESC;

-- Count expired ads by month
SELECT 
    DATE_TRUNC('month', expiry_date) as expiry_month,
    COUNT(*) as expired_count
FROM expired_ads_view 
GROUP BY DATE_TRUNC('month', expiry_date)
ORDER BY expiry_month DESC;
```

### Administrative Reports
```sql
-- Recently expired ads (last 7 days)
SELECT ad_id, ad_image_url, expiry_date, is_active
FROM expired_ads_view 
WHERE expired_for <= INTERVAL '7 days'
ORDER BY expiry_date DESC;

-- Long-expired ads that need cleanup
SELECT ad_id, ad_image_url, expiry_date, expired_for
FROM expired_ads_view 
WHERE expired_for > INTERVAL '90 days'
  AND is_active = true
ORDER BY expired_for DESC;

-- Expired ads summary
SELECT 
    is_active,
    COUNT(*) as ad_count,
    AVG(expired_for) as avg_expired_duration,
    MAX(expired_for) as longest_expired
FROM expired_ads_view 
GROUP BY is_active;
```

### Batch Operations
```sql
-- Get expired ad IDs for batch deactivation
SELECT ad_id 
FROM expired_ads_view 
WHERE is_active = true 
  AND expired_for > INTERVAL '7 days';

-- Get expired ads with their targeting information
SELECT 
    eav.ad_id,
    eav.expiry_date,
    eav.expired_for,
    cat.pincode,
    cat.is_global
FROM expired_ads_view eav
JOIN custom_ad_targets cat ON eav.ad_id = cat.ad_id
WHERE eav.is_active = true
ORDER BY eav.expired_for DESC;
```

## Performance Considerations

### Indexes Used
The view leverages indexes from the source table:
- **custom_ads**: Primary key (id), expiry_date, is_active, created_at

### Query Optimization
- **Pre-filtered**: View only includes expired ads, reducing result set
- **Date calculations**: Computed at query time for accuracy
- **Ordered results**: Pre-sorted by expiry_date for common use cases

### Recommended Indexes
For optimal performance, ensure these indexes exist on `custom_ads`:
```sql
-- Composite index for expired ads queries
CREATE INDEX IF NOT EXISTS idx_custom_ads_expiry_active 
ON custom_ads (expiry_date, is_active) 
WHERE expiry_date IS NOT NULL;

-- Index for cleanup queries
CREATE INDEX IF NOT EXISTS idx_custom_ads_expiry_created 
ON custom_ads (expiry_date, created_at) 
WHERE expiry_date <= now();
```

## Administrative Use Cases

### 1. Automated Cleanup
- **Daily job**: Deactivate ads expired for more than 24 hours
- **Weekly job**: Archive ads expired for more than 30 days
- **Monthly job**: Delete ads expired for more than 90 days

### 2. Monitoring and Alerts
- **Alert on expired active ads**: Notify when active ads have expired
- **Cleanup reports**: Weekly reports on expired ad counts
- **Performance tracking**: Monitor ad lifecycle and expiry patterns

### 3. Business Intelligence
- **Ad effectiveness**: Analyze which ads reach their expiry vs early termination
- **Lifecycle analysis**: Track average ad duration and expiry patterns
- **Cleanup efficiency**: Monitor how quickly expired ads are processed

## Related Views and Tables

### Related Views
- **ad_targets_view**: Shows all ads with targeting information and expiry status
- **unified_posts_view**: May reference ads in content or analytics

### Source Tables
- **custom_ads**: Primary source for ad information
- **custom_ad_targets**: Related targeting information (not directly in view)

## Best Practices

### Query Optimization
1. **Use appropriate time filters**: Filter by expired_for duration for efficiency
2. **Batch operations**: Process expired ads in batches to avoid long-running queries
3. **Index usage**: Ensure queries use the expiry_date indexes

### Administrative Workflows
1. **Regular cleanup**: Implement automated cleanup for old expired ads
2. **Grace periods**: Allow reasonable time before deactivating expired ads
3. **Audit trails**: Log cleanup operations for compliance and debugging
4. **Notification systems**: Alert advertisers before and after ad expiry

### Monitoring
1. **Track cleanup performance**: Monitor how quickly expired ads are processed
2. **Alert on accumulation**: Notify if expired ads accumulate without cleanup
3. **Performance monitoring**: Watch for slow queries during cleanup operations

This view provides essential functionality for managing the lifecycle of custom ads and maintaining a clean, efficient advertising system.
