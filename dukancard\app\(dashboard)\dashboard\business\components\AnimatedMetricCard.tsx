"use client";

import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";

interface AnimatedMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description: string;
  color: "rose" | "blue" | "amber" | "red" | "yellow" | "primary" | "emerald";
  isUpdated?: boolean;
}

export default function AnimatedMetricCard({
  title,
  value,
  icon: Icon,
  description,
  color,
  isUpdated = false,
}: AnimatedMetricCardProps) {
  // Enhanced color variants for modern SaaS design
  const colorVariants = {
    rose: {
      icon: "bg-rose-50 text-rose-600 dark:bg-rose-900/20 dark:text-rose-400 group-hover:bg-rose-100 dark:group-hover:bg-rose-900/30",
      accent: "text-rose-600 dark:text-rose-400"
    },
    blue: {
      icon: "bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30",
      accent: "text-blue-600 dark:text-blue-400"
    },
    amber: {
      icon: "bg-amber-50 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400 group-hover:bg-amber-100 dark:group-hover:bg-amber-900/30",
      accent: "text-amber-600 dark:text-amber-400"
    },
    red: {
      icon: "bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400 group-hover:bg-red-100 dark:group-hover:bg-red-900/30",
      accent: "text-red-600 dark:text-red-400"
    },
    yellow: {
      icon: "bg-yellow-50 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400 group-hover:bg-yellow-100 dark:group-hover:bg-yellow-900/30",
      accent: "text-yellow-600 dark:text-yellow-400"
    },
    primary: {
      icon: "bg-primary/10 text-primary group-hover:bg-primary/15",
      accent: "text-primary"
    },
    emerald: {
      icon: "bg-emerald-50 text-emerald-600 dark:bg-emerald-900/20 dark:text-emerald-400 group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/30",
      accent: "text-emerald-600 dark:text-emerald-400"
    }
  };

  const currentColor = colorVariants[color];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  const counterVariants = {
    initial: { scale: 1 },
    update: {
      scale: 1.05,
      transition: { duration: 0.3 },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      className="group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300"
    >
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40" />

      <div className="relative p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="space-y-1">
            <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400 tracking-wide">
              {title}
            </p>
            <div className="flex items-baseline gap-2">
              <motion.h3
                className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 tracking-tight"
                variants={counterVariants}
                initial="initial"
                animate={isUpdated ? "update" : "initial"}
              >
                {typeof value === 'string' ? value : value.toLocaleString()}
              </motion.h3>
            </div>
          </div>

          <div className={`
            flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300
            ${currentColor.icon}
          `}>
            <Icon className="w-6 h-6" />
          </div>
        </div>

        <div className="space-y-3">
          <p className="text-sm text-neutral-500 dark:text-neutral-400 leading-relaxed">
            {description}
          </p>

          {/* Update indicator */}
          {isUpdated && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="flex items-center gap-2 text-xs font-medium text-primary"
            >
              <div className="w-2 h-2 rounded-full bg-primary animate-pulse" />
              Updated
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
