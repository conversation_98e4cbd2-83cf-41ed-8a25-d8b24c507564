"use client";

import { motion } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface SubscriptionCardSkeletonProps {
  index?: number;
  variant?: 'default' | 'compact';
}

export default function SubscriptionCardSkeleton({ 
  index = 0, 
  variant = 'default' 
}: SubscriptionCardSkeletonProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.05 }}
      className={cn(
        "rounded-lg border p-0 overflow-hidden transition-all duration-300",
        "bg-white dark:bg-black border-neutral-200 dark:border-neutral-800",
        "shadow-sm",
        variant === 'compact' && "max-w-sm"
      )}
    >
      {/* Card with header image background */}
      <div className="relative">
        {/* Decorative header skeleton */}
        <Skeleton className={cn(
          "w-full",
          variant === 'compact' ? "h-16" : "h-20"
        )} />

        {/* Avatar - positioned to overlap the header */}
        <div className={cn(
          "absolute left-4",
          variant === 'compact' ? "-bottom-4" : "-bottom-6"
        )}>
          <div className="p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800">
            <Skeleton className={cn(
              "rounded-full",
              variant === 'compact' ? "h-12 w-12" : "h-16 w-16"
            )} />
          </div>
        </div>
      </div>

      {/* Card content */}
      <div className={cn(
        "px-4 pb-4",
        variant === 'compact' ? "pt-6" : "pt-8"
      )}>
        <div className="flex flex-col">
          <div className="mb-3">
            {/* Name skeleton */}
            <Skeleton className={cn(
              "mb-2",
              variant === 'compact' ? "h-5 w-32" : "h-6 w-40"
            )} />
            {/* Location skeleton */}
            <Skeleton className={cn(
              "mt-1",
              variant === 'compact' ? "h-3 w-24" : "h-4 w-32"
            )} />
          </div>

          {/* Action button skeleton */}
          <Skeleton className={cn(
            "w-full mt-2 rounded-md",
            variant === 'compact' ? "h-8" : "h-9"
          )} />
        </div>
      </div>
    </motion.div>
  );
}

export function SubscriptionListSkeleton({
  variant = 'default',
  count = 6
}: {
  variant?: 'default' | 'compact';
  count?: number;
}) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <SubscriptionCardSkeleton
          key={index}
          index={index}
          variant={variant}
        />
      ))}
    </div>
  );
}
